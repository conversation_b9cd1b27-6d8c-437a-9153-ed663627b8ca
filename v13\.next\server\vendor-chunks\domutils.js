"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domutils";
exports.ids = ["vendor-chunks/domutils"];
exports.modules = {

/***/ "(rsc)/./node_modules/domutils/lib/esm/feeds.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/esm/feeds.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFeed: () => (/* binding */ getFeed)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/esm/legacy.js\");\n\n\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n  const feedRoot = getOneElement(isValidFeed, doc);\n  return !feedRoot ? null : feedRoot.name === \"feed\" ? getAtomFeed(feedRoot) : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n  var _a;\n  const childs = feedRoot.children;\n  const feed = {\n    type: \"atom\",\n    items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"entry\", childs).map(item => {\n      var _a;\n      const {\n        children\n      } = item;\n      const entry = {\n        media: getMediaElements(children)\n      };\n      addConditionally(entry, \"id\", \"id\", children);\n      addConditionally(entry, \"title\", \"title\", children);\n      const href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n      if (href) {\n        entry.link = href;\n      }\n      const description = fetch(\"summary\", children) || fetch(\"content\", children);\n      if (description) {\n        entry.description = description;\n      }\n      const pubDate = fetch(\"updated\", children);\n      if (pubDate) {\n        entry.pubDate = new Date(pubDate);\n      }\n      return entry;\n    })\n  };\n  addConditionally(feed, \"id\", \"id\", childs);\n  addConditionally(feed, \"title\", \"title\", childs);\n  const href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n  if (href) {\n    feed.link = href;\n  }\n  addConditionally(feed, \"description\", \"subtitle\", childs);\n  const updated = fetch(\"updated\", childs);\n  if (updated) {\n    feed.updated = new Date(updated);\n  }\n  addConditionally(feed, \"author\", \"email\", childs, true);\n  return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n  var _a, _b;\n  const childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n  const feed = {\n    type: feedRoot.name.substr(0, 3),\n    id: \"\",\n    items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"item\", feedRoot.children).map(item => {\n      const {\n        children\n      } = item;\n      const entry = {\n        media: getMediaElements(children)\n      };\n      addConditionally(entry, \"id\", \"guid\", children);\n      addConditionally(entry, \"title\", \"title\", children);\n      addConditionally(entry, \"link\", \"link\", children);\n      addConditionally(entry, \"description\", \"description\", children);\n      const pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n      if (pubDate) entry.pubDate = new Date(pubDate);\n      return entry;\n    })\n  };\n  addConditionally(feed, \"title\", \"title\", childs);\n  addConditionally(feed, \"link\", \"link\", childs);\n  addConditionally(feed, \"description\", \"description\", childs);\n  const updated = fetch(\"lastBuildDate\", childs);\n  if (updated) {\n    feed.updated = new Date(updated);\n  }\n  addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n  return feed;\n}\nconst MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nconst MEDIA_KEYS_INT = [\"fileSize\", \"bitrate\", \"framerate\", \"samplingrate\", \"channels\", \"duration\", \"height\", \"width\"];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n  return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"media:content\", where).map(elem => {\n    const {\n      attribs\n    } = elem;\n    const media = {\n      medium: attribs[\"medium\"],\n      isDefault: !!attribs[\"isDefault\"]\n    };\n    for (const attrib of MEDIA_KEYS_STRING) {\n      if (attribs[attrib]) {\n        media[attrib] = attribs[attrib];\n      }\n    }\n    for (const attrib of MEDIA_KEYS_INT) {\n      if (attribs[attrib]) {\n        media[attrib] = parseInt(attribs[attrib], 10);\n      }\n    }\n    if (attribs[\"expression\"]) {\n      media.expression = attribs[\"expression\"];\n    }\n    return media;\n  });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n  return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse = false) {\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent)((0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse = false) {\n  const val = fetch(tagName, where, recurse);\n  if (val) obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n  return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/feeds.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/helpers.js":
/*!**************************************************!*\
  !*** ./node_modules/domutils/lib/esm/helpers.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* binding */ DocumentPosition),\n/* harmony export */   compareDocumentPosition: () => (/* binding */ compareDocumentPosition),\n/* harmony export */   removeSubsets: () => (/* binding */ removeSubsets),\n/* harmony export */   uniqueSort: () => (/* binding */ uniqueSort)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n  let idx = nodes.length;\n  /*\n   * Check if each node (or one of its ancestors) is already contained in the\n   * array.\n   */\n  while (--idx >= 0) {\n    const node = nodes[idx];\n    /*\n     * Remove the node if it is not unique.\n     * We are going through the array from the end, so we only\n     * have to check nodes that preceed the node under consideration in the array.\n     */\n    if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n      nodes.splice(idx, 1);\n      continue;\n    }\n    for (let ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n      if (nodes.includes(ancestor)) {\n        nodes.splice(idx, 1);\n        break;\n      }\n    }\n  }\n  return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n  DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n  DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n  DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n  DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n  DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n  const aParents = [];\n  const bParents = [];\n  if (nodeA === nodeB) {\n    return 0;\n  }\n  let current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n  while (current) {\n    aParents.unshift(current);\n    current = current.parent;\n  }\n  current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n  while (current) {\n    bParents.unshift(current);\n    current = current.parent;\n  }\n  const maxIdx = Math.min(aParents.length, bParents.length);\n  let idx = 0;\n  while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n    idx++;\n  }\n  if (idx === 0) {\n    return DocumentPosition.DISCONNECTED;\n  }\n  const sharedParent = aParents[idx - 1];\n  const siblings = sharedParent.children;\n  const aSibling = aParents[idx];\n  const bSibling = bParents[idx];\n  if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n    if (sharedParent === nodeB) {\n      return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n    }\n    return DocumentPosition.FOLLOWING;\n  }\n  if (sharedParent === nodeA) {\n    return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n  }\n  return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n  nodes = nodes.filter((node, i, arr) => !arr.includes(node, i + 1));\n  nodes.sort((a, b) => {\n    const relative = compareDocumentPosition(a, b);\n    if (relative & DocumentPosition.PRECEDING) {\n      return -1;\n    } else if (relative & DocumentPosition.FOLLOWING) {\n      return 1;\n    }\n    return 0;\n  });\n  return nodes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.DocumentPosition),\n/* harmony export */   append: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.append),\n/* harmony export */   appendChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.appendChild),\n/* harmony export */   compareDocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.compareDocumentPosition),\n/* harmony export */   existsOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.existsOne),\n/* harmony export */   filter: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.filter),\n/* harmony export */   find: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.find),\n/* harmony export */   findAll: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findAll),\n/* harmony export */   findOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOne),\n/* harmony export */   findOneChild: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOneChild),\n/* harmony export */   getAttributeValue: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getAttributeValue),\n/* harmony export */   getChildren: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getChildren),\n/* harmony export */   getElementById: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementById),\n/* harmony export */   getElements: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElements),\n/* harmony export */   getElementsByClassName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagType),\n/* harmony export */   getFeed: () => (/* reexport safe */ _feeds_js__WEBPACK_IMPORTED_MODULE_6__.getFeed),\n/* harmony export */   getInnerHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getInnerHTML),\n/* harmony export */   getName: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getName),\n/* harmony export */   getOuterHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getOuterHTML),\n/* harmony export */   getParent: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getParent),\n/* harmony export */   getSiblings: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getSiblings),\n/* harmony export */   getText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getText),\n/* harmony export */   hasAttrib: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.hasAttrib),\n/* harmony export */   hasChildren: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.hasChildren),\n/* harmony export */   innerText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.innerText),\n/* harmony export */   isCDATA: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isComment),\n/* harmony export */   isDocument: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isText),\n/* harmony export */   nextElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.nextElementSibling),\n/* harmony export */   prepend: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prepend),\n/* harmony export */   prependChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prependChild),\n/* harmony export */   prevElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.prevElementSibling),\n/* harmony export */   removeElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.removeElement),\n/* harmony export */   removeSubsets: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.removeSubsets),\n/* harmony export */   replaceElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.replaceElement),\n/* harmony export */   testElement: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.testElement),\n/* harmony export */   textContent: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent),\n/* harmony export */   uniqueSort: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.uniqueSort)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _traversal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./traversal.js */ \"(rsc)/./node_modules/domutils/lib/esm/traversal.js\");\n/* harmony import */ var _manipulation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./manipulation.js */ \"(rsc)/./node_modules/domutils/lib/esm/manipulation.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/esm/querying.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/./node_modules/domutils/lib/esm/legacy.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers.js */ \"(rsc)/./node_modules/domutils/lib/esm/helpers.js\");\n/* harmony import */ var _feeds_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./feeds.js */ \"(rsc)/./node_modules/domutils/lib/esm/feeds.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n\n\n\n\n\n/** @deprecated Use these methods from `domhandler` directly. */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNBO0FBQ0c7QUFDSjtBQUNGO0FBQ0M7QUFDRjtBQUMzQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjEzXFxub2RlX21vZHVsZXNcXGRvbXV0aWxzXFxsaWJcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vc3RyaW5naWZ5LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90cmF2ZXJzYWwuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL21hbmlwdWxhdGlvbi5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcXVlcnlpbmcuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2xlZ2FjeS5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vaGVscGVycy5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZmVlZHMuanNcIjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgdGhlc2UgbWV0aG9kcyBmcm9tIGBkb21oYW5kbGVyYCBkaXJlY3RseS4gKi9cbmV4cG9ydCB7IGlzVGFnLCBpc0NEQVRBLCBpc1RleHQsIGlzQ29tbWVudCwgaXNEb2N1bWVudCwgaGFzQ2hpbGRyZW4sIH0gZnJvbSBcImRvbWhhbmRsZXJcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6WyJpc1RhZyIsImlzQ0RBVEEiLCJpc1RleHQiLCJpc0NvbW1lbnQiLCJpc0RvY3VtZW50IiwiaGFzQ2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/legacy.js":
/*!*************************************************!*\
  !*** ./node_modules/domutils/lib/esm/legacy.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getElementById: () => (/* binding */ getElementById),\n/* harmony export */   getElements: () => (/* binding */ getElements),\n/* harmony export */   getElementsByClassName: () => (/* binding */ getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* binding */ getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* binding */ getElementsByTagType),\n/* harmony export */   testElement: () => (/* binding */ testElement)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/./node_modules/domutils/lib/esm/querying.js\");\n\n\n/**\n * A map of functions to check nodes against.\n */\nconst Checks = {\n  tag_name(name) {\n    if (typeof name === \"function\") {\n      return elem => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && name(elem.name);\n    } else if (name === \"*\") {\n      return domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag;\n    }\n    return elem => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.name === name;\n  },\n  tag_type(type) {\n    if (typeof type === \"function\") {\n      return elem => type(elem.type);\n    }\n    return elem => elem.type === type;\n  },\n  tag_contains(data) {\n    if (typeof data === \"function\") {\n      return elem => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && data(elem.data);\n    }\n    return elem => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && elem.data === data;\n  }\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n  if (typeof value === \"function\") {\n    return elem => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && value(elem.attribs[attrib]);\n  }\n  return elem => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.attribs[attrib] === value;\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n  return elem => a(elem) || b(elem);\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n  const funcs = Object.keys(options).map(key => {\n    const value = options[key];\n    return Object.prototype.hasOwnProperty.call(Checks, key) ? Checks[key](value) : getAttribCheck(key, value);\n  });\n  return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n  const test = compileTest(options);\n  return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit = Infinity) {\n  const test = compileTest(options);\n  return test ? (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse = true) {\n  if (!Array.isArray(nodes)) nodes = [nodes];\n  return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse = true, limit = Infinity) {\n  return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse = true, limit = Infinity) {\n  return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse = true, limit = Infinity) {\n  return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/manipulation.js":
/*!*******************************************************!*\
  !*** ./node_modules/domutils/lib/esm/manipulation.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendChild: () => (/* binding */ appendChild),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependChild: () => (/* binding */ prependChild),\n/* harmony export */   removeElement: () => (/* binding */ removeElement),\n/* harmony export */   replaceElement: () => (/* binding */ replaceElement)\n/* harmony export */ });\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n  if (elem.prev) elem.prev.next = elem.next;\n  if (elem.next) elem.next.prev = elem.prev;\n  if (elem.parent) {\n    const childs = elem.parent.children;\n    const childsIndex = childs.lastIndexOf(elem);\n    if (childsIndex >= 0) {\n      childs.splice(childsIndex, 1);\n    }\n  }\n  elem.next = null;\n  elem.prev = null;\n  elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n  const prev = replacement.prev = elem.prev;\n  if (prev) {\n    prev.next = replacement;\n  }\n  const next = replacement.next = elem.next;\n  if (next) {\n    next.prev = replacement;\n  }\n  const parent = replacement.parent = elem.parent;\n  if (parent) {\n    const childs = parent.children;\n    childs[childs.lastIndexOf(elem)] = replacement;\n    elem.parent = null;\n  }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n  removeElement(child);\n  child.next = null;\n  child.parent = parent;\n  if (parent.children.push(child) > 1) {\n    const sibling = parent.children[parent.children.length - 2];\n    sibling.next = child;\n    child.prev = sibling;\n  } else {\n    child.prev = null;\n  }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n  removeElement(next);\n  const {\n    parent\n  } = elem;\n  const currNext = elem.next;\n  next.next = currNext;\n  next.prev = elem;\n  elem.next = next;\n  next.parent = parent;\n  if (currNext) {\n    currNext.prev = next;\n    if (parent) {\n      const childs = parent.children;\n      childs.splice(childs.lastIndexOf(currNext), 0, next);\n    }\n  } else if (parent) {\n    parent.children.push(next);\n  }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n  removeElement(child);\n  child.parent = parent;\n  child.prev = null;\n  if (parent.children.unshift(child) !== 1) {\n    const sibling = parent.children[1];\n    sibling.prev = child;\n    child.next = sibling;\n  } else {\n    child.next = null;\n  }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n  removeElement(prev);\n  const {\n    parent\n  } = elem;\n  if (parent) {\n    const childs = parent.children;\n    childs.splice(childs.indexOf(elem), 0, prev);\n  }\n  if (elem.prev) {\n    elem.prev.next = prev;\n  }\n  prev.parent = parent;\n  prev.prev = elem.prev;\n  prev.next = elem;\n  elem.prev = prev;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/manipulation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/querying.js":
/*!***************************************************!*\
  !*** ./node_modules/domutils/lib/esm/querying.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   existsOne: () => (/* binding */ existsOne),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findAll: () => (/* binding */ findAll),\n/* harmony export */   findOne: () => (/* binding */ findOne),\n/* harmony export */   findOneChild: () => (/* binding */ findOneChild)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse = true, limit = Infinity) {\n  return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n  const result = [];\n  /** Stack of the arrays we are looking at. */\n  const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n  /** Stack of the indices within the arrays. */\n  const indexStack = [0];\n  for (;;) {\n    // First, check if the current array has any more elements to look at.\n    if (indexStack[0] >= nodeStack[0].length) {\n      // If we have no more arrays to look at, we are done.\n      if (indexStack.length === 1) {\n        return result;\n      }\n      // Otherwise, remove the current array from the stack.\n      nodeStack.shift();\n      indexStack.shift();\n      // Loop back to the start to continue with the next array.\n      continue;\n    }\n    const elem = nodeStack[0][indexStack[0]++];\n    if (test(elem)) {\n      result.push(elem);\n      if (--limit <= 0) return result;\n    }\n    if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n      /*\n       * Add the children to the stack. We are depth-first, so this is\n       * the next array we look at.\n       */\n      indexStack.unshift(0);\n      nodeStack.unshift(elem.children);\n    }\n  }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n  return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse = true) {\n  const searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n  for (let i = 0; i < searchedNodes.length; i++) {\n    const node = searchedNodes[i];\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) {\n      return node;\n    }\n    if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && node.children.length > 0) {\n      const found = findOne(test, node.children, true);\n      if (found) return found;\n    }\n  }\n  return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n  return (Array.isArray(nodes) ? nodes : [nodes]).some(node => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node) || (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && existsOne(test, node.children));\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n  const result = [];\n  const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n  const indexStack = [0];\n  for (;;) {\n    if (indexStack[0] >= nodeStack[0].length) {\n      if (nodeStack.length === 1) {\n        return result;\n      }\n      // Otherwise, remove the current array from the stack.\n      nodeStack.shift();\n      indexStack.shift();\n      // Loop back to the start to continue with the next array.\n      continue;\n    }\n    const elem = nodeStack[0][indexStack[0]++];\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && test(elem)) result.push(elem);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n      indexStack.unshift(0);\n      nodeStack.unshift(elem.children);\n    }\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/querying.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/stringify.js":
/*!****************************************************!*\
  !*** ./node_modules/domutils/lib/esm/stringify.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInnerHTML: () => (/* binding */ getInnerHTML),\n/* harmony export */   getOuterHTML: () => (/* binding */ getOuterHTML),\n/* harmony export */   getText: () => (/* binding */ getText),\n/* harmony export */   innerText: () => (/* binding */ innerText),\n/* harmony export */   textContent: () => (/* binding */ textContent)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/dom-serializer/lib/esm/index.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n\n\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n  return (0,dom_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n  return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) ? node.children.map(node => getOuterHTML(node, options)).join(\"\") : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n  if (Array.isArray(node)) return node.map(getText).join(\"\");\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node)) return node.name === \"br\" ? \"\\n\" : getText(node.children);\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node)) return getText(node.children);\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node)) return node.data;\n  return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n  if (Array.isArray(node)) return node.map(textContent).join(\"\");\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isComment)(node)) {\n    return textContent(node.children);\n  }\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node)) return node.data;\n  return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n  if (Array.isArray(node)) return node.map(innerText).join(\"\");\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && (node.type === domelementtype__WEBPACK_IMPORTED_MODULE_2__.ElementType.Tag || (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))) {\n    return innerText(node.children);\n  }\n  if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node)) return node.data;\n  return \"\";\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domutils/lib/esm/traversal.js":
/*!****************************************************!*\
  !*** ./node_modules/domutils/lib/esm/traversal.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributeValue: () => (/* binding */ getAttributeValue),\n/* harmony export */   getChildren: () => (/* binding */ getChildren),\n/* harmony export */   getName: () => (/* binding */ getName),\n/* harmony export */   getParent: () => (/* binding */ getParent),\n/* harmony export */   getSiblings: () => (/* binding */ getSiblings),\n/* harmony export */   hasAttrib: () => (/* binding */ hasAttrib),\n/* harmony export */   nextElementSibling: () => (/* binding */ nextElementSibling),\n/* harmony export */   prevElementSibling: () => (/* binding */ prevElementSibling)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n  return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n  return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n  const parent = getParent(elem);\n  if (parent != null) return getChildren(parent);\n  const siblings = [elem];\n  let {\n    prev,\n    next\n  } = elem;\n  while (prev != null) {\n    siblings.unshift(prev);\n    ({\n      prev\n    } = prev);\n  }\n  while (next != null) {\n    siblings.push(next);\n    ({\n      next\n    } = next);\n  }\n  return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n  var _a;\n  return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n  return elem.attribs != null && Object.prototype.hasOwnProperty.call(elem.attribs, name) && elem.attribs[name] != null;\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n  return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n  let {\n    next\n  } = elem;\n  while (next !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(next)) ({\n    next\n  } = next);\n  return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n  let {\n    prev\n  } = elem;\n  while (prev !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(prev)) ({\n    prev\n  } = prev);\n  return prev;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domutils/lib/esm/traversal.js\n");

/***/ })

};
;