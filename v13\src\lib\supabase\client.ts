import { createBrowserClient } from '@supabase/ssr';

// Cliente para el navegador (componentes del cliente)
export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,       // Persistir sesión en el navegador
        autoRefreshToken: true,     // Refrescar token automáticamente
        detectSessionInUrl: true    // ESENCIAL: Detectar y procesar tokens de URL
      }
    }
  );
}

// Mantener compatibilidad con código existente
export const supabase = createClient();
