/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/_not-found/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_not-found/page\",\n      function () {\n        return __webpack_require__(/*! ./node_modules/next/dist/client/components/not-found-error.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_not-found/page\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUMlM0ElNUNVc2VycyU1Q25hYXRhJTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1Q09wb3NJJTVDdjEzJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWVycm9yLmpzJnBhZ2U9JTJGX25vdC1mb3VuZCUyRnBhZ2UhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsd0pBQStEO0FBQ3RGO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL19ub3QtZm91bmQvcGFnZVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3IuanNcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL19ub3QtZm91bmQvcGFnZVwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessErrorFallback\", ({\n    enumerable: true,\n    get: function get() {\n        return HTTPAccessErrorFallback;\n    }\n}));\nvar _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nvar _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nvar _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nvar styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    desc: {\n        display: 'inline-block'\n    },\n    h1: {\n        display: 'inline-block',\n        margin: '0 20px 0 0',\n        padding: '0 23px 0 0',\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: 'top',\n        lineHeight: '49px'\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: '49px',\n        margin: 0\n    }\n};\nfunction HTTPAccessErrorFallback(param) {\n    var status = param.status, message = param.message;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                children: status + \": \" + message\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                style: styles.error,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            dangerouslySetInnerHTML: {\n                                /* Minified CSS from\n            body { margin: 0; color: #000; background: #fff; }\n            .next-error-h1 {\n            border-right: 1px solid rgba(0, 0, 0, .3);\n            }\n            @media (prefers-color-scheme: dark) {\n            body { color: #fff; background: #000; }\n            .next-error-h1 {\n            border-right: 1px solid rgba(255, 255, 255, .3);\n            }\n            }\n            */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                            className: \"next-error-h1\",\n                            style: styles.h1,\n                            children: status\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            style: styles.desc,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h2\", {\n                                style: styles.h2,\n                                children: message\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c1 = HTTPAccessErrorFallback;\n_c = HTTPAccessErrorFallback;\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n    Object.defineProperty(exports[\"default\"], '__esModule', {\n        value: true\n    });\n    Object.assign(exports[\"default\"], exports);\n    module.exports = exports[\"default\"];\n}\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessErrorFallback\");\nvar _c1;\n$RefreshReg$(_c1, \"HTTPAccessErrorFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/not-found-error.js ***!
  \*********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function get() {\n        return NotFound;\n    }\n}));\nvar _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nvar _errorfallback = __webpack_require__(/*! ./http-access-fallback/error-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js\");\nfunction NotFound() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorfallback.HTTPAccessErrorFallback, {\n        status: 404,\n        message: \"This page could not be found.\"\n    });\n}\n_c1 = NotFound;\n_c = NotFound;\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n    Object.defineProperty(exports[\"default\"], '__esModule', {\n        value: true\n    });\n    Object.assign(exports[\"default\"], exports);\n    module.exports = exports[\"default\"];\n}\nvar _c;\n$RefreshReg$(_c, \"NotFound\");\nvar _c1;\n$RefreshReg$(_c1, \"NotFound\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);