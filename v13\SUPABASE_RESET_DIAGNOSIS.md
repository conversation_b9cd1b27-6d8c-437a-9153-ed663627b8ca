# 🔍 Diagnós<PERSON>o Completo: Reset Password Supabase

## ❌ PROBLEMA PRINCIPAL IDENTIFICADO

### **🚨 Rate Limit de Emails Muy Bajo**
```
rate_limit_email_sent: 2 (DEMASIADO BAJO)
```

**Esto significa que solo puedes enviar 2 emails de recuperación por hora.**

## 🔧 SOLUCIONES REQUERIDAS

### **1. ⚡ URGENTE: Aumentar Rate Limit de Emails**

**Ir a Supabase Dashboard:**
1. Authentication → Settings
2. Buscar "Email rate limits" 
3. Cambiar `Rate limit for email sent` de **2** a **10**
4. Guardar cambios

### **2. ✅ Verificar URL Configuration**

**Authentication → URL Configuration:**
```
Site URL: http://localhost:3000

Additional Redirect URLs:
http://localhost:3000/auth/reset-password
http://localhost:3000/auth/callback
http://localhost:3000
```

### **3. ✅ Verificar Email Template**

**Authentication → Email Templates → Reset Password:**

**Subject:** `Restablecer tu Contraseña - OposI`

**Template debe contener:** `{{ .ConfirmationURL }}`

## 🧪 PASOS DE PRUEBA DESPUÉS DE CORREGIR

### **Paso 1: Limpiar Rate Limit**
- Esperar 1 hora desde el último email enviado
- O cambiar el rate limit primero

### **Paso 2: Prueba Completa**
1. Ir a `/profile` → Configuración
2. Solicitar cambio de contraseña
3. Verificar que llega el email
4. Hacer clic INMEDIATAMENTE en el enlace
5. Verificar que carga el formulario (no error)

## 📊 CONFIGURACIÓN ACTUAL VERIFICADA

✅ **Site URL:** `http://localhost:3000`
✅ **URI Allow List:** Configurada correctamente
✅ **OTP Expiration:** 3600 segundos (1 hora)
✅ **Email Template:** Contiene `{{ .ConfirmationURL }}`
❌ **Rate Limit:** 2 emails/hora (DEMASIADO BAJO)

## 🔍 LOGS ESPERADOS DESPUÉS DE LA CORRECCIÓN

### **Servidor (Middleware):**
```
🚀 [MIDDLEWARE START] Path: /auth/reset-password#access_token=...&type=recovery
[MW_LOG 6] Path is /auth/reset-password. User detected (possibly recovery session): [user-id]. Allowing passthrough.
```

### **Cliente (Navegador):**
```
🔐 [ResetPassword] Component mounted, checking URL: http://localhost:3000/auth/reset-password#access_token=...
🔐 [ResetPassword] getSession result: { hasSession: true, userId: "[user-id]" }
```

## ⚠️ SI SIGUE FALLANDO DESPUÉS DE CORREGIR RATE LIMIT

1. **Verificar en Network Tab** si el email se está enviando
2. **Revisar spam/junk** en el email
3. **Probar con otro email** para descartar problemas específicos
4. **Verificar logs del servidor** durante el envío

## 🎯 CAUSA RAÍZ

El rate limit de 2 emails/hora es extremadamente restrictivo para desarrollo y testing. Durante las pruebas es normal enviar múltiples emails, y este límite se alcanza rápidamente, causando que Supabase rechace silenciosamente las solicitudes de envío.
