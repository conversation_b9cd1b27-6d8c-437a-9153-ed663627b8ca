-- Migración para agregar stripe_subscription_id a user_profiles
-- Ejecutar en Supabase SQL Editor

-- Agregar columna stripe_subscription_id a user_profiles
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;

-- Agregar comentario para documentar la columna
COMMENT ON COLUMN user_profiles.stripe_subscription_id IS 'ID de suscripción de Stripe para usuarios con planes de pago recurrentes';

-- Crear índice para mejorar rendimiento en consultas por subscription_id
CREATE INDEX IF NOT EXISTS idx_user_profiles_stripe_subscription_id 
ON user_profiles(stripe_subscription_id) 
WHERE stripe_subscription_id IS NOT NULL;
