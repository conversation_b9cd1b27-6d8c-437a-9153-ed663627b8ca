// Test para verificar la función atómica create_user_profile_and_history
// Este archivo será eliminado después de las pruebas

import { supabaseAdmin } from './src/lib/supabase/admin.js';
import { getPlanConfiguration, getTokenLimitForPlan } from './src/lib/utils/planLimits.js';

async function testAtomicFunction() {
  console.log('🧪 Iniciando tests de función atómica...\n');

  // Test 1: Crear un usuario real y probar la función atómica
  console.log('Test 1: Función atómica con usuario real');
  try {
    // Crear un usuario real primero
    const testEmail = `atomic-test-${Date.now()}@example.com`;
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.inviteUserByEmail(
      testEmail,
      {
        data: { name: 'Test Atomic User' },
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`
      }
    );

    if (userError) {
      throw new Error(`Error creando usuario: ${userError.message}`);
    }

    const userId = userData.user.id;
    console.log(`✅ Usuario creado: ${userId}`);

    // Preparar datos para la función atómica
    const planConfig = getPlanConfiguration('free');
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 5);

    const profileData = {
      subscription_plan: 'free',
      monthly_token_limit: getTokenLimitForPlan('free'),
      current_month_tokens: 0,
      current_month: currentMonth,
      payment_verified: true,
      stripe_customer_id: null,
      stripe_subscription_id: null,
      last_payment_date: null,
      auto_renew: false,
      plan_expires_at: expiresAt.toISOString(),
      plan_features: planConfig.features,
      security_flags: {
        test_atomic_function: true,
        created_at: new Date().toISOString()
      }
    };

    const transactionId = crypto.randomUUID();

    // Ejecutar función atómica
    const { data: result, error: rpcError } = await supabaseAdmin
      .rpc('create_user_profile_and_history', {
        p_user_id: userId,
        p_transaction_id: transactionId,
        p_profile_data: profileData
      })
      .single();

    if (rpcError) {
      throw new Error(`Error en función RPC: ${rpcError.message}`);
    }

    console.log('✅ Test 1 PASÓ: Función atómica ejecutada exitosamente');
    console.log(`   - Profile ID: ${result.created_profile_id}`);
    console.log(`   - History ID: ${result.created_history_id}`);

    // Verificar que el perfil fue creado
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('id', result.created_profile_id)
      .single();

    if (profileError) {
      throw new Error(`Error verificando perfil: ${profileError.message}`);
    }

    console.log('✅ Perfil verificado en base de datos');
    console.log(`   - Plan: ${profile.subscription_plan}`);
    console.log(`   - Tokens: ${profile.monthly_token_limit}`);

    // Verificar que el historial fue creado
    const { data: history, error: historyError } = await supabaseAdmin
      .from('user_plan_history')
      .select('*')
      .eq('id', result.created_history_id)
      .single();

    if (historyError) {
      throw new Error(`Error verificando historial: ${historyError.message}`);
    }

    console.log('✅ Historial verificado en base de datos');
    console.log(`   - Old plan: ${history.old_plan}`);
    console.log(`   - New plan: ${history.new_plan}`);
    console.log(`   - Reason: ${history.reason}\n`);

  } catch (error) {
    console.log('❌ Test 1 ERROR:', error.message, '\n');
  }

  // Test 2: Probar actualización de perfil existente (upsert)
  console.log('Test 2: Upsert de perfil existente');
  try {
    // Crear otro usuario
    const testEmail2 = `atomic-upsert-${Date.now()}@example.com`;
    const { data: userData2, error: userError2 } = await supabaseAdmin.auth.admin.inviteUserByEmail(
      testEmail2,
      {
        data: { name: 'Test Upsert User' },
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`
      }
    );

    if (userError2) {
      throw new Error(`Error creando usuario 2: ${userError2.message}`);
    }

    const userId2 = userData2.user.id;
    console.log(`✅ Usuario 2 creado: ${userId2}`);

    // Primera llamada - crear perfil
    const profileData1 = {
      subscription_plan: 'free',
      monthly_token_limit: 50000,
      current_month_tokens: 0,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      payment_verified: true,
      stripe_customer_id: null,
      stripe_subscription_id: null,
      last_payment_date: null,
      auto_renew: false,
      plan_expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      plan_features: {},
      security_flags: { first_call: true }
    };

    const { data: result1 } = await supabaseAdmin
      .rpc('create_user_profile_and_history', {
        p_user_id: userId2,
        p_transaction_id: crypto.randomUUID(),
        p_profile_data: profileData1
      })
      .single();

    console.log('✅ Primera llamada exitosa (crear)');

    // Segunda llamada - actualizar perfil
    const profileData2 = {
      subscription_plan: 'usuario',
      monthly_token_limit: 200000,
      current_month_tokens: 1000,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      payment_verified: true,
      stripe_customer_id: 'cus_test123',
      stripe_subscription_id: 'sub_test123',
      last_payment_date: new Date().toISOString(),
      auto_renew: true,
      plan_expires_at: null,
      plan_features: { premium: true },
      security_flags: { second_call: true, upgraded: true }
    };

    const { data: result2 } = await supabaseAdmin
      .rpc('create_user_profile_and_history', {
        p_user_id: userId2,
        p_transaction_id: crypto.randomUUID(),
        p_profile_data: profileData2
      })
      .single();

    console.log('✅ Test 2 PASÓ: Upsert funcionó correctamente');
    console.log(`   - Mismo Profile ID: ${result1.created_profile_id === result2.created_profile_id}`);
    console.log(`   - Nuevo History ID: ${result2.created_history_id}\n`);

  } catch (error) {
    console.log('❌ Test 2 ERROR:', error.message, '\n');
  }

  console.log('🏁 Tests de función atómica completados');
}

// Ejecutar tests
testAtomicFunction().catch(console.error);
