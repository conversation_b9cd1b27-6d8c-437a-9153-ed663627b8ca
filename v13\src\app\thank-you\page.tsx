// src/app/thank-you/page.tsx
'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { getPlanById } from '@/lib/stripe/plans';
import Link from 'next/link';

type UserStatus = 'checking' | 'ready' | 'error' | 'timeout' | 'webhook_failed' | 'manual_pending';

interface AccountInfo {
  id: string;
  email: string;
  plan: string;
  paymentVerified: boolean;
  hasTemporaryPassword: boolean;
  loginUrl: string;
}

function ThankYouContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const planId = searchParams.get('plan') || 'free';
  const sessionId = searchParams.get('session_id');
  const isManual = searchParams.get('manual') === 'true';

  const [userStatus, setUserStatus] = useState<UserStatus>('checking');
  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [isReactivating, setIsReactivating] = useState(false);

  const plan = getPlanById(planId);

  // Verificación de estado del usuario
  useEffect(() => {
    if (!sessionId) {
      setUserStatus('error');
      return;
    }

    const checkAccountStatus = async () => {
      try {
        // Para plan gratuito, usar lógica diferente
        if (planId === 'free') {
          if (isManual) {
            // Modo manual: mostrar mensaje de espera
            setUserStatus('manual_pending');
            return;
          }

          // Para cuentas gratuitas automatizadas, asumir que están listas inmediatamente
          setUserStatus('ready');
          setAccountInfo({
            id: sessionId,
            email: '<EMAIL>', // Se actualizará en welcome
            plan: 'free',
            paymentVerified: true,
            hasTemporaryPassword: true,
            loginUrl: '/login'
          });

          // Redirección automática tras 2 segundos para plan gratuito
          setTimeout(() => {
            router.push(`/welcome?plan=${planId}&new_user=true`);
          }, 2000);
          return;
        }

        // Para planes de pago, usar la lógica existente
        const response = await fetch(`/api/user/status?session_id=${sessionId}`);
        const data = await response.json();

        if (data.ready) {
          setUserStatus('ready');
          setAccountInfo(data.user);

          // Redirección automática tras 3 segundos
          setTimeout(() => {
            router.push(`/welcome?plan=${planId}&new_user=true`);
          }, 3000);
        } else if (timeElapsed >= 90) {
          // Timeout inteligente para detectar webhook fallido
          setUserStatus('webhook_failed');
        } else if (timeElapsed >= 60) {
          setUserStatus('timeout');
        }
      } catch (error) {
        console.error('Error verificando estado:', error);
        if (timeElapsed >= 30) {
          setUserStatus('error');
        }
      }
    };

    // Verificación inicial inmediata
    checkAccountStatus();

    // Para plan gratuito, no necesitamos polling
    if (planId === 'free') {
      return;
    }

    // Polling cada 3 segundos solo para planes de pago
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 3);
      if (userStatus === 'checking') {
        checkAccountStatus();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [sessionId, userStatus, timeElapsed, planId, router]);

  // Función para reactivar cuenta
  const handleReactivateAccount = async () => {
    setIsReactivating(true);
    try {
      const response = await fetch('/api/user/reactivate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      if (response.ok) {
        setUserStatus('checking');
        setTimeElapsed(0);
      } else {
        console.error('Error reactivando cuenta');
      }
    } catch (error) {
      console.error('Error reactivando cuenta:', error);
    } finally {
      setIsReactivating(false);
    }
  };

  // Estado: Verificando
  if (userStatus === 'checking') {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>

              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
                {planId === 'free' ? '¡Cuenta Gratuita Creada!' : '¡Pago Confirmado!'}
              </h2>

              <p className="text-lg text-gray-700 mb-6">
                {planId === 'free'
                  ? `Tu cuenta gratuita de <strong>${plan?.name}</strong> ha sido creada exitosamente`
                  : `Tu cuenta para el <strong>${plan?.name}</strong> se está creando automáticamente`
                }
              </p>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <p className="text-blue-800 text-sm">
                  {planId === 'free' ? (
                    <>
                      <strong>🎉 ¡Listo para empezar!</strong><br/>
                      Tu cuenta gratuita está activa por 5 días. Puedes comenzar a usar OposiAI inmediatamente.
                    </>
                  ) : (
                    <>
                      <strong>🤖 Proceso automático en curso</strong><br/>
                      No necesitas hacer nada más. Tu cuenta estará lista en unos segundos.
                    </>
                  )}
                </p>
              </div>

              <p className="text-sm text-gray-600">
                Tiempo transcurrido: {timeElapsed} segundos
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Estado: Listo
  if (userStatus === 'ready' && accountInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>

              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
                ¡Cuenta Creada Exitosamente!
              </h2>

              <p className="text-lg text-gray-700 mb-6">
                Tu cuenta está lista para usar
              </p>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-green-800 mb-2">¡Tu cuenta está lista!</h3>
                <p className="text-green-700 text-sm mb-3">
                  <strong>Email de acceso:</strong> {accountInfo.email}
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <p className="text-blue-800 text-xs">
                    <strong>📧 Revisa tu email</strong><br/>
                    Te hemos enviado un enlace para establecer tu contraseña.
                    Revisa tu bandeja de entrada (y spam) para completar la configuración de tu cuenta.
                  </p>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Redirigiendo automáticamente en 3 segundos...
              </p>

              <Link
                href="/welcome"
                className="inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Acceder Ahora
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Estado: Manual Pending (plan gratuito en modo manual)
  if (userStatus === 'manual_pending') {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-orange-100 mb-4">
                <svg className="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>

              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
                ¡Solicitud Recibida!
              </h2>

              <p className="text-lg text-gray-700 mb-6">
                Tu solicitud para el <strong>{plan?.name}</strong> ha sido enviada correctamente
              </p>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-orange-800 mb-2">⏳ Activación Manual</h3>
                <p className="text-orange-700 text-sm mb-3">
                  Tu cuenta será activada manualmente por nuestro equipo en las próximas horas.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <p className="text-blue-800 text-xs">
                    <strong>📧 Te notificaremos por email</strong><br/>
                    Recibirás un email con las instrucciones de acceso una vez que tu cuenta esté lista.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-2">🎯 ¿Qué incluye tu plan gratuito?</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>📄 Hasta 1 documento de estudio</li>
                  <li>❓ Hasta 10 preguntas de test</li>
                  <li>🃏 Hasta 10 flashcards</li>
                  <li>🗺️ Hasta 2 mapas mentales</li>
                  <li>🤖 50,000 tokens de IA</li>
                  <li>⏰ 5 días de acceso completo</li>
                </ul>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Tiempo estimado de activación: 2-6 horas
              </p>

              <div className="space-y-3">
                <Link
                  href="/"
                  className="inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  Volver al Inicio
                </Link>

                <p className="text-xs text-gray-500">
                  ¿Necesitas ayuda? Contacta con nosotros en{' '}
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Estados: Error, Timeout, Webhook Fallido
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
        <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 mb-4">
              {userStatus === 'webhook_failed' ? 'Reactivación Necesaria' :
               userStatus === 'timeout' ? 'Procesando...' : 'Problema Temporal'}
            </h2>

            <p className="text-gray-700 mb-6">
              {userStatus === 'webhook_failed'
                ? 'Tu pago fue procesado, pero necesitamos reactivar la creación de tu cuenta.'
                : userStatus === 'timeout'
                  ? 'Tu cuenta se está creando. Esto puede tardar un poco más de lo normal.'
                  : 'Hubo un problema al verificar tu cuenta.'
              }
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <p className="text-blue-800 text-sm">
                <strong>Tu pago fue procesado correctamente.</strong><br/>
                {userStatus === 'webhook_failed'
                  ? 'Haz clic en "Reactivar Cuenta" para completar el proceso.'
                  : 'Si el problema persiste, contacta con soporte.'
                }
              </p>
            </div>

            <div className="space-y-3">
              {userStatus === 'webhook_failed' && (
                <button
                  onClick={handleReactivateAccount}
                  disabled={isReactivating}
                  className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {isReactivating ? 'Reactivando...' : 'Reactivar Cuenta'}
                </button>
              )}

              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Verificar Nuevamente
              </button>

              <a
                href={`mailto:<EMAIL>?subject=Problema con cuenta&body=ID de Sesión: ${sessionId}`}
                className="block w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors text-center"
              >
                Contactar Soporte
              </a>

              {sessionId && (
                <div className="mt-4 text-xs text-gray-500">
                  ID de Sesión: {sessionId}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ThankYouPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Cargando...</p>
            </div>
          </div>
        </div>
      </div>
    }>
      <ThankYouContent />
    </Suspense>
  );
}
