# Test Plan: Registro Gratuito Automático

## Problemas Identificados y Soluciones Implementadas

### 1. **❌ PROBLEMA PRINCIPAL: Endpoint no accesible**
**Problema**: `/api/auth/register-free` no estaba en rutas públicas del middleware

**Solución**: ✅ Añadido endpoint a `ROUTE_PERMISSIONS.public` en middleware.ts

### 2. **❌ PROBLEMA: Redirección prematura a login**
**Problema**: Usuarios con tokens de invitación eran redirigidos a login antes de poder configurar contraseña

**Solución**: ✅ Hecho `/auth/reset-password` completamente público en middleware

### 3. **Middleware no reconocía usuarios legítimos**
**Problema**: La función `isLegitimateUser()` no reconocía usuarios con `created_via: 'free_registration'`

**Solución**: ✅ Actualizada la lógica para reconocer:
- `userMetadata.created_via === 'free_registration'`
- `userMetadata.free_account === true`
- `userMetadata.plan` (usuarios en proceso de configuración)
- `userMetadata.stripe_session_id` o `userMetadata.stripe_customer_id`

### 4. **Falta de flags específicos para cuentas gratuitas**
**Problema**: Los usuarios gratuitos no tenían identificadores claros

**Solución**: ✅ Añadidos flags adicionales en `FreeAccountService`:
- `legitimate_user: true`
- `registration_type: 'automatic_free'`
- `registration_timestamp`

### 5. **Callback no manejaba PASSWORD_RECOVERY correctamente**
**Problema**: Usuarios nuevos no eran redirigidos a configurar contraseña

**Solución**: ✅ Mejorado el callback para:
- Detectar usuarios que necesitan configurar contraseña
- Redirigir a `/auth/reset-password` cuando sea necesario
- Manejar cuentas gratuitas vs de pago diferentemente

### 6. **Redirección incorrecta después de configurar contraseña**
**Problema**: Usuarios gratuitos eran redirigidos a login en lugar de a la app

**Solución**: ✅ Modificada lógica de redirección en reset-password:
- Usuarios gratuitos → `/app`
- Usuarios de pago → `/welcome`

## Plan de Pruebas

### Paso 1: Registro Gratuito
1. Ir a la página de registro gratuito
2. Completar el formulario con email válido
3. Verificar que se crea el usuario con los flags correctos
4. Verificar que se envía el email de invitación

### Paso 2: Activación de Cuenta
1. Hacer clic en el enlace del email
2. Verificar redirección a `/auth/callback`
3. Verificar que el middleware permite el acceso
4. Verificar redirección a `/auth/reset-password`

### Paso 3: Configuración de Contraseña
1. Establecer contraseña en `/auth/reset-password`
2. Verificar que se completa la configuración
3. Verificar redirección a `/app`

### Paso 4: Acceso a la Aplicación
1. Verificar que el middleware permite acceso a `/app`
2. Verificar que se crea el perfil de usuario si no existe
3. Verificar que las funcionalidades gratuitas están disponibles

## Flujo Corregido Final:

1. **Usuario completa formulario** → `/api/auth/register-free` (✅ ahora accesible)
2. **Se crea usuario con flags** → `created_via: 'free_registration'`, `legitimate_user: true`
3. **Email de invitación enviado** → Usuario hace clic en enlace
4. **Acceso directo a reset-password** → `/auth/reset-password` (✅ completamente público)
5. **Usuario configura contraseña** → Página maneja tokens automáticamente
6. **Redirección inteligente** → Usuarios gratuitos van a `/app`, usuarios de pago a `/welcome`
7. **Middleware reconoce usuario** → Como legítimo por los flags añadidos
8. **Acceso a aplicación** → Usuario puede usar todas las funciones gratuitas

## Comandos de Prueba

```bash
# Iniciar el servidor de desarrollo
npm run dev

# En otra terminal, verificar logs del middleware
tail -f .next/server.log

# Probar registro con curl (opcional)
curl -X POST http://localhost:3000/api/auth/register-free \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","customerName":"Test User"}'
```

## Verificaciones Específicas

### En el Middleware
- [ ] Usuario con `created_via: 'free_registration'` es reconocido como legítimo
- [ ] Rutas de auth permiten acceso a usuarios legítimos
- [ ] Se crea perfil de recuperación cuando es necesario
- [ ] Logs muestran el proceso de validación

### En el Callback
- [ ] Detecta usuarios que necesitan configurar contraseña
- [ ] Redirige correctamente según el tipo de cuenta
- [ ] Maneja errores de autenticación apropiadamente

### En FreeAccountService
- [ ] Crea usuarios con todos los flags necesarios
- [ ] Envía email de invitación correctamente
- [ ] Maneja errores de email duplicado

## Posibles Problemas Restantes

1. **Timing Issues**: Puede haber una ventana entre creación de usuario y perfil
2. **Email Confirmation**: Verificar que `email_confirmed_at` se establece correctamente
3. **Session State**: Asegurar que la sesión se establece apropiadamente

## Rollback Plan

Si hay problemas, se pueden revertir los cambios:
1. Restaurar `middleware.ts` a la versión anterior
2. Restaurar `freeAccountService.ts`
3. Restaurar `auth/callback/page.tsx`
4. Verificar que el flujo anterior funciona
