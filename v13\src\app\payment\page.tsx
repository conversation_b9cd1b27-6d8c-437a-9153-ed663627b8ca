// src/app/payment/page.tsx
'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { PLANS, getPlanById } from '@/lib/stripe/plans';
import toast from 'react-hot-toast';

function PaymentContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const planId = searchParams.get('plan') || 'free';
  
  const [email, setEmail] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const plan = getPlanById(planId);

  useEffect(() => {
    if (!plan) {
      router.push('/');
    }
  }, [plan, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error('Por favor, ingresa tu email');
      return;
    }

    setIsLoading(true);

    try {
      // Para el plan gratuito, intentar el nuevo endpoint automatizado con fallback
      if (planId === 'free') {
        try {
          // Intentar el nuevo endpoint automatizado
          const registerResponse = await fetch('/api/auth/register-free', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email,
              customerName,
            }),
          });

          // Si el endpoint existe y responde correctamente
          if (registerResponse.status !== 404) {
            const registerData = await registerResponse.json();

            if (registerResponse.ok && registerData.success) {
              // Redirigir con session ID simulado para compatibilidad
              router.push(`/thank-you?plan=${planId}&session_id=${registerData.data.sessionId}`);
              return;
            } else {
              if (registerData.code === 'EMAIL_EXISTS') {
                toast.error('Ya existe una cuenta con este email. Intenta iniciar sesión.');
              } else if (registerResponse.status === 429) {
                toast.error(`Demasiados intentos. Inténtalo en ${Math.ceil(registerData.retryAfter / 60)} minutos.`);
              } else {
                toast.error(registerData.error || 'Error al crear la cuenta gratuita');
              }
              return;
            }
          }
        } catch (error) {
          console.log('Endpoint automatizado no disponible, usando fallback manual');
        }

        // Fallback: usar el sistema manual existente
        console.log('🔄 Usando sistema manual para plan gratuito (fallback)');
        const notifyResponse = await fetch('/api/notify-signup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'subscription_request',
            email,
            customerName,
            planName: plan?.name
          }),
        });

        if (notifyResponse.ok) {
          router.push(`/thank-you?plan=${planId}&manual=true`);
        } else {
          toast.error('Error al procesar la solicitud');
        }
        return;
      }

      // Para planes de pago, crear sesión de Stripe
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          email,
          customerName,
        }),
      });

      const data = await response.json();

      if (response.ok && data.url) {
        // Enviar notificación antes de redirigir a Stripe
        await fetch('/api/notify-signup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'subscription_request',
            email,
            customerName,
            planName: plan?.name
          }),
        });

        // Redirigir a Stripe Checkout
        window.location.href = data.url;
      } else {
        toast.error(data.error || 'Error al crear la sesión de pago');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al procesar el pago');
    } finally {
      setIsLoading(false);
    }
  };

  if (!plan) {
    return <div>Cargando...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            {plan.name}
          </h1>
          <p className="text-2xl font-semibold text-blue-600 mt-2">
            {plan.price === 0 ? 'Gratis' : `€${(plan.price / 100).toFixed(2)}`}
            {planId === 'pro' && <span className="text-sm text-gray-500">/mes</span>}
          </p>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Características incluidas:
            </h3>
            <ul className="space-y-2">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center text-sm text-gray-600">
                  <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  {feature}
                </li>
              ))}
            </ul>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email *
              </label>
              <input
                type="email"
                id="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-gray-700">
                Nombre (opcional)
              </label>
              <input
                type="text"
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Tu nombre"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Procesando...' : 
               planId === 'free' ? 'Solicitar Acceso Gratuito' : 'Proceder al Pago'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function PaymentPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Cargando...</p>
            </div>
          </div>
        </div>
      </div>
    }>
      <PaymentContent />
    </Suspense>
  );
}
