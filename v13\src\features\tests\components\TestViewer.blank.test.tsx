import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TestViewer from './TestViewer';
import { obtenerTests, obtenerPreguntasTestCount, obtenerPreguntasPorTestId, obtenerEstadisticasTest, obtenerEstadisticasGeneralesTests, registrarRespuestaTest } from '@/lib/supabase/testsService';

// Mock the entire module
jest.mock('@/lib/supabase/testsService', () => ({
  obtenerTests: jest.fn(),
  obtenerPreguntasPorTestId: jest.fn(),
  obtenerPreguntasTestCount: jest.fn(),
  registrarRespuestaTest: jest.fn(),
  obtenerEstadisticasGeneralesTests: jest.fn(),
  obtenerEstadisticasTest: jest.fn(),
}));

const mockTests = [
  {
    id: 'test-1',
    titulo: 'Test de Prueba',
    descripcion: 'Test para probar funcionalidad en blanco',
    created_at: '2024-01-01',
    user_id: 'user-1'
  }
];

const mockPreguntas = [
  {
    id: 'pregunta-1',
    pregunta: '¿Cuál es la capital de España?',
    opcion_a: 'Madrid',
    opcion_b: 'Barcelona',
    opcion_c: 'Valencia',
    opcion_d: 'Sevilla',
    respuesta_correcta: 'a',
    test_id: 'test-1'
  },
  {
    id: 'pregunta-2',
    pregunta: '¿En qué año se descubrió América?',
    opcion_a: '1490',
    opcion_b: '1491',
    opcion_c: '1492',
    opcion_d: '1493',
    respuesta_correcta: 'c',
    test_id: 'test-1'
  }
];

describe('TestViewer - Funcionalidad en Blanco', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (obtenerTests as jest.Mock).mockResolvedValue(mockTests);
    (obtenerPreguntasPorTestId as jest.Mock).mockResolvedValue(mockPreguntas);
    (obtenerPreguntasTestCount as jest.Mock).mockResolvedValue(2);
    (registrarRespuestaTest as jest.Mock).mockResolvedValue(true);
    (obtenerEstadisticasGeneralesTests as jest.Mock).mockResolvedValue({
      totalTests: 1,
      totalPreguntas: 2,
      promedioAciertos: 50
    });
    (obtenerEstadisticasTest as jest.Mock).mockResolvedValue({
      totalIntentos: 1,
      mejorPuntuacion: 50,
      ultimoIntento: '2024-01-01'
    });
  });

  test('debe mostrar la opción de dejar en blanco', async () => {
    render(<TestViewer />);
    
    // Seleccionar un test
    await waitFor(() => {
      expect(screen.getByText('Test de Prueba')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Test de Prueba'));
    
    // Esperar a que se carguen las preguntas
    await waitFor(() => {
      expect(screen.getByText('¿Cuál es la capital de España?')).toBeInTheDocument();
    });
    
    // Verificar que existe la opción de dejar en blanco
    expect(screen.getByText('Marque si quiere dejar en blanco')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
  });

  test('debe permitir seleccionar la opción en blanco', async () => {
    const user = userEvent.setup();
    render(<TestViewer />);
    
    // Seleccionar un test
    await waitFor(() => {
      expect(screen.getByText('Test de Prueba')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Test de Prueba'));
    
    // Esperar a que se carguen las preguntas
    await waitFor(() => {
      expect(screen.getByText('¿Cuál es la capital de España?')).toBeInTheDocument();
    });
    
    // Seleccionar la opción en blanco
    const checkbox = screen.getByRole('checkbox');
    await user.click(checkbox);
    
    // Verificar que el checkbox está marcado
    expect(checkbox).toBeChecked();
  });

  test('debe permitir avanzar con respuesta en blanco', async () => {
    const user = userEvent.setup();
    render(<TestViewer />);
    
    // Seleccionar un test
    await waitFor(() => {
      expect(screen.getByText('Test de Prueba')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Test de Prueba'));
    
    // Esperar a que se carguen las preguntas
    await waitFor(() => {
      expect(screen.getByText('¿Cuál es la capital de España?')).toBeInTheDocument();
    });
    
    // Seleccionar la opción en blanco
    const checkbox = screen.getByRole('checkbox');
    await user.click(checkbox);
    
    // Hacer clic en siguiente
    const siguienteButton = screen.getByText('Siguiente');
    await user.click(siguienteButton);
    
    // Verificar que avanzó a la siguiente pregunta
    await waitFor(() => {
      expect(screen.getByText('¿En qué año se descubrió América?')).toBeInTheDocument();
    });
  });

  test('debe permitir avanzar sin seleccionar nada (tratado como blanco)', async () => {
    const user = userEvent.setup();
    render(<TestViewer />);
    
    // Seleccionar un test
    await waitFor(() => {
      expect(screen.getByText('Test de Prueba')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Test de Prueba'));
    
    // Esperar a que se carguen las preguntas
    await waitFor(() => {
      expect(screen.getByText('¿Cuál es la capital de España?')).toBeInTheDocument();
    });
    
    // Hacer clic en siguiente sin seleccionar nada
    const siguienteButton = screen.getByText('Siguiente');
    await user.click(siguienteButton);
    
    // Verificar que avanzó a la siguiente pregunta
    await waitFor(() => {
      expect(screen.getByText('¿En qué año se descubrió América?')).toBeInTheDocument();
    });
  });

  test('debe mostrar respuestas en blanco en los resultados', async () => {
    const user = userEvent.setup();
    render(<TestViewer />);
    
    // Seleccionar un test
    await waitFor(() => {
      expect(screen.getByText('Test de Prueba')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Test de Prueba'));
    
    // Esperar a que se carguen las preguntas
    await waitFor(() => {
      expect(screen.getByText('¿Cuál es la capital de España?')).toBeInTheDocument();
    });
    
    // Dejar primera pregunta en blanco y avanzar
    const siguienteButton = screen.getByText('Siguiente');
    await user.click(siguienteButton);
    
    // En la segunda pregunta, seleccionar una respuesta correcta
    await waitFor(() => {
      expect(screen.getByText('¿En qué año se descubrió América?')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('1492'));
    
    // Finalizar test
    const finalizarButton = screen.getByText('Finalizar Test');
    await user.click(finalizarButton);
    
    // Verificar que se muestran los resultados con respuestas en blanco
    await waitFor(() => {
      expect(screen.getByText('Resultados del Test')).toBeInTheDocument();
      expect(screen.getByText('En Blanco')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument(); // 1 respuesta en blanco
    });
  });
});
