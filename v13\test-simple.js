// Test simple para verificar la función atómica directamente
// Este archivo será eliminado después de las pruebas

const { createClient } = require('@supabase/supabase-js');

// Configuración de Supabase (usando variables de entorno)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variables de entorno de Supabase no configuradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testAtomicFunction() {
  console.log('🧪 Probando función atómica create_user_profile_and_history...\n');

  try {
    // Test 1: Usar un usuario existente para probar
    console.log('Paso 1: Usando usuario existente para prueba...');

    // Crear un UUID único para este test
    const testUserId = crypto.randomUUID();

    // Primero crear el usuario en auth.users usando SQL directo
    const { error: createUserError } = await supabase.rpc('exec_sql', {
      sql: `INSERT INTO auth.users (id, instance_id, email, encrypted_password, email_confirmed_at, created_at, updated_at, aud, role)
            VALUES ('${testUserId}', '00000000-0000-0000-0000-000000000000', 'test-${Date.now()}@testdomain.local', 'encrypted', now(), now(), now(), 'authenticated', 'authenticated')`
    });

    if (createUserError) {
      console.log('⚠️ No se pudo crear usuario en auth, usando función directamente...');
    }

    const userId = testUserId;
    console.log(`✅ Usuario ID para test: ${userId}`);

    // Test 2: Probar función atómica
    console.log('\nPaso 2: Ejecutando función atómica...');
    
    const profileData = {
      subscription_plan: 'free',
      monthly_token_limit: 50000,
      current_month_tokens: 0,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      payment_verified: true,
      stripe_customer_id: null,
      stripe_subscription_id: null,
      last_payment_date: null,
      auto_renew: false,
      plan_expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      plan_features: { test: true },
      security_flags: { 
        test_atomic_function: true,
        created_at: new Date().toISOString()
      }
    };

    const transactionId = crypto.randomUUID();

    const { data: result, error: rpcError } = await supabase
      .rpc('create_user_profile_and_history', {
        p_user_id: userId,
        p_transaction_id: transactionId,
        p_profile_data: profileData
      })
      .single();

    if (rpcError) {
      throw new Error(`Error en función RPC: ${rpcError.message}`);
    }

    console.log('✅ Función atómica ejecutada exitosamente');
    console.log(`   - Profile ID: ${result.created_profile_id}`);
    console.log(`   - History ID: ${result.created_history_id}`);

    // Test 3: Verificar que el perfil fue creado
    console.log('\nPaso 3: Verificando perfil creado...');
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', result.created_profile_id)
      .single();

    if (profileError) {
      throw new Error(`Error verificando perfil: ${profileError.message}`);
    }

    console.log('✅ Perfil verificado en base de datos');
    console.log(`   - User ID: ${profile.user_id}`);
    console.log(`   - Plan: ${profile.subscription_plan}`);
    console.log(`   - Tokens: ${profile.monthly_token_limit}`);
    console.log(`   - Payment verified: ${profile.payment_verified}`);

    // Test 4: Verificar que el historial fue creado
    console.log('\nPaso 4: Verificando historial creado...');
    const { data: history, error: historyError } = await supabase
      .from('user_plan_history')
      .select('*')
      .eq('id', result.created_history_id)
      .single();

    if (historyError) {
      throw new Error(`Error verificando historial: ${historyError.message}`);
    }

    console.log('✅ Historial verificado en base de datos');
    console.log(`   - User ID: ${history.user_id}`);
    console.log(`   - Old plan: ${history.old_plan}`);
    console.log(`   - New plan: ${history.new_plan}`);
    console.log(`   - Reason: ${history.reason}`);
    console.log(`   - Transaction ID: ${history.transaction_id}`);

    // Test 5: Probar upsert (actualización)
    console.log('\nPaso 5: Probando upsert (actualización)...');
    
    const updatedProfileData = {
      subscription_plan: 'usuario',
      monthly_token_limit: 200000,
      current_month_tokens: 1000,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      payment_verified: true,
      stripe_customer_id: 'cus_test123',
      stripe_subscription_id: 'sub_test123',
      last_payment_date: new Date().toISOString(),
      auto_renew: true,
      plan_expires_at: null,
      plan_features: { premium: true },
      security_flags: { 
        test_atomic_function: true,
        updated_at: new Date().toISOString(),
        upgraded: true
      }
    };

    const { data: updateResult, error: updateError } = await supabase
      .rpc('create_user_profile_and_history', {
        p_user_id: userId,
        p_transaction_id: crypto.randomUUID(),
        p_profile_data: updatedProfileData
      })
      .single();

    if (updateError) {
      throw new Error(`Error en actualización: ${updateError.message}`);
    }

    console.log('✅ Upsert ejecutado exitosamente');
    console.log(`   - Mismo Profile ID: ${result.created_profile_id === updateResult.created_profile_id}`);
    console.log(`   - Nuevo History ID: ${updateResult.created_history_id}`);

    // Verificar que el perfil se actualizó
    const { data: updatedProfile } = await supabase
      .from('user_profiles')
      .select('subscription_plan, monthly_token_limit, stripe_customer_id')
      .eq('user_id', userId)
      .single();

    console.log('✅ Actualización verificada');
    console.log(`   - Nuevo plan: ${updatedProfile.subscription_plan}`);
    console.log(`   - Nuevo límite: ${updatedProfile.monthly_token_limit}`);
    console.log(`   - Stripe customer: ${updatedProfile.stripe_customer_id}`);

    console.log('\n🎉 TODOS LOS TESTS PASARON EXITOSAMENTE');
    console.log('✅ La función atómica funciona correctamente');
    console.log('✅ El upsert funciona correctamente');
    console.log('✅ La integridad de datos está garantizada');

  } catch (error) {
    console.log('\n❌ TEST FALLÓ:', error.message);
    console.log('Stack trace:', error.stack);
  }
}

// Ejecutar test
testAtomicFunction();
