// src/app/api/admin/cleanup-expired-free/route.ts
// Endpoint para limpieza manual de cuentas gratuitas expiradas (solo administradores)

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { FreeAccountService } from '@/lib/services/freeAccountService';
import { supabaseAdmin } from '@/lib/supabase/admin';

// Lista de emails de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Agregar más emails de administradores aquí
];

export async function POST(request: NextRequest) {
  try {
    console.log('🧹 Iniciando limpieza manual de cuentas expiradas');
    
    // 1. Verificar autenticación
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No necesitamos setear cookies en este endpoint
          },
        },
      }
    );
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        error: 'Usuario no autenticado'
      }, { status: 401 });
    }
    
    // 2. Verificar permisos de administrador
    if (!user.email || !ADMIN_EMAILS.includes(user.email)) {
      console.log(`❌ Acceso denegado para usuario: ${user.email}`);
      return NextResponse.json({
        error: 'Acceso denegado. Solo administradores pueden ejecutar esta acción.'
      }, { status: 403 });
    }
    
    console.log(`👤 Administrador autorizado: ${user.email}`);
    
    // 3. Obtener parámetros opcionales
    const body = await request.json().catch(() => ({}));
    const { dryRun = false, maxAccounts = 100 } = body;
    
    if (dryRun) {
      console.log('🔍 Ejecutando en modo dry-run (solo consulta)');
    }
    
    // 4. Buscar cuentas expiradas
    const now = new Date().toISOString();
    
    const { data: expiredProfiles, error: searchError } = await supabaseAdmin
      .from('user_profiles')
      .select(`
        user_id, 
        id, 
        plan_expires_at, 
        subscription_plan,
        security_flags,
        created_at
      `)
      .eq('subscription_plan', 'free')
      .lt('plan_expires_at', now)
      .eq('payment_verified', true) // Solo cuentas activas
      .limit(maxAccounts);
    
    if (searchError) {
      throw new Error(`Error buscando cuentas expiradas: ${searchError.message}`);
    }
    
    if (!expiredProfiles || expiredProfiles.length === 0) {
      console.log('✅ No se encontraron cuentas expiradas');
      return NextResponse.json({
        success: true,
        message: 'No hay cuentas expiradas para limpiar',
        found: 0,
        cleaned: 0,
        errors: []
      });
    }
    
    console.log(`📊 Encontradas ${expiredProfiles.length} cuentas expiradas`);
    
    // 5. Preparar reporte detallado
    const accountsReport = expiredProfiles.map(profile => ({
      userId: profile.user_id,
      profileId: profile.id,
      expiredAt: profile.plan_expires_at,
      createdAt: profile.created_at,
      daysSinceExpiration: Math.floor(
        (new Date().getTime() - new Date(profile.plan_expires_at).getTime()) / (1000 * 60 * 60 * 24)
      ),
      securityFlags: profile.security_flags
    }));
    
    // 6. Si es dry-run, solo retornar el reporte
    if (dryRun) {
      return NextResponse.json({
        success: true,
        dryRun: true,
        message: `Se encontraron ${expiredProfiles.length} cuentas para limpiar`,
        found: expiredProfiles.length,
        accounts: accountsReport,
        wouldClean: expiredProfiles.length
      });
    }
    
    // 7. Ejecutar limpieza real
    const result = await FreeAccountService.cleanupExpiredAccounts();
    
    // 8. Registrar la acción en logs de auditoría
    try {
      await supabaseAdmin
        .from('feature_access_log')
        .insert({
          user_id: user.id,
          feature_name: 'admin_cleanup_expired_accounts',
          access_granted: true,
          plan_at_time: 'admin',
          tokens_used: 0,
          metadata: {
            action: 'cleanup_expired_free_accounts',
            found: expiredProfiles.length,
            cleaned: result.cleaned,
            errors_count: result.errors.length,
            executed_at: new Date().toISOString(),
            admin_email: user.email
          }
        });
    } catch (auditError) {
      console.error('⚠️ Error registrando auditoría:', auditError);
    }
    
    console.log(`✅ Limpieza completada: ${result.cleaned} cuentas procesadas`);
    
    return NextResponse.json({
      success: true,
      message: `Limpieza completada exitosamente`,
      found: expiredProfiles.length,
      cleaned: result.cleaned,
      errors: result.errors,
      accounts: accountsReport,
      summary: {
        totalFound: expiredProfiles.length,
        successfullyCleaned: result.cleaned,
        errorsCount: result.errors.length,
        executedBy: user.email,
        executedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('❌ Error crítico en limpieza de cuentas:', error);
    
    return NextResponse.json({
      error: 'Error interno del servidor',
      details: process.env.NODE_ENV === 'development' 
        ? (error instanceof Error ? error.message : 'Error desconocido')
        : undefined
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('📊 Consultando estadísticas de cuentas gratuitas');
    
    // 1. Verificar autenticación y permisos (mismo código que POST)
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
      return NextResponse.json({
        error: 'Acceso denegado'
      }, { status: 403 });
    }
    
    // 2. Obtener estadísticas de cuentas gratuitas
    const now = new Date().toISOString();
    
    // Cuentas gratuitas activas
    const { data: activeAccounts, error: activeError } = await supabaseAdmin
      .from('user_profiles')
      .select('user_id, plan_expires_at, created_at')
      .eq('subscription_plan', 'free')
      .eq('payment_verified', true)
      .gt('plan_expires_at', now);
    
    // Cuentas gratuitas expiradas
    const { data: expiredAccounts, error: expiredError } = await supabaseAdmin
      .from('user_profiles')
      .select('user_id, plan_expires_at, created_at')
      .eq('subscription_plan', 'free')
      .eq('payment_verified', true)
      .lt('plan_expires_at', now);
    
    // Cuentas gratuitas desactivadas
    const { data: disabledAccounts, error: disabledError } = await supabaseAdmin
      .from('user_profiles')
      .select('user_id, plan_expires_at, created_at')
      .eq('subscription_plan', 'free')
      .eq('payment_verified', false);
    
    if (activeError || expiredError || disabledError) {
      throw new Error('Error obteniendo estadísticas');
    }
    
    // 3. Calcular métricas
    const stats = {
      active: activeAccounts?.length || 0,
      expired: expiredAccounts?.length || 0,
      disabled: disabledAccounts?.length || 0,
      total: (activeAccounts?.length || 0) + (expiredAccounts?.length || 0) + (disabledAccounts?.length || 0)
    };
    
    // Cuentas que expiran pronto (próximas 24 horas)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const expiringSoon = activeAccounts?.filter(account => 
      new Date(account.plan_expires_at) <= tomorrow
    ) || [];
    
    return NextResponse.json({
      success: true,
      statistics: stats,
      expiringSoon: {
        count: expiringSoon.length,
        accounts: expiringSoon.map(account => ({
          userId: account.user_id,
          expiresAt: account.plan_expires_at,
          hoursRemaining: Math.ceil(
            (new Date(account.plan_expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60)
          )
        }))
      },
      lastUpdated: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error obteniendo estadísticas:', error);
    
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
