import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService } from '@/lib/supabase/admin';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const sessionId = searchParams.get('session_id');

  if (!sessionId) {
    return NextResponse.json({ 
      error: 'Session ID requerido',
      ready: false 
    }, { status: 400 });
  }

  try {
    // Verificar en stripe_transactions si existe la transacción
    const transaction = await SupabaseAdminService.getTransactionBySessionId(sessionId);

    if (!transaction) {
      return NextResponse.json({
        ready: false,
        message: 'Transacción no encontrada - verificando...',
        status: 'processing'
      });
    }

    // Si existe transacción, verificar si el usuario fue creado
    if (transaction.user_id) {
      const profile = await SupabaseAdminService.getUserProfile(transaction.user_id);

      if (profile && profile.payment_verified) {
        // Usuario encontrado y verificado - cuenta creada exitosamente
        return NextResponse.json({
          ready: true,
          status: 'ready',
          user: {
            id: profile.id,
            email: transaction.user_email,
            plan: profile.subscription_plan,
            paymentVerified: profile.payment_verified,
            hasTemporaryPassword: true, // Siempre true para usuarios creados por webhook
            loginUrl: '/auth/login'
          }
        });
      }
    }

    // Transacción existe pero usuario aún no creado - webhook en proceso
    return NextResponse.json({ 
      ready: false, 
      message: 'Cuenta en proceso de creación...',
      status: 'processing',
      transaction: {
        email: transaction.user_email,
        plan: transaction.plan_id,
        paymentStatus: transaction.payment_status
      }
    });

  } catch (error) {
    console.error('Error verificando estado usuario:', error);
    return NextResponse.json({ 
      ready: false, 
      error: 'Error interno del servidor',
      status: 'error'
    }, { status: 500 });
  }
}
