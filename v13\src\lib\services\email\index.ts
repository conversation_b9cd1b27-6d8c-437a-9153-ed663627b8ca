// src/lib/services/email/index.ts
// Archivo de índice para facilitar importaciones del sistema de notificaciones

// Exportar servicio principal
export { EmailNotificationService } from './emailNotificationService';

// Exportar módulos especializados (para uso avanzado)
export { EmailTemplates } from './emailTemplates';
export { EmailSender } from './emailSender';
export { EmailLogger } from './emailLogger';
export { EmailAnalytics } from './emailAnalytics';

// Exportar tipos e interfaces
export type {
  EmailNotification,
  EmailTemplate,
  EmailStats,
  FailureStats,
  UserNotificationsResult,
  RetryResult,
  EmailLogData,
  EmailUpdateData,
  EmailStatus,
  EmailType,
  ErrorCategory
} from './types';

// Re-exportar para compatibilidad con importaciones existentes
export { EmailNotificationService as default } from './emailNotificationService';
