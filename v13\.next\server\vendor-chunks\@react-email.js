"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-email";
exports.ids = ["vendor-chunks/@react-email"];
exports.modules = {

/***/ "(rsc)/./node_modules/@react-email/render/dist/node/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@react-email/render/dist/node/index.mjs ***!
  \**************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plainTextSelectors: () => (/* binding */ plainTextSelectors),\n/* harmony export */   render: () => (/* binding */ render),\n/* harmony export */   renderAsync: () => (/* binding */ renderAsync)\n/* harmony export */ });\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-to-text */ \"(rsc)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prettier/plugins/html */ \"prettier/plugins/html\");\n/* harmony import */ var prettier_standalone__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prettier/standalone */ \"prettier/standalone\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__, prettier_standalone__WEBPACK_IMPORTED_MODULE_3__]);\n([prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__, prettier_standalone__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = value => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = value => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = x => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/node/render.tsx\n\n\n\n// src/shared/plain-text-selectors.ts\nvar plainTextSelectors = [{\n  selector: \"img\",\n  format: \"skip\"\n}, {\n  selector: \"#__react-email-preview\",\n  format: \"skip\"\n}, {\n  selector: \"a\",\n  options: {\n    linkBrackets: false\n  }\n}];\n\n// src/shared/utils/pretty.ts\n\n\nfunction recursivelyMapDoc(doc, callback) {\n  if (Array.isArray(doc)) {\n    return doc.map(innerDoc => recursivelyMapDoc(innerDoc, callback));\n  }\n  if (typeof doc === \"object\") {\n    if (doc.type === \"group\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback),\n        expandedStates: recursivelyMapDoc(doc.expandedStates, callback)\n      });\n    }\n    if (\"contents\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback)\n      });\n    }\n    if (\"parts\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        parts: recursivelyMapDoc(doc.parts, callback)\n      });\n    }\n    if (doc.type === \"if-break\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        breakContents: recursivelyMapDoc(doc.breakContents, callback),\n        flatContents: recursivelyMapDoc(doc.flatContents, callback)\n      });\n    }\n  }\n  return callback(doc);\n}\nvar modifiedHtml = __spreadValues({}, prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\nif (modifiedHtml.printers) {\n  const previousPrint = modifiedHtml.printers.html.print;\n  modifiedHtml.printers.html.print = (path, options, print, args) => {\n    const node = path.getNode();\n    const rawPrintingResult = previousPrint(path, options, print, args);\n    if (node.type === \"ieConditionalComment\") {\n      const printingResult = recursivelyMapDoc(rawPrintingResult, doc => {\n        if (typeof doc === \"object\" && doc.type === \"line\") {\n          return doc.soft ? \"\" : \" \";\n        }\n        return doc;\n      });\n      return printingResult;\n    }\n    return rawPrintingResult;\n  };\n}\nvar defaults = {\n  endOfLine: \"lf\",\n  tabWidth: 2,\n  plugins: [modifiedHtml],\n  bracketSameLine: true,\n  parser: \"html\"\n};\nvar pretty = (str, options = {}) => {\n  return (0,prettier_standalone__WEBPACK_IMPORTED_MODULE_3__.format)(str.replaceAll(\"\\0\", \"\"), __spreadValues(__spreadValues({}, defaults), options));\n};\n\n// src/node/read-stream.ts\n\nvar decoder = new TextDecoder(\"utf-8\");\nvar readStream = stream => __async(void 0, null, function* () {\n  let result = \"\";\n  if (\"pipeTo\" in stream) {\n    const writableStream = new WritableStream({\n      write(chunk) {\n        result += decoder.decode(chunk);\n      }\n    });\n    yield stream.pipeTo(writableStream);\n  } else {\n    const writable = new node_stream__WEBPACK_IMPORTED_MODULE_4__.Writable({\n      write(chunk, _encoding, callback) {\n        result += decoder.decode(chunk);\n        callback();\n      }\n    });\n    stream.pipe(writable);\n    yield new Promise((resolve, reject) => {\n      writable.on(\"error\", reject);\n      writable.on(\"close\", () => {\n        resolve();\n      });\n    });\n  }\n  return result;\n});\n\n// src/node/render.tsx\n\nvar render = (element, options) => __async(void 0, null, function* () {\n  const suspendedElement = /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n    children: element\n  });\n  const reactDOMServer = yield __webpack_require__.e(/*! import() */ \"vendor-chunks/next\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-dom/server */ \"(rsc)/./node_modules/next/dist/compiled/react-dom/server.js\", 19));\n  let html2;\n  if (Object.hasOwn(reactDOMServer, \"renderToReadableStream\")) {\n    html2 = yield readStream(yield reactDOMServer.renderToReadableStream(suspendedElement));\n  } else {\n    yield new Promise((resolve, reject) => {\n      const stream = reactDOMServer.renderToPipeableStream(suspendedElement, {\n        onAllReady() {\n          return __async(this, null, function* () {\n            html2 = yield readStream(stream);\n            resolve();\n          });\n        },\n        onError(error) {\n          reject(error);\n        }\n      });\n    });\n  }\n  if (options == null ? void 0 : options.plainText) {\n    return (0,html_to_text__WEBPACK_IMPORTED_MODULE_0__.convert)(html2, __spreadValues({\n      selectors: plainTextSelectors\n    }, options.htmlToTextOptions));\n  }\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const document = `${doctype}${html2.replace(/<!DOCTYPE.*?>/, \"\")}`;\n  if (options == null ? void 0 : options.pretty) {\n    return pretty(document);\n  }\n  return document;\n});\n\n// src/node/index.ts\nvar renderAsync = (element, options) => {\n  return render(element, options);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\n");

/***/ })

};
;