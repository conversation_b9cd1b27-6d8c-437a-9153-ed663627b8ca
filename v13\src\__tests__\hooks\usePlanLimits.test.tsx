// src/__tests__/hooks/usePlanLimits.test.tsx
// Tests para hooks de validación de planes

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { renderHook, act, waitFor } from '@testing-library/react';
import { usePlanLimits, useFeatureAccess, useActionBlock } from '@/hooks/usePlanLimits';
import { createClient } from '@/lib/supabase/supabaseClient';
import { PermissionService } from '@/lib/services/permissionService';
import { LimitHandler } from '@/lib/services/limitHandler';

// Mock de dependencias
jest.mock('@/lib/supabase/supabaseClient');
jest.mock('@/lib/services/permissionService');
jest.mock('@/lib/services/limitHandler');

const mockSupabase = createClient as jest.MockedFunction<typeof createClient>;
const mockPermissionService = PermissionService as jest.Mocked<typeof PermissionService>;
const mockLimitHandler = LimitHandler as jest.Mocked<typeof LimitHandler>;

describe('usePlanLimits', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>'
  };

  const mockProfile = {
    user_id: 'test-user-id',
    subscription_plan: 'usuario',
    payment_verified: true,
    monthly_token_limit: 1000000,
    current_month_tokens: 100000,
    current_month: '2025-01-01'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Supabase client
    const mockSupabaseInstance = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null
        })
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockProfile,
              error: null
            })
          })
        })
      })
    };
    
    mockSupabase.mockReturnValue(mockSupabaseInstance as any);
    
    // Mock LimitHandler
    mockLimitHandler.checkUserLimits.mockResolvedValue([]);
  });

  describe('usePlanLimits hook', () => {
    it('should load user plan limits successfully', async () => {
      const { result } = renderHook(() => usePlanLimits());

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.userPlan).toBe('usuario');
      expect(result.current.paymentVerified).toBe(true);
      expect(result.current.tokenUsage).toEqual({
        current: 100000,
        limit: 1000000,
        percentage: 10,
        remaining: 900000
      });
      expect(result.current.error).toBeNull();
    });

    it('should handle authentication error', async () => {
      const mockSupabaseInstance = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: { message: 'Not authenticated' }
          })
        }
      };
      
      mockSupabase.mockReturnValue(mockSupabaseInstance as any);

      const { result } = renderHook(() => usePlanLimits());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBe('Usuario no autenticado');
      expect(result.current.userPlan).toBeNull();
    });

    it('should handle profile not found error', async () => {
      const mockSupabaseInstance = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: mockUser },
            error: null
          })
        },
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { message: 'Profile not found' }
              })
            })
          })
        })
      };
      
      mockSupabase.mockReturnValue(mockSupabaseInstance as any);

      const { result } = renderHook(() => usePlanLimits());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBe('Perfil no encontrado');
    });

    it('should refresh limits when refresh is called', async () => {
      const { result } = renderHook(() => usePlanLimits());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Clear previous calls
      jest.clearAllMocks();

      act(() => {
        result.current.refresh();
      });

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockLimitHandler.checkUserLimits).toHaveBeenCalledWith('test-user-id');
    });

    it('should handle month rollover correctly', async () => {
      const lastMonthProfile = {
        ...mockProfile,
        current_month_tokens: 500000,
        current_month: '2024-12-01' // Last month
      };

      const mockSupabaseInstance = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: mockUser },
            error: null
          })
        },
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: lastMonthProfile,
                error: null
              })
            })
          })
        })
      };
      
      mockSupabase.mockReturnValue(mockSupabaseInstance as any);

      const { result } = renderHook(() => usePlanLimits());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should reset tokens for new month
      expect(result.current.tokenUsage?.current).toBe(0);
      expect(result.current.tokenUsage?.remaining).toBe(1000000);
    });
  });

  describe('useFeatureAccess hook', () => {
    beforeEach(() => {
      mockPermissionService.checkClientPermission.mockResolvedValue({
        granted: true,
        userPlan: 'usuario',
        tokenInfo: {
          current: 100000,
          limit: 1000000,
          remaining: 900000,
          percentage: 10
        }
      });
    });

    it('should check feature access automatically', async () => {
      const { result } = renderHook(() => 
        useFeatureAccess('ai_tutor_chat', 1000, true)
      );

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.allowed).toBe(true);
      expect(mockPermissionService.checkClientPermission).toHaveBeenCalledWith({
        feature: 'ai_tutor_chat',
        tokensRequired: 1000,
        requiresPayment: true,
        minimumPlan: ['usuario', 'pro']
      });
    });

    it('should not auto-check when autoCheck is false', async () => {
      const { result } = renderHook(() => 
        useFeatureAccess('ai_tutor_chat', 1000, false)
      );

      // Wait a bit to ensure no automatic check
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(result.current.loading).toBe(true);
      expect(mockPermissionService.checkClientPermission).not.toHaveBeenCalled();
    });

    it('should allow manual check access', async () => {
      const { result } = renderHook(() => 
        useFeatureAccess('ai_tutor_chat', 1000, false)
      );

      await act(async () => {
        await result.current.checkAccess();
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.allowed).toBe(true);
      expect(mockPermissionService.checkClientPermission).toHaveBeenCalled();
    });

    it('should handle permission denied', async () => {
      mockPermissionService.checkClientPermission.mockResolvedValue({
        granted: false,
        reason: 'Insufficient plan',
        upgradeRequired: true,
        suggestedPlan: 'pro'
      });

      const { result } = renderHook(() => 
        useFeatureAccess('study_planning', 2000, true)
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.allowed).toBe(false);
      expect(result.current.reason).toBe('Insufficient plan');
      expect(result.current.upgradeRequired).toBe(true);
      expect(result.current.suggestedPlan).toBe('pro');
    });

    it('should handle errors gracefully', async () => {
      mockPermissionService.checkClientPermission.mockRejectedValue(
        new Error('Network error')
      );

      const { result } = renderHook(() => 
        useFeatureAccess('ai_tutor_chat', 1000, true)
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.allowed).toBe(false);
      expect(result.current.reason).toBe('Error verificando acceso');
    });
  });

  describe('useActionBlock hook', () => {
    beforeEach(() => {
      mockLimitHandler.isActionBlocked.mockResolvedValue({
        blocked: false
      });
    });

    it('should check if action is blocked', async () => {
      const { result } = renderHook(() => 
        useActionBlock('test_generation', 500)
      );

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.isBlocked).toBe(false);
      expect(mockLimitHandler.isActionBlocked).toHaveBeenCalledWith(
        'test-user-id',
        'test_generation',
        500
      );
    });

    it('should detect blocked actions', async () => {
      mockLimitHandler.isActionBlocked.mockResolvedValue({
        blocked: true,
        reason: 'Token limit exceeded',
        limitStatus: {
          type: 'tokens',
          severity: 'exceeded',
          current: 50000,
          limit: 50000,
          percentage: 100,
          message: 'Limit exceeded',
          actionRequired: true
        }
      });

      const { result } = renderHook(() => 
        useActionBlock('test_generation', 1000)
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.isBlocked).toBe(true);
      expect(result.current.reason).toBe('Token limit exceeded');
      expect(result.current.limitStatus).toBeDefined();
    });

    it('should handle authentication errors', async () => {
      const mockSupabaseInstance = {
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: { message: 'Not authenticated' }
          })
        }
      };
      
      mockSupabase.mockReturnValue(mockSupabaseInstance as any);

      const { result } = renderHook(() => 
        useActionBlock('test_generation', 500)
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.isBlocked).toBe(true);
      expect(result.current.reason).toBe('Usuario no autenticado');
    });

    it('should allow refreshing block status', async () => {
      const { result } = renderHook(() => 
        useActionBlock('test_generation', 500)
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Clear previous calls
      jest.clearAllMocks();

      await act(async () => {
        await result.current.refresh();
      });

      expect(mockLimitHandler.isActionBlocked).toHaveBeenCalledWith(
        'test-user-id',
        'test_generation',
        500
      );
    });
  });
});
