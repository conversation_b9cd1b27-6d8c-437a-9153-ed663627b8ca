// Endpoint temporal para corregir el usuario
import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    const { email, correctUserId } = await request.json();
    
    if (!email || !correctUserId) {
      return NextResponse.json({ error: 'Email and correctUserId required' }, { status: 400 });
    }

    console.log(`🔧 Corrigiendo usuario: ${email} -> ${correctUserId}`);

    // 1. Verificar que el usuario existe en Auth
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(correctUserId);
    
    if (authError || !authUser.user) {
      return NextResponse.json({ error: 'User not found in auth' }, { status: 404 });
    }

    // 2. Actualizar el perfil con los datos de la suscripción
    const { data: updatedProfile, error: updateError } = await supabaseAdmin
      .from('user_profiles')
      .update({
        subscription_plan: 'usuario',
        payment_verified: true,
        stripe_customer_id: 'cus_SWXCXC1eEuqbxo', // Del último pago
        stripe_subscription_id: 'sub_1RbU8X07kFn3sIXh5w3FEP5n', // Del último pago
        plan_expires_at: '2025-07-18T22:07:17.749Z', // Del último pago
        auto_renew: true,
        last_payment_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        monthly_token_limit: 1000000, // Plan usuario
        security_flags: {
          subscription_status: 'active',
          subscription_id: 'sub_1RbU8X07kFn3sIXh5w3FEP5n',
          subscription_created_at: new Date().toISOString(),
          manual_fix: true,
          fixed_at: new Date().toISOString()
        }
      })
      .eq('user_id', correctUserId)
      .select();

    if (updateError) {
      console.error('Error actualizando perfil:', updateError);
      return NextResponse.json({ error: 'Error updating profile', details: updateError }, { status: 500 });
    }

    // 3. Verificar el resultado
    const { data: verifyProfile, error: verifyError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', correctUserId)
      .single();

    return NextResponse.json({
      success: true,
      message: 'User profile updated successfully',
      auth_user: {
        id: authUser.user.id,
        email: authUser.user.email
      },
      updated_profile: updatedProfile,
      verified_profile: verifyProfile
    });

  } catch (error) {
    console.error('Error fixing user:', error);
    return NextResponse.json({ 
      error: 'Internal error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
