// src/lib/services/freeAccountService.ts
// Servicio para gestión automatizada de cuentas gratuitas

import { SupabaseAdminService, ExtendedUserProfile } from '@/lib/supabase/admin';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/lib/utils/planLimits';
import { supabaseAdmin } from '@/lib/supabase/admin';

export interface CreateFreeAccountRequest {
  email: string;
  name?: string;
}

export interface FreeAccountResult {
  success: boolean;
  userId?: string;
  profileId?: string;
  error?: string;
  expiresAt?: string;
}

export interface FreeAccountStatus {
  isActive: boolean;
  expiresAt: string | null;
  daysRemaining: number;
  hoursRemaining: number;
  usageCount: {
    documents: number;
    tests: number;
    flashcards: number;
    mindMaps: number;
    tokens: number;
  };
  limits: {
    documents: number;
    tests: number;
    flashcards: number;
    mindMaps: number;
    tokens: number;
  };
}

export class FreeAccountService {
  
  /**
   * Crear cuenta gratuita automáticamente
   */
  static async createFreeAccount(request: CreateFreeAccountRequest): Promise<FreeAccountResult> {
    try {
      console.log('🆓 Iniciando creación de cuenta gratuita:', request.email);
      
      // 1. Validar que el email no esté ya registrado
      // Usar getUserByEmail que es más eficiente para verificar existencia
      try {
        const existingUser = await SupabaseAdminService.getUserByEmail(request.email);
        if (existingUser) {
          return {
            success: false,
            error: 'Ya existe una cuenta con este email'
          };
        }
      } catch (error) {
        // Si no se encuentra el usuario, continuamos (esto es lo que queremos)
        console.log('Usuario no existe, continuando con la creación');
      }
      
      // 2. Calcular fecha de expiración (5 días desde ahora)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 5);
      
      // 3. Crear usuario con invitación
      const userData = {
        name: request.name,
        plan: 'free',
        free_account: true,
        expires_at: expiresAt.toISOString(),
        created_via: 'free_registration',
        // Flags adicionales para identificación en middleware
        legitimate_user: true,
        registration_type: 'automatic_free',
        registration_timestamp: new Date().toISOString()
      };
      
      console.log('🔄 Creando invitación de usuario con datos:', {
        email: request.email,
        userData: userData,
        timestamp: new Date().toISOString()
      });

      const userInvitation = await SupabaseAdminService.createUserWithInvitation(
        request.email,
        userData
      );

      console.log('📊 Resultado de createUserWithInvitation:', {
        hasUser: !!userInvitation.user,
        userId: userInvitation.user?.id,
        userEmail: userInvitation.user?.email,
        userAud: userInvitation.user?.aud,
        userRole: userInvitation.user?.role,
        emailConfirmed: userInvitation.user?.email_confirmed_at,
        createdAt: userInvitation.user?.created_at,
        userMetadata: userInvitation.user?.user_metadata,
        appMetadata: userInvitation.user?.app_metadata,
        fullResponse: userInvitation
      });

      if (!userInvitation.user) {
        throw new Error('Error creando usuario - no se devolvió objeto user');
      }

      console.log('✅ Usuario gratuito creado exitosamente:', {
        userId: userInvitation.user.id,
        email: userInvitation.user.email,
        needsEmailConfirmation: !userInvitation.user.email_confirmed_at
      });
      
      // 4. Crear perfil de usuario y registrar historial de forma atómica
      const planConfig = getPlanConfiguration('free');
      if (!planConfig) {
        throw new Error('Configuración de plan gratuito no encontrada');
      }

      const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
      const profileData = { // Preparamos el objeto JSON para la función
        subscription_plan: 'free',
        monthly_token_limit: getTokenLimitForPlan('free'),
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: true, // Las cuentas gratuitas se consideran "verificadas"
        stripe_customer_id: null,
        stripe_subscription_id: null,
        last_payment_date: null,
        auto_renew: false,
        plan_expires_at: expiresAt.toISOString(),
        plan_features: planConfig.features,
        security_flags: {
          created_via_free_registration: true,
          free_account: true,
          expires_at: expiresAt.toISOString(),
          activation_date: new Date().toISOString(),
          usage_count: {
            documents: 0,
            tests: 0,
            flashcards: 0,
            mindMaps: 0,
            tokens: 0
          }
        }
      };

      // Las cuentas gratuitas no tienen transacción asociada, pasar null
      const { data: creationResult, error: rpcError } = await supabaseAdmin
        .rpc('create_user_profile_and_history', {
          p_user_id: userInvitation.user.id,
          p_transaction_id: null, // NULL para cuentas gratuitas
          p_profile_data: profileData
        })
        .single(); // .single() es importante para obtener un único resultado

      if (rpcError) {
        console.error('Error al ejecutar la función create_user_profile_and_history:', rpcError);
        throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);
      }

      const profileId = (creationResult as any).created_profile_id;
      console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);

      console.log('🎉 Cuenta gratuita creada exitosamente');

      return {
        success: true,
        userId: userInvitation.user.id,
        profileId: profileId, // Usamos el ID devuelto por la función
        expiresAt: expiresAt.toISOString()
      };
      
    } catch (error) {
      console.error('❌ Error creando cuenta gratuita:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }
  
  /**
   * Verificar estado de cuenta gratuita
   */
  static async getFreeAccountStatus(userId: string): Promise<FreeAccountStatus | null> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile || profile.subscription_plan !== 'free') {
        return null;
      }
      
      const now = new Date();
      const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;
      
      if (!expiresAt) {
        return null;
      }
      
      const isActive = now < expiresAt;
      const timeDiff = expiresAt.getTime() - now.getTime();
      const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
      const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));
      
      // Obtener contadores de uso desde security_flags
      // Para tokens, usar current_month_tokens como fuente única de verdad
      const usageCount = profile.security_flags?.usage_count || {
        documents: 0,
        tests: 0,
        flashcards: 0,
        mindMaps: 0,
        tokens: 0 // No usado, se obtiene de current_month_tokens
      };

      // Sobrescribir tokens con la fuente de verdad
      usageCount.tokens = profile.current_month_tokens || 0;
      
      // Límites del plan gratuito (trial de 5 días)
      const planConfig = getPlanConfiguration('free');
      const limits = {
        documents: planConfig?.limits.documents || 1,
        tests: planConfig?.limits.testsForTrial || 10,        // Límite total para trial
        flashcards: planConfig?.limits.flashcardsForTrial || 10,  // Límite total para trial
        mindMaps: planConfig?.limits.mindMapsForTrial || 2,       // Límite total para trial
        tokens: planConfig?.limits.tokensForTrial || 50000      // Límite total para trial
      };
      
      return {
        isActive,
        expiresAt: expiresAt.toISOString(),
        daysRemaining,
        hoursRemaining,
        usageCount,
        limits
      };
      
    } catch (error) {
      console.error('Error obteniendo estado de cuenta gratuita:', error);
      return null;
    }
  }
  
  /**
   * Incrementar contador de uso
   * NOTA: Para tokens, el incremento se maneja automáticamente por tokenTracker.ts
   * que actualiza current_month_tokens. Este método solo maneja features no-token.
   */
  static async incrementUsageCount(
    userId: string,
    feature: 'documents' | 'tests' | 'flashcards' | 'mindMaps',
    amount: number = 1
  ): Promise<boolean> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);
      
      if (!profile || profile.subscription_plan !== 'free') {
        return false;
      }
      
      const currentUsage = profile.security_flags?.usage_count || {
        documents: 0,
        tests: 0,
        flashcards: 0,
        mindMaps: 0,
        tokens: 0 // Mantenemos para compatibilidad, pero no se actualiza aquí
      };

      currentUsage[feature] = (currentUsage[feature] || 0) + amount;

      // Actualizar solo security_flags (no current_month_tokens)
      // Los tokens se manejan exclusivamente por tokenTracker.ts
      const updateData: Partial<ExtendedUserProfile> = {
        security_flags: {
          ...profile.security_flags,
          usage_count: currentUsage
        },
        updated_at: new Date().toISOString()
      };
      
      await supabaseAdmin
        .from('user_profiles')
        .update(updateData)
        .eq('user_id', userId);
      
      return true;
      
    } catch (error) {
      console.error('Error incrementando contador de uso:', error);
      return false;
    }
  }
  
  /**
   * Verificar si se puede realizar una acción
   */
  static async canPerformAction(
    userId: string,
    feature: 'documents' | 'tests' | 'flashcards' | 'mindMaps' | 'tokens',
    amount: number = 1
  ): Promise<{ allowed: boolean; reason?: string; remaining?: number }> {
    try {
      const status = await this.getFreeAccountStatus(userId);
      
      if (!status) {
        return { allowed: false, reason: 'Cuenta no encontrada o no es gratuita' };
      }
      
      if (!status.isActive) {
        return { allowed: false, reason: 'Cuenta gratuita expirada' };
      }
      
      const currentUsage = status.usageCount[feature] || 0;
      const limit = status.limits[feature];
      const remaining = limit - currentUsage;
      
      if (currentUsage + amount > limit) {
        return { 
          allowed: false, 
          reason: `Límite de ${feature} alcanzado (${limit})`,
          remaining: Math.max(0, remaining)
        };
      }
      
      return { 
        allowed: true, 
        remaining: remaining - amount 
      };
      
    } catch (error) {
      console.error('Error verificando acción:', error);
      return { allowed: false, reason: 'Error interno' };
    }
  }
  
  /**
   * Limpiar cuentas gratuitas expiradas
   */
  static async cleanupExpiredAccounts(): Promise<{
    cleaned: number;
    errors: string[];
  }> {
    try {
      console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');
      
      const now = new Date().toISOString();
      
      // Buscar cuentas gratuitas expiradas
      const { data: expiredProfiles, error } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, id')
        .eq('subscription_plan', 'free')
        .lt('plan_expires_at', now);
      
      if (error) {
        throw new Error(`Error buscando cuentas expiradas: ${error.message}`);
      }
      
      if (!expiredProfiles || expiredProfiles.length === 0) {
        console.log('✅ No hay cuentas expiradas para limpiar');
        return { cleaned: 0, errors: [] };
      }
      
      console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas`);
      
      const errors: string[] = [];
      let cleaned = 0;
      
      for (const profile of expiredProfiles) {
        try {
          // Desactivar usuario en auth
          await supabaseAdmin.auth.admin.updateUserById(profile.user_id, {
            user_metadata: { account_disabled: true, disabled_reason: 'free_account_expired' }
          });
          
          // Marcar perfil como inactivo
          await supabaseAdmin
            .from('user_profiles')
            .update({
              payment_verified: false,
              security_flags: {
                account_disabled: true,
                disabled_at: new Date().toISOString(),
                disabled_reason: 'free_account_expired'
              }
            })
            .eq('user_id', profile.user_id);
          
          cleaned++;
          
        } catch (cleanupError) {
          const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }
      
      console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);
      
      return { cleaned, errors };
      
    } catch (error) {
      console.error('❌ Error en limpieza de cuentas:', error);
      return { 
        cleaned: 0, 
        errors: [error instanceof Error ? error.message : 'Error desconocido'] 
      };
    }
  }
}
