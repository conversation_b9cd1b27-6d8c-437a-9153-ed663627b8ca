'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Registrar componentes de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface TokenUsageChartProps {
  dailyHistory: Array<{
    date: string;
    tokens: number;
  }>;
}

export default function TokenUsageChart({ dailyHistory }: TokenUsageChartProps) {
  // Preparar datos para el gráfico
  const labels = dailyHistory.map(item => {
    const date = new Date(item.date);
    return date.toLocaleDateString('es-ES', { 
      month: 'short', 
      day: 'numeric' 
    });
  });

  const data = {
    labels,
    datasets: [
      {
        label: 'Tokens usados',
        data: dailyHistory.map(item => item.tokens),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: 'rgb(59, 130, 246)',
        pointRadius: 3,
        pointHoverRadius: 5,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Uso de Tokens - Últimos 30 días',
        font: {
          size: 14,
          weight: 'bold' as const,
        },
        color: '#374151',
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(59, 130, 246, 0.5)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const value = context.parsed.y;
            if (value >= 1000000) {
              return `${(value / 1000000).toFixed(1)}M tokens`;
            }
            if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}K tokens`;
            }
            return `${value.toLocaleString()} tokens`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#6B7280',
          font: {
            size: 11,
          },
          maxTicksLimit: 8,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(107, 114, 128, 0.1)',
        },
        ticks: {
          color: '#6B7280',
          font: {
            size: 11,
          },
          callback: function(value: any) {
            if (value >= 1000000) {
              return `${(value / 1000000).toFixed(1)}M`;
            }
            if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}K`;
            }
            return value.toLocaleString();
          }
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  // Calcular estadísticas básicas
  const totalTokens = dailyHistory.reduce((sum, item) => sum + item.tokens, 0);
  const averageDaily = totalTokens / dailyHistory.length;
  const maxDaily = Math.max(...dailyHistory.map(item => item.tokens));

  const formatTokens = (tokens: number) => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`;
    }
    if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(1)}K`;
    }
    return tokens.toLocaleString();
  };

  return (
    <div className="space-y-4">
      {/* Gráfico */}
      <div className="h-64 w-full">
        <Line data={data} options={options} />
      </div>

      {/* Estadísticas resumidas */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600">
            {formatTokens(totalTokens)}
          </div>
          <div className="text-xs text-gray-500">Total (30 días)</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-green-600">
            {formatTokens(Math.round(averageDaily))}
          </div>
          <div className="text-xs text-gray-500">Promedio diario</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-orange-600">
            {formatTokens(maxDaily)}
          </div>
          <div className="text-xs text-gray-500">Máximo diario</div>
        </div>
      </div>
    </div>
  );
}
