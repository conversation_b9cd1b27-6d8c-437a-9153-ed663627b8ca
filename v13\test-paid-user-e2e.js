// Test end-to-end para creación de usuario con plan de pago
// Este archivo será eliminado después de las pruebas

import { UserManagementService } from './src/lib/services/userManagement.js';
import { SupabaseAdminService, supabaseAdmin } from './src/lib/supabase/admin.js';

async function testPaidUserE2E() {
  console.log('🧪 Iniciando test end-to-end de usuario con plan de pago...\n');

  const testEmail = `e2e-paid-${Date.now()}@example.com`;
  const testName = 'Usuario Pago E2E Test';
  const sessionId = `cs_test_${Date.now()}`;
  const customerId = `cus_test_${Date.now()}`;

  try {
    console.log('Test: Creación completa de usuario con plan de pago');
    console.log(`Email: ${testEmail}`);
    console.log(`Nombre: ${testName}`);
    console.log(`Session ID: ${sessionId}\n`);

    // Paso 1: Crear usuario con plan de pago
    console.log('Paso 1: Ejecutando UserManagementService.createUserWithPlan...');
    const startTime = Date.now();
    
    const createRequest = {
      email: testEmail,
      name: testName,
      planId: 'usuario',
      stripeSessionId: sessionId,
      stripeCustomerId: customerId,
      amount: 1999, // 19.99 EUR
      currency: 'eur',
      subscriptionId: `sub_test_${Date.now()}`,
      planExpiresAt: null // Suscripción recurrente
    };

    const result = await UserManagementService.createUserWithPlan(createRequest);

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (!result.success) {
      throw new Error(`Creación falló: ${result.error}`);
    }

    console.log('✅ Usuario con plan de pago creado exitosamente');
    console.log(`   - Tiempo total: ${duration}ms`);
    console.log(`   - User ID: ${result.userId}`);
    console.log(`   - Profile ID: ${result.profileId}`);
    console.log(`   - Transaction ID: ${result.transactionId}\n`);

    // Paso 2: Verificar usuario en auth.users
    console.log('Paso 2: Verificando usuario en auth.users...');
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(result.userId);
    
    if (authError) {
      throw new Error(`Error obteniendo usuario auth: ${authError.message}`);
    }

    console.log('✅ Usuario verificado en auth.users');
    console.log(`   - Email: ${authUser.user.email}`);
    console.log(`   - Metadata: ${JSON.stringify(authUser.user.user_metadata)}\n`);

    // Paso 3: Verificar perfil en user_profiles
    console.log('Paso 3: Verificando perfil en user_profiles...');
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('id', result.profileId)
      .single();

    if (profileError) {
      throw new Error(`Error obteniendo perfil: ${profileError.message}`);
    }

    console.log('✅ Perfil verificado en user_profiles');
    console.log(`   - Plan: ${profile.subscription_plan}`);
    console.log(`   - Token limit: ${profile.monthly_token_limit}`);
    console.log(`   - Payment verified: ${profile.payment_verified}`);
    console.log(`   - Stripe customer: ${profile.stripe_customer_id}`);
    console.log(`   - Stripe subscription: ${profile.stripe_subscription_id}`);
    console.log(`   - Auto renew: ${profile.auto_renew}`);
    console.log(`   - Plan expires: ${profile.plan_expires_at}`);
    console.log(`   - Security flags: ${JSON.stringify(profile.security_flags, null, 2)}\n`);

    // Paso 4: Verificar historial en user_plan_history
    console.log('Paso 4: Verificando historial en user_plan_history...');
    const { data: history, error: historyError } = await supabaseAdmin
      .from('user_plan_history')
      .select('*')
      .eq('user_id', result.userId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (historyError) {
      throw new Error(`Error obteniendo historial: ${historyError.message}`);
    }

    if (!history || history.length === 0) {
      throw new Error('No se encontró historial de plan');
    }

    const latestHistory = history[0];
    console.log('✅ Historial verificado en user_plan_history');
    console.log(`   - Old plan: ${latestHistory.old_plan}`);
    console.log(`   - New plan: ${latestHistory.new_plan}`);
    console.log(`   - Changed by: ${latestHistory.changed_by}`);
    console.log(`   - Reason: ${latestHistory.reason}`);
    console.log(`   - Transaction ID: ${latestHistory.transaction_id}`);
    console.log(`   - Created at: ${latestHistory.created_at}\n`);

    // Paso 5: Verificar transacción
    console.log('Paso 5: Verificando transacción...');
    const transaction = await SupabaseAdminService.getTransactionBySessionId(sessionId);

    if (!transaction) {
      throw new Error('No se encontró la transacción');
    }

    console.log('✅ Transacción verificada');
    console.log(`   - ID: ${transaction.id}`);
    console.log(`   - Status: ${transaction.status}`);
    console.log(`   - User ID: ${transaction.user_id}`);
    console.log(`   - Amount: ${transaction.amount} ${transaction.currency}`);
    console.log(`   - Plan: ${transaction.plan_id}\n`);

    // Paso 6: Probar actualización de plan existente
    console.log('Paso 6: Probando actualización de plan existente...');
    const updateResult = await UserManagementService.updateUserPlan(
      result.userId,
      'pro',
      result.transactionId,
      'Test upgrade to pro plan'
    );

    if (!updateResult.success) {
      throw new Error(`Actualización falló: ${updateResult.error}`);
    }

    console.log('✅ Actualización de plan exitosa');
    console.log(`   - Nuevo Profile ID: ${updateResult.profileId}\n`);

    // Verificar que el plan se actualizó
    const { data: updatedProfile } = await supabaseAdmin
      .from('user_profiles')
      .select('subscription_plan, monthly_token_limit')
      .eq('user_id', result.userId)
      .single();

    console.log('✅ Plan actualizado verificado');
    console.log(`   - Nuevo plan: ${updatedProfile.subscription_plan}`);
    console.log(`   - Nuevo límite: ${updatedProfile.monthly_token_limit}\n`);

    // Paso 7: Verificar que se creó nuevo historial
    const { data: newHistory } = await supabaseAdmin
      .from('user_plan_history')
      .select('*')
      .eq('user_id', result.userId)
      .order('created_at', { ascending: false })
      .limit(2);

    console.log('✅ Historial de actualización verificado');
    console.log(`   - Entradas de historial: ${newHistory.length}`);
    console.log(`   - Última actualización: ${newHistory[0].new_plan}`);
    console.log(`   - Razón: ${newHistory[0].reason}\n`);

    // Resumen final
    console.log('🎉 TEST END-TO-END COMPLETADO EXITOSAMENTE');
    console.log('✅ Todos los componentes funcionan correctamente:');
    console.log('   - Creación atómica de perfil e historial');
    console.log('   - Gestión de transacciones');
    console.log('   - Actualización de planes');
    console.log('   - Integridad de datos mantenida');
    console.log(`   - Tiempo total de ejecución: ${duration}ms`);

  } catch (error) {
    console.log('❌ TEST END-TO-END FALLÓ:', error.message);
    console.log('Stack trace:', error.stack);
  }
}

// Ejecutar test
testPaidUserE2E().catch(console.error);
