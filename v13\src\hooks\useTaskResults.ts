'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';

interface UseTaskResultsOptions {
  taskType: BackgroundTask['type'];
  onResult?: (result: any) => void;
  onError?: (error: string) => void;
}

export const useTaskResults = ({ taskType, onResult, onError }: UseTaskResultsOptions) => {
  const { tasks } = useBackgroundTasks();
  const [processedTaskIds, setProcessedTaskIds] = useState<Set<string>>(new Set());

  // Usar refs para los callbacks para evitar dependencias que cambien en cada render
  const onResultRef = useRef(onResult);
  const onErrorRef = useRef(onError);

  // Actualizar las refs cuando cambien los callbacks
  onResultRef.current = onResult;
  onErrorRef.current = onError;

  useEffect(() => {
    // Buscar tareas completadas del tipo especificado que no hayamos procesado
    const newCompletedTasks = tasks.filter(task =>
      task.type === taskType &&
      task.status === 'completed' &&
      !processedTaskIds.has(task.id) &&
      task.result
    );

    // Buscar tareas con error del tipo especificado que no hayamos procesado
    const newErrorTasks = tasks.filter(task =>
      task.type === taskType &&
      task.status === 'error' &&
      !processedTaskIds.has(task.id) &&
      task.error
    );

    // Procesar nuevas tareas completadas
    if (newCompletedTasks.length > 0) {
      const latestTask = newCompletedTasks[newCompletedTasks.length - 1];

      setProcessedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(latestTask.id);
        return newSet;
      });

      if (onResultRef.current) {
        // Usar setTimeout para evitar actualizaciones durante el render
        setTimeout(() => {
          onResultRef.current?.(latestTask.result);
        }, 0);
      }
    }

    // Procesar nuevas tareas con error
    if (newErrorTasks.length > 0) {
      const latestErrorTask = newErrorTasks[newErrorTasks.length - 1];

      setProcessedTaskIds(prev => {
        const newSet = new Set(prev);
        newSet.add(latestErrorTask.id);
        return newSet;
      });

      if (onErrorRef.current) {
        // Usar setTimeout para evitar actualizaciones durante el render
        setTimeout(() => {
          onErrorRef.current?.(latestErrorTask.error!);
        }, 0);
      }
    }
  }, [tasks, taskType, processedTaskIds]);

  // Función para resetear los IDs procesados (útil para procesar nuevos resultados)
  const resetProcessed = useCallback(() => {
    setProcessedTaskIds(new Set());
  }, []);

  return {
    resetProcessed
  };
};
