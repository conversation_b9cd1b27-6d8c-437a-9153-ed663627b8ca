// src/app/api/auth/register-free/route.ts
// Endpoint para registro automático de cuentas gratuitas

import { NextRequest, NextResponse } from 'next/server';
import { FreeAccountService } from '@/lib/services/freeAccountService';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

// Rate limiting simple en memoria (en producción usar Redis)
const registrationAttempts = new Map<string, { count: number; lastAttempt: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos
const MAX_ATTEMPTS = 3; // Máximo 3 intentos por IP en 15 minutos

export async function POST(request: NextRequest) {
  try {
    const startTime = Date.now();
    console.log('🆓 Iniciando registro de cuenta gratuita');
    
    // 1. Rate limiting básico
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const now = Date.now();
    const attempts = registrationAttempts.get(clientIP);
    
    if (attempts) {
      // Limpiar intentos antiguos
      if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {
        registrationAttempts.delete(clientIP);
      } else if (attempts.count >= MAX_ATTEMPTS) {
        console.log(`❌ Rate limit excedido para IP: ${clientIP}`);
        return NextResponse.json({
          error: 'Demasiados intentos de registro. Inténtalo más tarde.',
          retryAfter: Math.ceil((RATE_LIMIT_WINDOW - (now - attempts.lastAttempt)) / 1000)
        }, { status: 429 });
      }
    }
    
    // 2. Validar datos de entrada
    const body = await request.json();
    const { email, customerName } = body;
    
    if (!email || typeof email !== 'string') {
      return NextResponse.json({
        error: 'Email es requerido y debe ser válido'
      }, { status: 400 });
    }
    
    // Validación básica de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: 'Formato de email inválido'
      }, { status: 400 });
    }
    
    // Normalizar email
    const normalizedEmail = email.toLowerCase().trim();
    
    console.log('📧 Procesando registro para:', normalizedEmail);
    
    // 3. Actualizar contador de intentos
    const currentAttempts = registrationAttempts.get(clientIP) || { count: 0, lastAttempt: 0 };
    registrationAttempts.set(clientIP, {
      count: currentAttempts.count + 1,
      lastAttempt: now
    });
    
    // 4. Crear cuenta gratuita
    const result = await FreeAccountService.createFreeAccount({
      email: normalizedEmail,
      name: customerName || undefined
    });
    
    if (!result.success) {
      console.log('❌ Error creando cuenta gratuita:', result.error);
      
      // Si el email ya existe, no es un error de rate limiting
      if (result.error?.includes('Ya existe una cuenta')) {
        return NextResponse.json({
          error: result.error,
          code: 'EMAIL_EXISTS'
        }, { status: 409 });
      }
      
      return NextResponse.json({
        error: result.error || 'Error interno al crear la cuenta'
      }, { status: 500 });
    }
    
    console.log('✅ Cuenta gratuita creada exitosamente:', result.userId);
    
    // 5. Enviar email de bienvenida
    try {
      if (process.env.RESEND_API_KEY) {
        const welcomeEmailData = {
          from: 'OposiAI <<EMAIL>>',
          to: [normalizedEmail],
          subject: '🎉 ¡Bienvenido a OposiAI! Tu cuenta gratuita está lista',
          html: generateWelcomeEmail(customerName || 'Usuario', result.expiresAt!)
        };

        await resend.emails.send(welcomeEmailData);
        console.log('📧 Email de bienvenida enviado');
      }
    } catch (emailError) {
      console.error('⚠️ Error enviando email de bienvenida:', emailError);
      // No fallar el registro por error de email
    }
    
    // 6. Enviar notificación al administrador
    try {
      if (process.env.NOTIFICATION_EMAIL) {
        const adminNotificationData = {
          from: 'OposiAI Notificaciones <<EMAIL>>',
          to: [process.env.NOTIFICATION_EMAIL],
          subject: '🆓 Nueva Cuenta Gratuita Creada - OposiAI',
          html: generateAdminNotification(normalizedEmail, customerName, result.expiresAt!)
        };

        await resend.emails.send(adminNotificationData);
        console.log('📧 Notificación de administrador enviada');
      }
    } catch (notificationError) {
      console.error('⚠️ Error enviando notificación de administrador:', notificationError);
      // No fallar el registro por error de notificación
    }
    
    // 7. Limpiar rate limiting en caso de éxito
    registrationAttempts.delete(clientIP);
    
    const processingTime = Date.now() - startTime;
    console.log(`🎉 Registro gratuito completado en ${processingTime}ms`);
    
    return NextResponse.json({
      success: true,
      message: 'Cuenta gratuita creada exitosamente',
      data: {
        userId: result.userId,
        expiresAt: result.expiresAt,
        sessionId: `free_${result.userId}_${Date.now()}` // Simular session ID para compatibilidad
      }
    });
    
  } catch (error) {
    console.error('❌ Error crítico en registro gratuito:', error);
    
    return NextResponse.json({
      error: 'Error interno del servidor',
      details: process.env.NODE_ENV === 'development' 
        ? (error instanceof Error ? error.message : 'Error desconocido')
        : undefined
    }, { status: 500 });
  }
}

/**
 * Generar HTML para email de bienvenida
 */
function generateWelcomeEmail(name: string, expiresAt: string): string {
  const expirationDate = new Date(expiresAt).toLocaleDateString('es-ES', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin-bottom: 10px;">¡Bienvenido a OposiAI!</h1>
        <p style="color: #6b7280; font-size: 18px;">Tu asistente inteligente para oposiciones</p>
      </div>
      
      <div style="background-color: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
        <h2 style="color: #1e40af; margin-top: 0;">¡Hola ${name}!</h2>
        <p style="color: #374151; line-height: 1.6;">
          Tu cuenta gratuita de OposiAI ha sido creada exitosamente. Ya puedes empezar a usar 
          todas las funcionalidades disponibles en tu plan.
        </p>
      </div>
      
      <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 25px;">
        <h3 style="color: #92400e; margin-top: 0;">📅 Tu cuenta gratuita expira el:</h3>
        <p style="color: #92400e; font-size: 18px; font-weight: bold; margin-bottom: 10px;">
          ${expirationDate}
        </p>
        <p style="color: #92400e; font-size: 14px; margin-bottom: 0;">
          Tienes 5 días completos para explorar todas las funcionalidades.
        </p>
      </div>
      
      <div style="margin-bottom: 25px;">
        <h3 style="color: #1f2937;">🎯 ¿Qué puedes hacer con tu cuenta gratuita?</h3>
        <ul style="color: #374151; line-height: 1.8;">
          <li>📄 Subir hasta 1 documento de estudio</li>
          <li>❓ Generar hasta 10 preguntas de test</li>
          <li>🃏 Crear hasta 10 flashcards</li>
          <li>🗺️ Generar hasta 2 mapas mentales</li>
          <li>🤖 Usar hasta 50,000 tokens de IA</li>
        </ul>
      </div>
      
      <div style="text-align: center; margin-bottom: 25px;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/app" 
           style="display: inline-block; background-color: #2563eb; color: white; padding: 15px 30px; 
                  text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px;">
          🚀 Comenzar a usar OposiAI
        </a>
      </div>
      
      <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
        <h3 style="color: #1f2937; margin-top: 0;">💡 Consejos para aprovechar al máximo tu cuenta:</h3>
        <ol style="color: #374151; line-height: 1.6;">
          <li>Sube tu documento más importante primero</li>
          <li>Genera tests para evaluar tu conocimiento</li>
          <li>Usa flashcards para memorizar conceptos clave</li>
          <li>Crea mapas mentales para visualizar la información</li>
        </ol>
      </div>
      
      <div style="text-align: center; padding-top: 20px; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px; margin-bottom: 10px;">
          ¿Quieres más funcionalidades? Descubre nuestros planes premium
        </p>
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/payment" 
           style="color: #2563eb; text-decoration: none; font-weight: bold;">
          Ver Planes Premium →
        </a>
      </div>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center;">
        <p style="color: #6b7280; font-size: 12px;">
          Este email fue generado automáticamente por OposiAI.<br>
          Si tienes alguna pregunta, contacta con nuestro soporte.
        </p>
      </div>
    </div>
  `;
}

/**
 * Generar HTML para notificación de administrador
 */
function generateAdminNotification(email: string, name: string | undefined, expiresAt: string): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2563eb;">🆓 Nueva Cuenta Gratuita Creada</h2>
      
      <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #1e40af;">Detalles del Usuario</h3>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Nombre:</strong> ${name || 'No proporcionado'}</p>
        <p><strong>Plan:</strong> Gratuito (5 días)</p>
        <p><strong>Expira:</strong> ${new Date(expiresAt).toLocaleString('es-ES')}</p>
        <p><strong>Fecha de registro:</strong> ${new Date().toLocaleString('es-ES')}</p>
      </div>

      <div style="background-color: #dcfce7; padding: 15px; border-radius: 8px; border-left: 4px solid #16a34a;">
        <h4 style="margin-top: 0; color: #166534;">✅ Cuenta Creada Automáticamente</h4>
        <p style="margin-bottom: 0; color: #166534;">
          La cuenta fue creada automáticamente por el sistema. No se requiere acción manual.
        </p>
      </div>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          Este email fue generado automáticamente por el sistema de registro gratuito de OposiAI.
        </p>
      </div>
    </div>
  `;
}
