// src/app/api/test-email/route.ts
// Endpoint para probar el envío de correos

import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Iniciando test de email...');
    
    // Validar que tenemos la API key
    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json({
        error: 'RESEND_API_KEY no configurada'
      }, { status: 500 });
    }
    
    const body = await request.json();
    const { email } = body;
    
    if (!email) {
      return NextResponse.json({
        error: 'Email es requerido'
      }, { status: 400 });
    }
    
    console.log('📧 Enviando email de prueba a:', email);
    
    const testEmailData = {
      from: 'OposiAI Test <<EMAIL>>',
      to: [email],
      subject: '🧪 Test de Email - OposiAI',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">🧪 Test de Email Exitoso</h1>
          <p>Este es un email de prueba para verificar que el sistema de correos funciona correctamente.</p>
          <div style="background-color: #dcfce7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="color: #166534; margin: 0;">
              ✅ Si recibes este email, significa que la configuración de Resend está funcionando.
            </p>
          </div>
          <p style="color: #6b7280; font-size: 14px;">
            Enviado el: ${new Date().toLocaleString('es-ES')}<br>
            Desde: Sistema de pruebas de OposiAI
          </p>
        </div>
      `
    };
    
    const result = await resend.emails.send(testEmailData);
    
    console.log('✅ Email de prueba enviado exitosamente:', result.data?.id);
    
    return NextResponse.json({
      success: true,
      message: 'Email de prueba enviado exitosamente',
      emailId: result.data?.id,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error enviando email de prueba:', error);
    
    return NextResponse.json({
      error: 'Error enviando email de prueba',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}

// También permitir GET para pruebas rápidas
export async function GET() {
  return NextResponse.json({
    message: 'Endpoint de prueba de email',
    usage: 'POST con { "email": "<EMAIL>" }',
    configured: !!process.env.RESEND_API_KEY
  });
}
