// src/app/api/admin/email-failures/route.ts
// Endpoint para administradores para gestionar notificaciones fallidas

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { EmailNotificationService } from '@/lib/services/email/emailNotificationService';

// Lista de emails de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Agregar más emails de administradores aquí
];

// GET: Obtener estadísticas de fallos
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación de administrador
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
      return NextResponse.json({
        error: 'Acceso denegado. Solo administradores pueden ver estas estadísticas.'
      }, { status: 403 });
    }

    // Obtener parámetros de consulta
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Si no se especifican fechas, usar últimos 7 días
    const defaultStartDate = startDate || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const defaultEndDate = endDate || new Date().toISOString();

    console.log(`📊 Obteniendo estadísticas de fallos desde ${defaultStartDate} hasta ${defaultEndDate}`);

    // Obtener estadísticas de fallos
    const failureStats = await EmailNotificationService.getFailureStats(
      defaultStartDate,
      defaultEndDate
    );

    return NextResponse.json({
      success: true,
      data: {
        period: {
          startDate: defaultStartDate,
          endDate: defaultEndDate
        },
        failures: failureStats,
        recommendations: generateRecommendations(failureStats)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error obteniendo estadísticas de fallos:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}

// POST: Reintentar notificaciones fallidas
export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación de administrador
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
      return NextResponse.json({
        error: 'Acceso denegado. Solo administradores pueden ejecutar esta acción.'
      }, { status: 403 });
    }

    // Obtener parámetros del body
    const body = await request.json().catch(() => ({}));
    const maxAge = body.maxAge || 24; // Máximo 24 horas por defecto
    const limit = body.limit || 10;   // Máximo 10 reintentos por defecto

    console.log(`🔄 Iniciando reintentos de notificaciones fallidas (maxAge: ${maxAge}h, limit: ${limit})`);

    // Ejecutar reintentos
    const result = await EmailNotificationService.retryFailedNotifications(maxAge, limit);

    return NextResponse.json({
      success: true,
      message: 'Reintentos de notificaciones completados',
      result: {
        attempted: result.attempted,
        successful: result.successful,
        failed: result.failed,
        successRate: result.attempted > 0 ? 
          `${Math.round((result.successful / result.attempted) * 100)}%` : '0%',
        errors: result.errors
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error en reintentos de notificaciones:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}

// Función auxiliar para generar recomendaciones basadas en estadísticas
function generateRecommendations(failureStats: any): string[] {
  const recommendations: string[] = [];

  if (failureStats.failureRate > 10) {
    recommendations.push('⚠️ Alta tasa de fallos (>10%). Revisar configuración del proveedor de email.');
  }

  if (failureStats.errorsByType['Network Error'] > 0) {
    recommendations.push('🌐 Errores de red detectados. Verificar conectividad y timeouts.');
  }

  if (failureStats.errorsByType['Rate Limit'] > 0) {
    recommendations.push('🚦 Límites de tasa alcanzados. Considerar espaciar más los envíos.');
  }

  if (failureStats.errorsByType['Authentication Error'] > 0) {
    recommendations.push('🔑 Errores de autenticación. Verificar API keys del proveedor.');
  }

  if (failureStats.errorsByType['Invalid Email'] > 0) {
    recommendations.push('📧 Emails inválidos detectados. Implementar validación más estricta.');
  }

  if (failureStats.errorsByType['Email Bounced'] > 0) {
    recommendations.push('↩️ Emails rebotados. Considerar lista de supresión automática.');
  }

  if (failureStats.totalFailures === 0) {
    recommendations.push('✅ No se detectaron fallos recientes. Sistema funcionando correctamente.');
  }

  return recommendations;
}
