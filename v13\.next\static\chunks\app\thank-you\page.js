/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/thank-you/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ "(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js");
/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv13%5C%5Csrc%5C%5Capp%5C%5Cthank-you%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv13%5C%5Csrc%5C%5Capp%5C%5Cthank-you%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/thank-you/page.tsx */ \"(app-pages-browser)/./src/app/thank-you/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUN0aGFuay15b3UlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUE2SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjEzXFxcXHNyY1xcXFxhcHBcXFxcdGhhbmsteW91XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv13%5C%5Csrc%5C%5Capp%5C%5Cthank-you%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nvar _defineProperty = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js\");\nvar _objectWithoutProperties = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/objectWithoutProperties.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/objectWithoutProperties.js\");\nvar _slicedToArray = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/slicedToArray.js\");\nvar _s1 = $RefreshSig$();\nvar _excluded = [\n    \"href\",\n    \"as\",\n    \"children\",\n    \"prefetch\",\n    \"passHref\",\n    \"replace\",\n    \"shallow\",\n    \"scroll\",\n    \"onClick\",\n    \"onMouseEnter\",\n    \"onTouchStart\",\n    \"legacyBehavior\",\n    \"onNavigate\",\n    \"ref\",\n    \"unstable_dynamicOnHover\"\n];\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n  * A React component that extends the HTML `<a>` element to provide\n  * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n  * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n  *\n  * @remarks\n  * - Prefetching is only enabled in production.\n  *\n  * @see https://nextjs.org/docs/app/api-reference/components/link\n  */ \"default\": function _default() {\n        return LinkComponent;\n    },\n    useLinkStatus: function useLinkStatus() {\n        return _useLinkStatus;\n    }\n});\nvar _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nvar _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nvar _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nvar _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nvar _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nvar _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nvar _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nvar _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nvar _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nvar _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nvar _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nvar _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nvar _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nvar _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    var eventTarget = event.currentTarget;\n    var target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    var nodeName = e.currentTarget.nodeName;\n    // anchors inside an svg have a lowercase nodeName\n    var isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    var navigate = function navigate() {\n        if (onNavigate) {\n            var isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: function preventDefault() {\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react[\"default\"].startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    _s1();\n    var _ref = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS), _ref2 = _slicedToArray(_ref, 2), linkStatus = _ref2[0], setOptimisticLinkStatus = _ref2[1];\n    var children;\n    var linkInstanceRef = (0, _react.useRef)(null);\n    var hrefProp = props.href, asProp = props.as, childrenProp = props.children, _props$prefetch = props.prefetch, prefetchProp = _props$prefetch === void 0 ? null : _props$prefetch, passHref = props.passHref, replace = props.replace, shallow = props.shallow, scroll = props.scroll, _onClick = props.onClick, onMouseEnterProp = props.onMouseEnter, onTouchStartProp = props.onTouchStart, _props$legacyBehavior = props.legacyBehavior, legacyBehavior = _props$legacyBehavior === void 0 ? false : _props$legacyBehavior, onNavigate = props.onNavigate, forwardedRef = props.ref, unstable_dynamicOnHover = props.unstable_dynamicOnHover, restProps = _objectWithoutProperties(props, _excluded);\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    var router = _react[\"default\"].useContext(_approutercontextsharedruntime.AppRouterContext);\n    var prefetchEnabled = prefetchProp !== false;\n    /**\n  * The possible states for prefetch are:\n  * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n  * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n  * - false: we will not prefetch if in the viewport at all\n  * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n  */ var appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        var createPropError = function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }; // TypeScript trick for type-guarding:\n        var requiredPropsGuard = {\n            href: true\n        };\n        var requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach(function(key) {\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key: key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                var _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        var optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        var optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach(function(key) {\n            var valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key: key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key: key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key: key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                var _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            var _href;\n            if (typeof hrefProp === 'string') {\n                _href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                _href = hrefProp.pathname;\n            }\n            if (_href) {\n                var hasDynamicSegment = _href.split('/').some(function(segment) {\n                    return segment.startsWith('[') && segment.endsWith(']');\n                });\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + _href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    var _react$default$useMem = _react[\"default\"].useMemo({\n        \"LinkComponent.useMemo[_react$default$useMem]\": function() {\n            var resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo[_react$default$useMem]\"], [\n        hrefProp,\n        asProp\n    ]), href = _react$default$useMem.href, as = _react$default$useMem.as;\n    // This will return the first child, if multiple are provided it will throw an error\n    var child;\n    if (legacyBehavior) {\n        if (true) {\n            if (_onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react[\"default\"].Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    var childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    var observeLinkVisibilityOnMount = _react[\"default\"].useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": function(element) {\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": function() {\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    var mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    var childProps = {\n        ref: mergedRef,\n        onClick: function onClick(e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof _onClick === 'function') {\n                _onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter: function onMouseEnter(e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || true) {\n                return;\n            }\n            var upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            var upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    var link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react[\"default\"].cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", _objectSpread(_objectSpread(_objectSpread({}, restProps), childProps), {}, {\n            children: children\n        }));\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"7gIwaCt/xViM4SOBJIu91NMRbC0=\");\n_c1 = LinkComponent;\n_s1(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nvar LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nvar _useLinkStatus = function _useLinkStatus() {\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n    Object.defineProperty(exports[\"default\"], '__esModule', {\n        value: true\n    });\n    Object.assign(exports[\"default\"], exports);\n    module.exports = exports[\"default\"];\n}\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\nvar _c1;\n$RefreshReg$(_c1, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function get() {\n        return useMergedRef;\n    }\n}));\nvar _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    var cleanupA = (0, _react.useRef)(null);\n    var cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)(function(current) {\n        if (current === null) {\n            var cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            var cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        var cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return function() {\n                return refA(null);\n            };\n        }\n    } else {\n        refA.current = current;\n        return function() {\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n    Object.defineProperty(exports[\"default\"], '__esModule', {\n        value: true\n    });\n    Object.assign(exports[\"default\"], exports);\n    module.exports = exports[\"default\"];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _asyncToGenerator)\n/* harmony export */ });\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(undefined);\n    });\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxM1xcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function formatUrl() {\n        return _formatUrl;\n    },\n    formatWithValidation: function formatWithValidation() {\n        return _formatWithValidation;\n    },\n    urlObjectKeys: function urlObjectKeys() {\n        return _urlObjectKeys;\n    }\n});\nvar _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nvar _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nvar slashedProtocols = /https?|ftp|gopher|file/;\nfunction _formatUrl(urlObj) {\n    var auth = urlObj.auth, hostname = urlObj.hostname;\n    var protocol = urlObj.protocol || '';\n    var pathname = urlObj.pathname || '';\n    var hash = urlObj.hash || '';\n    var query = urlObj.query || '';\n    var host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    var search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nvar _urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction _formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach(function(key) {\n                if (!_urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return _formatUrl(url);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function get() {\n        return isLocalURL;\n    }\n}));\nvar _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nvar _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        var locationOrigin = (0, _utils.getLocationOrigin)();\n        var resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \***********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nvar _slicedToArray = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/slicedToArray.js\");\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n    if (!it) {\n        if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n            if (it) o = it;\n            var i = 0;\n            var F = function F() {};\n            return {\n                s: F,\n                n: function n() {\n                    if (i >= o.length) return {\n                        done: true\n                    };\n                    return {\n                        done: false,\n                        value: o[i++]\n                    };\n                },\n                e: function e(_e) {\n                    throw _e;\n                },\n                f: F\n            };\n        }\n        throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true, didErr = false, err;\n    return {\n        s: function s() {\n            it = it.call(o);\n        },\n        n: function n() {\n            var step = it.next();\n            normalCompletion = step.done;\n            return step;\n        },\n        e: function e(_e2) {\n            didErr = true;\n            err = _e2;\n        },\n        f: function f() {\n            try {\n                if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n            } finally{\n                if (didErr) throw err;\n            }\n        }\n    };\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function assign() {\n        return _assign;\n    },\n    searchParamsToUrlQuery: function searchParamsToUrlQuery() {\n        return _searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function urlQueryToSearchParams() {\n        return _urlQueryToSearchParams;\n    }\n});\nfunction _searchParamsToUrlQuery(searchParams) {\n    var query = {};\n    var _iterator = _createForOfIteratorHelper(searchParams.entries()), _step;\n    try {\n        for(_iterator.s(); !(_step = _iterator.n()).done;){\n            var _step$value = _slicedToArray(_step.value, 2), key = _step$value[0], value = _step$value[1];\n            var existing = query[key];\n            if (typeof existing === 'undefined') {\n                query[key] = value;\n            } else if (Array.isArray(existing)) {\n                existing.push(value);\n            } else {\n                query[key] = [\n                    existing,\n                    value\n                ];\n            }\n        }\n    } catch (err) {\n        _iterator.e(err);\n    } finally{\n        _iterator.f();\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction _urlQueryToSearchParams(query) {\n    var searchParams = new URLSearchParams();\n    for(var _i = 0, _Object$entries = Object.entries(query); _i < _Object$entries.length; _i++){\n        var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2), key = _Object$entries$_i[0], value = _Object$entries$_i[1];\n        if (Array.isArray(value)) {\n            var _iterator2 = _createForOfIteratorHelper(value), _step2;\n            try {\n                for(_iterator2.s(); !(_step2 = _iterator2.n()).done;){\n                    var item = _step2.value;\n                    searchParams.append(key, stringifyUrlQueryParam(item));\n                }\n            } catch (err) {\n                _iterator2.e(err);\n            } finally{\n                _iterator2.f();\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction _assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for(var _i2 = 0, _searchParamsList = searchParamsList; _i2 < _searchParamsList.length; _i2++){\n        var searchParams = _searchParamsList[_i2];\n        var _iterator3 = _createForOfIteratorHelper(searchParams.keys()), _step3;\n        try {\n            for(_iterator3.s(); !(_step3 = _iterator3.n()).done;){\n                var key = _step3.value;\n                target[\"delete\"](key);\n            }\n        } catch (err) {\n            _iterator3.e(err);\n        } finally{\n            _iterator3.f();\n        }\n        var _iterator4 = _createForOfIteratorHelper(searchParams.entries()), _step4;\n        try {\n            for(_iterator4.s(); !(_step4 = _iterator4.n()).done;){\n                var _step4$value = _slicedToArray(_step4.value, 2), _key2 = _step4$value[0], value = _step4$value[1];\n                target.append(_key2, value);\n            }\n        } catch (err) {\n            _iterator4.e(err);\n        } finally{\n            _iterator4.f();\n        }\n    }\n    return target;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nvar _regeneratorRuntime = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\nvar _createClass = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js\");\nvar _classCallCheck = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js\");\nvar _inherits = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js\");\nvar _possibleConstructorReturn = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js\");\nvar _getPrototypeOf = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js\");\nvar _wrapNativeSuper = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/wrapNativeSuper.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/wrapNativeSuper.js\");\nvar _asyncToGenerator = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/asyncToGenerator.js\");\nfunction _createSuper(Derived) {\n    var hasNativeReflectConstruct = _isNativeReflectConstruct();\n    return function _createSuperInternal() {\n        var Super = _getPrototypeOf(Derived), result;\n        if (hasNativeReflectConstruct) {\n            var NewTarget = _getPrototypeOf(this).constructor;\n            result = Reflect.construct(Super, arguments, NewTarget);\n        } else {\n            result = Super.apply(this, arguments);\n        }\n        return _possibleConstructorReturn(this, result);\n    };\n}\nfunction _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function DecodeError() {\n        return _DecodeError;\n    },\n    MiddlewareNotFoundError: function MiddlewareNotFoundError() {\n        return _MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function MissingStaticPage() {\n        return _MissingStaticPage;\n    },\n    NormalizeError: function NormalizeError() {\n        return _NormalizeError;\n    },\n    PageNotFoundError: function PageNotFoundError() {\n        return _PageNotFoundError;\n    },\n    SP: function SP() {\n        return _SP;\n    },\n    ST: function ST() {\n        return _ST;\n    },\n    WEB_VITALS: function WEB_VITALS() {\n        return _WEB_VITALS;\n    },\n    execOnce: function execOnce() {\n        return _execOnce;\n    },\n    getDisplayName: function getDisplayName() {\n        return _getDisplayName;\n    },\n    getLocationOrigin: function getLocationOrigin() {\n        return _getLocationOrigin;\n    },\n    getURL: function getURL() {\n        return _getURL;\n    },\n    isAbsoluteUrl: function isAbsoluteUrl() {\n        return _isAbsoluteUrl;\n    },\n    isResSent: function isResSent() {\n        return _isResSent;\n    },\n    loadGetInitialProps: function loadGetInitialProps() {\n        return _loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function normalizeRepeatedSlashes() {\n        return _normalizeRepeatedSlashes;\n    },\n    stringifyError: function stringifyError() {\n        return _stringifyError;\n    }\n});\nvar _WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction _execOnce(fn) {\n    var used = false;\n    var result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn.apply(void 0, args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nvar ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nvar _isAbsoluteUrl = function _isAbsoluteUrl(url) {\n    return ABSOLUTE_URL_REGEX.test(url);\n};\nfunction _getLocationOrigin() {\n    var _window$location = window.location, protocol = _window$location.protocol, hostname = _window$location.hostname, port = _window$location.port;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction _getURL() {\n    var href = window.location.href;\n    var origin = _getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction _getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction _isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction _normalizeRepeatedSlashes(url) {\n    var urlParts = url.split('?');\n    var urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nfunction _loadGetInitialProps(_x, _x2) {\n    return _loadGetInitialProps2.apply(this, arguments);\n}\nfunction _loadGetInitialProps2() {\n    _loadGetInitialProps2 = _asyncToGenerator(/*#__PURE__*/ _regeneratorRuntime.mark(function _callee(App, ctx) {\n        var _App_prototype, message, res, props, _message;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n            while(1)switch(_context.prev = _context.next){\n                case 0:\n                    if (false) {}\n                    if (!((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps)) {\n                        _context.next = 4;\n                        break;\n                    }\n                    message = '\"' + _getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n                    throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                        value: \"E394\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 4:\n                    // when called from _app `ctx` is nested in `ctx`\n                    res = ctx.res || ctx.ctx && ctx.ctx.res;\n                    if (App.getInitialProps) {\n                        _context.next = 12;\n                        break;\n                    }\n                    if (!(ctx.ctx && ctx.Component)) {\n                        _context.next = 11;\n                        break;\n                    }\n                    _context.next = 9;\n                    return _loadGetInitialProps(ctx.Component, ctx.ctx);\n                case 9:\n                    _context.t0 = _context.sent;\n                    return _context.abrupt(\"return\", {\n                        pageProps: _context.t0\n                    });\n                case 11:\n                    return _context.abrupt(\"return\", {});\n                case 12:\n                    _context.next = 14;\n                    return App.getInitialProps(ctx);\n                case 14:\n                    props = _context.sent;\n                    if (!(res && _isResSent(res))) {\n                        _context.next = 17;\n                        break;\n                    }\n                    return _context.abrupt(\"return\", props);\n                case 17:\n                    if (props) {\n                        _context.next = 20;\n                        break;\n                    }\n                    _message = '\"' + _getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n                    throw Object.defineProperty(new Error(_message), \"__NEXT_ERROR_CODE\", {\n                        value: \"E394\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                case 20:\n                    if (true) {\n                        if (Object.keys(props).length === 0 && !ctx.ctx) {\n                            console.warn(\"\" + _getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n                        }\n                    }\n                    return _context.abrupt(\"return\", props);\n                case 22:\n                case \"end\":\n                    return _context.stop();\n            }\n        }, _callee);\n    }));\n    return _loadGetInitialProps2.apply(this, arguments);\n}\nvar _SP = typeof performance !== 'undefined';\nvar _ST = _SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every(function(method) {\n    return typeof performance[method] === 'function';\n});\nvar _DecodeError = /*#__PURE__*/ function(_Error) {\n    _inherits(_DecodeError, _Error);\n    var _super = _createSuper(_DecodeError);\n    function _DecodeError() {\n        _classCallCheck(this, _DecodeError);\n        return _super.apply(this, arguments);\n    }\n    return _createClass(_DecodeError);\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nvar _NormalizeError = /*#__PURE__*/ function(_Error2) {\n    _inherits(_NormalizeError, _Error2);\n    var _super2 = _createSuper(_NormalizeError);\n    function _NormalizeError() {\n        _classCallCheck(this, _NormalizeError);\n        return _super2.apply(this, arguments);\n    }\n    return _createClass(_NormalizeError);\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nvar _PageNotFoundError = /*#__PURE__*/ function(_Error3) {\n    _inherits(_PageNotFoundError, _Error3);\n    var _super3 = _createSuper(_PageNotFoundError);\n    function _PageNotFoundError(page) {\n        var _this;\n        _classCallCheck(this, _PageNotFoundError);\n        _this = _super3.call(this);\n        _this.code = 'ENOENT';\n        _this.name = 'PageNotFoundError';\n        _this.message = \"Cannot find module for page: \" + page;\n        return _this;\n    }\n    return _createClass(_PageNotFoundError);\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nvar _MissingStaticPage = /*#__PURE__*/ function(_Error4) {\n    _inherits(_MissingStaticPage, _Error4);\n    var _super4 = _createSuper(_MissingStaticPage);\n    function _MissingStaticPage(page, message) {\n        var _this2;\n        _classCallCheck(this, _MissingStaticPage);\n        _this2 = _super4.call(this);\n        _this2.message = \"Failed to load static file for page: \" + page + \" \" + message;\n        return _this2;\n    }\n    return _createClass(_MissingStaticPage);\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nvar _MiddlewareNotFoundError = /*#__PURE__*/ function(_Error5) {\n    _inherits(_MiddlewareNotFoundError, _Error5);\n    var _super5 = _createSuper(_MiddlewareNotFoundError);\n    function _MiddlewareNotFoundError() {\n        var _this3;\n        _classCallCheck(this, _MiddlewareNotFoundError);\n        _this3 = _super5.call(this);\n        _this3.code = 'ENOENT';\n        _this3.message = \"Cannot find the middleware module\";\n        return _this3;\n    }\n    return _createClass(_MiddlewareNotFoundError);\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nfunction _stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function get() {\n        return errorOnce;\n    }\n}));\nvar errorOnce = function errorOnce(_) {};\nif (true) {\n    var errors = new Set();\n    errorOnce = function errorOnce(msg) {\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUN6Q0csS0FBSyxFQUFFO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILDZDQUE0QztJQUN4Q0ksVUFBVSxFQUFFLElBQUk7SUFDaEJDLEdBQUcsRUFBRSxTQUFBQSxJQUFBLEVBQVc7UUFDWixPQUFPQyxTQUFTO0lBQ3BCO0FBQ0osQ0FBQyxFQUFDO0FBQ0YsSUFBSUEsU0FBUyxHQUFHLFNBQUFBLFVBQUNDLENBQUMsRUFBRyxDQUFDLENBQUM7QUFDdkIsVUFBMkM7SUFDdkMsSUFBTUMsTUFBTSxHQUFHLElBQUlDLEdBQUcsQ0FBQyxDQUFDO0lBQ3hCSCxTQUFTLEdBQUcsU0FBQUEsVUFBQ0ksR0FBRyxFQUFHO1FBQ2YsSUFBSSxDQUFDRixNQUFNLENBQUNHLEdBQUcsQ0FBQ0QsR0FBRyxDQUFDLEVBQUU7WUFDbEJFLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDSCxHQUFHLENBQUM7UUFDdEI7UUFDQUYsTUFBTSxDQUFDTSxHQUFHLENBQUNKLEdBQUcsQ0FBQztJQUNuQixDQUFDO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxM1xcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzaGFyZWRcXGxpYlxcdXRpbHNcXGVycm9yLW9uY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJlcnJvck9uY2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGVycm9yT25jZTtcbiAgICB9XG59KTtcbmxldCBlcnJvck9uY2UgPSAoXyk9Pnt9O1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICBjb25zdCBlcnJvcnMgPSBuZXcgU2V0KCk7XG4gICAgZXJyb3JPbmNlID0gKG1zZyk9PntcbiAgICAgICAgaWYgKCFlcnJvcnMuaGFzKG1zZykpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IobXNnKTtcbiAgICAgICAgfVxuICAgICAgICBlcnJvcnMuYWRkKG1zZyk7XG4gICAgfTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXJyb3Itb25jZS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiZXJyb3JPbmNlIiwiXyIsImVycm9ycyIsIlNldCIsIm1zZyIsImhhcyIsImNvbnNvbGUiLCJlcnJvciIsImFkZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/thank-you/page.tsx":
/*!************************************!*\
  !*** ./src/app/thank-you/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThankYouPage)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/stripe/plans */ \"(app-pages-browser)/./src/lib/stripe/plans.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n// src/app/thank-you/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v13\\\\src\\\\app\\\\thank-you\\\\page.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ThankYouContent() {\n    _s();\n    _s1();\n    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    var planId = searchParams.get('plan') || 'free';\n    var sessionId = searchParams.get('session_id');\n    var isManual = searchParams.get('manual') === 'true';\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('checking'), userStatus = _useState[0], setUserStatus = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), accountInfo = _useState2[0], setAccountInfo = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), timeElapsed = _useState3[0], setTimeElapsed = _useState3[1];\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isReactivating = _useState4[0], setIsReactivating = _useState4[1];\n    var plan = (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_4__.getPlanById)(planId);\n    // Verificación de estado del usuario\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ThankYouContent.useEffect\": function() {\n            if (!sessionId) {\n                setUserStatus('error');\n                return;\n            }\n            var checkAccountStatus = /*#__PURE__*/ ({\n                \"ThankYouContent.useEffect.checkAccountStatus\": function() {\n                    var _ref = (0,C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee() {\n                        var response, data;\n                        return C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                            while(1)switch(_context.prev = _context.next){\n                                case 0:\n                                    _context.prev = 0;\n                                    if (!(planId === 'free')) {\n                                        _context.next = 9;\n                                        break;\n                                    }\n                                    if (!isManual) {\n                                        _context.next = 5;\n                                        break;\n                                    }\n                                    // Modo manual: mostrar mensaje de espera\n                                    setUserStatus('manual_pending');\n                                    return _context.abrupt(\"return\");\n                                case 5:\n                                    // Para cuentas gratuitas automatizadas, asumir que están listas inmediatamente\n                                    setUserStatus('ready');\n                                    setAccountInfo({\n                                        id: sessionId,\n                                        email: '<EMAIL>',\n                                        // Se actualizará en welcome\n                                        plan: 'free',\n                                        paymentVerified: true,\n                                        hasTemporaryPassword: true,\n                                        loginUrl: '/login'\n                                    });\n                                    // Redirección automática tras 2 segundos para plan gratuito\n                                    setTimeout({\n                                        \"ThankYouContent.useEffect.checkAccountStatus._ref._callee._callee$\": function() {\n                                            router.push(\"/welcome?plan=\".concat(planId, \"&new_user=true\"));\n                                        }\n                                    }[\"ThankYouContent.useEffect.checkAccountStatus._ref._callee._callee$\"], 2000);\n                                    return _context.abrupt(\"return\");\n                                case 9:\n                                    _context.next = 11;\n                                    return fetch(\"/api/user/status?session_id=\".concat(sessionId));\n                                case 11:\n                                    response = _context.sent;\n                                    _context.next = 14;\n                                    return response.json();\n                                case 14:\n                                    data = _context.sent;\n                                    if (data.ready) {\n                                        setUserStatus('ready');\n                                        setAccountInfo(data.user);\n                                        // Redirección automática tras 3 segundos\n                                        setTimeout({\n                                            \"ThankYouContent.useEffect.checkAccountStatus._ref._callee._callee$\": function() {\n                                                router.push(\"/welcome?plan=\".concat(planId, \"&new_user=true\"));\n                                            }\n                                        }[\"ThankYouContent.useEffect.checkAccountStatus._ref._callee._callee$\"], 3000);\n                                    } else if (timeElapsed >= 90) {\n                                        // Timeout inteligente para detectar webhook fallido\n                                        setUserStatus('webhook_failed');\n                                    } else if (timeElapsed >= 60) {\n                                        setUserStatus('timeout');\n                                    }\n                                    _context.next = 22;\n                                    break;\n                                case 18:\n                                    _context.prev = 18;\n                                    _context.t0 = _context[\"catch\"](0);\n                                    console.error('Error verificando estado:', _context.t0);\n                                    if (timeElapsed >= 30) {\n                                        setUserStatus('error');\n                                    }\n                                case 22:\n                                case \"end\":\n                                    return _context.stop();\n                            }\n                        }, _callee, null, [\n                            [\n                                0,\n                                18\n                            ]\n                        ]);\n                    }));\n                    return function checkAccountStatus() {\n                        return _ref.apply(this, arguments);\n                    };\n                }\n            })[\"ThankYouContent.useEffect.checkAccountStatus\"]();\n            // Verificación inicial inmediata\n            checkAccountStatus();\n            // Para plan gratuito, no necesitamos polling\n            if (planId === 'free') {\n                return;\n            }\n            // Polling cada 3 segundos solo para planes de pago\n            var interval = setInterval({\n                \"ThankYouContent.useEffect.interval\": function() {\n                    setTimeElapsed({\n                        \"ThankYouContent.useEffect.interval\": function(prev) {\n                            return prev + 3;\n                        }\n                    }[\"ThankYouContent.useEffect.interval\"]);\n                    if (userStatus === 'checking') {\n                        checkAccountStatus();\n                    }\n                }\n            }[\"ThankYouContent.useEffect.interval\"], 3000);\n            return ({\n                \"ThankYouContent.useEffect\": function() {\n                    return clearInterval(interval);\n                }\n            })[\"ThankYouContent.useEffect\"];\n        }\n    }[\"ThankYouContent.useEffect\"], [\n        sessionId,\n        userStatus,\n        timeElapsed,\n        planId,\n        router\n    ]);\n    // Función para reactivar cuenta\n    var handleReactivateAccount = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2() {\n            var response;\n            return C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee2$(_context2) {\n                while(1)switch(_context2.prev = _context2.next){\n                    case 0:\n                        setIsReactivating(true);\n                        _context2.prev = 1;\n                        _context2.next = 4;\n                        return fetch('/api/user/reactivate', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                sessionId: sessionId\n                            })\n                        });\n                    case 4:\n                        response = _context2.sent;\n                        if (response.ok) {\n                            setUserStatus('checking');\n                            setTimeElapsed(0);\n                        } else {\n                            console.error('Error reactivando cuenta');\n                        }\n                        _context2.next = 11;\n                        break;\n                    case 8:\n                        _context2.prev = 8;\n                        _context2.t0 = _context2[\"catch\"](1);\n                        console.error('Error reactivando cuenta:', _context2.t0);\n                    case 11:\n                        _context2.prev = 11;\n                        setIsReactivating(false);\n                        return _context2.finish(11);\n                    case 14:\n                    case \"end\":\n                        return _context2.stop();\n                }\n            }, _callee2, null, [\n                [\n                    1,\n                    8,\n                    11,\n                    14\n                ]\n            ]);\n        }));\n        return function handleReactivateAccount() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    // Estado: Verificando\n    if (userStatus === 'checking') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-extrabold text-gray-900 mb-4\",\n                                children: planId === 'free' ? '¡Cuenta Gratuita Creada!' : '¡Pago Confirmado!'\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6\",\n                                children: planId === 'free' ? \"Tu cuenta gratuita de <strong>\".concat(plan === null || plan === void 0 ? void 0 : plan.name, \"</strong> ha sido creada exitosamente\") : \"Tu cuenta para el <strong>\".concat(plan === null || plan === void 0 ? void 0 : plan.name, \"</strong> se est\\xE1 creando autom\\xE1ticamente\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-800 text-sm\",\n                                    children: planId === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83C\\uDF89 \\xA1Listo para empezar!\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 163,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 163,\n                                                columnNumber: 63\n                                            }, this),\n                                            \"Tu cuenta gratuita est\\xE1 activa por 5 d\\xEDas. Puedes comenzar a usar OposiAI inmediatamente.\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83E\\uDD16 Proceso autom\\xE1tico en curso\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 168,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 168,\n                                                columnNumber: 70\n                                            }, this),\n                                            \"No necesitas hacer nada m\\xE1s. Tu cuenta estar\\xE1 lista en unos segundos.\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Tiempo transcurrido: \",\n                                    timeElapsed,\n                                    \" segundos\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    // Estado: Listo\n    if (userStatus === 'ready' && accountInfo) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6 text-green-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-extrabold text-gray-900 mb-4\",\n                                children: \"\\xA1Cuenta Creada Exitosamente!\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6\",\n                                children: \"Tu cuenta est\\xE1 lista para usar\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-green-800 mb-2\",\n                                        children: \"\\xA1Tu cuenta est\\xE1 lista!\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                        className: \"text-green-700 text-sm mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                                children: \"Email de acceso:\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            accountInfo.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-800 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                                    children: \"\\uD83D\\uDCE7 Revisa tu email\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 213,\n                                                    columnNumber: 56\n                                                }, this),\n                                                \"Te hemos enviado un enlace para establecer tu contrase\\xF1a. Revisa tu bandeja de entrada (y spam) para completar la configuraci\\xF3n de tu cuenta.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-4\",\n                                children: \"Redirigiendo autom\\xE1ticamente en 3 segundos...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/welcome\",\n                                className: \"inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n                                children: \"Acceder Ahora\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    // Estado: Manual Pending (plan gratuito en modo manual)\n    if (userStatus === 'manual_pending') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-orange-100 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6 text-orange-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-extrabold text-gray-900 mb-4\",\n                                children: \"\\xA1Solicitud Recibida!\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6\",\n                                children: [\n                                    \"Tu solicitud para el \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                        children: plan === null || plan === void 0 ? void 0 : plan.name\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 255,\n                                        columnNumber: 38\n                                    }, this),\n                                    \" ha sido enviada correctamente\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-orange-800 mb-2\",\n                                        children: \"\\u23F3 Activaci\\xF3n Manual\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                        className: \"text-orange-700 text-sm mb-3\",\n                                        children: \"Tu cuenta ser\\xE1 activada manualmente por nuestro equipo en las pr\\xF3ximas horas.\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-800 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                                    children: \"\\uD83D\\uDCE7 Te notificaremos por email\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 265,\n                                                    columnNumber: 67\n                                                }, this),\n                                                \"Recibir\\xE1s un email con las instrucciones de acceso una vez que tu cuenta est\\xE9 lista.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-gray-900 mb-2\",\n                                        children: \"\\uD83C\\uDFAF \\xBFQu\\xE9 incluye tu plan gratuito?\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-600 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"li\", {\n                                                children: \"\\uD83D\\uDCC4 Hasta 1 documento de estudio\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"li\", {\n                                                children: \"\\u2753 Hasta 10 preguntas de test\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"li\", {\n                                                children: \"\\uD83C\\uDCCF Hasta 10 flashcards\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"li\", {\n                                                children: \"\\uD83D\\uDDFA\\uFE0F Hasta 2 mapas mentales\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"li\", {\n                                                children: \"\\uD83E\\uDD16 50,000 tokens de IA\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"li\", {\n                                                children: \"\\u23F0 5 d\\xEDas de acceso completo\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-4\",\n                                children: \"Tiempo estimado de activaci\\xF3n: 2-6 horas\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/\",\n                                        className: \"inline-flex justify-center py-3 px-6 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n                                        children: \"Volver al Inicio\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"\\xBFNecesitas ayuda? Contacta con nosotros en\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"a\", {\n                                                href: \"mailto:<EMAIL>\",\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    // Estados: Error, Timeout, Webhook Fallido\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n            className: \"sm:mx-auto sm:w-full sm:max-w-2xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                className: \"bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-extrabold text-gray-900 mb-4\",\n                            children: userStatus === 'webhook_failed' ? 'Reactivación Necesaria' : userStatus === 'timeout' ? 'Procesando...' : 'Problema Temporal'\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 mb-6\",\n                            children: userStatus === 'webhook_failed' ? 'Tu pago fue procesado, pero necesitamos reactivar la creación de tu cuenta.' : userStatus === 'timeout' ? 'Tu cuenta se está creando. Esto puede tardar un poco más de lo normal.' : 'Hubo un problema al verificar tu cuenta.'\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"text-blue-800 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"strong\", {\n                                        children: \"Tu pago fue procesado correctamente.\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 331,\n                                        columnNumber: 70\n                                    }, this),\n                                    userStatus === 'webhook_failed' ? 'Haz clic en \"Reactivar Cuenta\" para completar el proceso.' : 'Si el problema persiste, contacta con soporte.'\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                userStatus === 'webhook_failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"button\", {\n                                    onClick: handleReactivateAccount,\n                                    disabled: isReactivating,\n                                    className: \"w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\",\n                                    children: isReactivating ? 'Reactivando...' : 'Reactivar Cuenta'\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"button\", {\n                                    onClick: function onClick() {\n                                        return window.location.reload();\n                                    },\n                                    className: \"w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"Verificar Nuevamente\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>?subject=Problema con cuenta&body=ID de Sesi\\xF3n: \".concat(sessionId),\n                                    className: \"block w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors text-center\",\n                                    children: \"Contactar Soporte\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this),\n                                sessionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-xs text-gray-500\",\n                                    children: [\n                                        \"ID de Sesi\\xF3n: \",\n                                        sessionId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 5\n    }, this);\n}\n_s(ThankYouContent, \"TQC8R0WF1Cxl6KwtTdQc5Te361Q=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = ThankYouContent;\n_s1(ThankYouContent, \"cRVuXlIX2XyOA73uzcpBrHoRZ5c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ThankYouContent;\nfunction ThankYouPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-6 shadow sm:rounded-lg sm:px-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Cargando...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 7\n        }, this),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(ThankYouContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 5\n    }, this);\n}\n_c3 = ThankYouPage;\n_c2 = ThankYouPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"ThankYouContent\");\n$RefreshReg$(_c2, \"ThankYouPage\");\nvar _c1, _c3;\n$RefreshReg$(_c1, \"ThankYouContent\");\n$RefreshReg$(_c3, \"ThankYouPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/thank-you/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nvar PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // En centavos (€10.00)\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // En centavos (€15.00)\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de Resúmenes para A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    var plan = getPlanById(planId);\n    return (plan === null || plan === void 0 ? void 0 : plan.planConfig) || null;\n}\n// Configuración de productos adicionales\nvar ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        // En centavos (€10.00)\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        // Placeholder - crear en Stripe\n        stripePriceId: 'price_tokens_additional' // Placeholder - crear en Stripe\n    }\n};\n// URLs de la aplicación\nvar APP_URLS = {\n    success: \"\".concat(\"http://localhost:3000\", \"/thank-you\"),\n    cancel: \"\".concat(\"http://localhost:3000\", \"/upgrade-plan\"),\n    webhook: \"\".concat(\"http://localhost:3000\", \"/api/stripe/webhook\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3RyaXBlL3BsYW5zLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBRTREO0FBRXJELElBQU1DLEtBQUssR0FBRztJQUNuQkMsSUFBSSxFQUFFO1FBQ0pDLEVBQUUsRUFBRSxNQUFNO1FBQ1ZDLElBQUksRUFBRSxhQUFhO1FBQ25CQyxLQUFLLEVBQUUsQ0FBQztRQUNSQyxlQUFlLEVBQUUsSUFBSTtRQUNyQkMsYUFBYSxFQUFFLElBQUk7UUFDbkJDLFFBQVEsRUFBRTtZQUNSLFVBQVU7WUFDViw0Q0FBNEM7WUFDNUMsNENBQTRDO1lBQzVDLCtDQUErQztZQUMvQyx5REFBeUQ7WUFDekQsd0RBQXdEO1lBQ3hELGFBQWE7WUFDYiw2QkFBNkI7WUFDN0IsOEJBQThCO1lBQzlCLHFCQUFxQjtTQUN0QjtRQUNEQyxNQUFNLEVBQUVULHNFQUFtQixDQUFDRSxJQUFJLENBQUNPLE1BQU07UUFDdkNDLFVBQVUsRUFBRVYsc0VBQW1CLENBQUNFLElBQUFBO0lBQ2xDLENBQUM7SUFDRFMsT0FBTyxFQUFFO1FBQ1BSLEVBQUUsRUFBRSxTQUFTO1FBQ2JDLElBQUksRUFBRSxjQUFjO1FBQ3BCQyxLQUFLLEVBQUUsSUFBSTtRQUFFO1FBQ2JDLGVBQWUsRUFBRSxxQkFBcUI7UUFDdEM7UUFDQTtRQUNBQyxhQUFhLEVBQUUsZ0NBQWdDO1FBQy9DQyxRQUFRLEVBQUU7WUFDUixVQUFVO1lBQ1YsdUdBQXVHO1lBQ3ZHLHdCQUF3QjtZQUN4QixnQ0FBZ0M7WUFDaEMsdUJBQXVCO1lBQ3ZCLDZCQUE2QjtZQUM3QixpQ0FBaUM7WUFDakMsZ0pBQWdKO1lBQ2hKLGFBQWE7WUFDYiw2QkFBNkI7WUFDN0IscUJBQXFCO1NBQ3RCO1FBQ0RDLE1BQU0sRUFBRVQsc0VBQW1CLENBQUNXLE9BQU8sQ0FBQ0YsTUFBTTtRQUMxQ0MsVUFBVSxFQUFFVixzRUFBbUIsQ0FBQ1csT0FBQUE7SUFDbEMsQ0FBQztJQUNEQyxHQUFHLEVBQUU7UUFDSFQsRUFBRSxFQUFFLEtBQUs7UUFDVEMsSUFBSSxFQUFFLFVBQVU7UUFDaEJDLEtBQUssRUFBRSxJQUFJO1FBQUU7UUFDYkMsZUFBZSxFQUFFLHFCQUFxQjtRQUN0Q0MsYUFBYSxFQUFFLGdDQUFnQztRQUMvQ0MsUUFBUSxFQUFFO1lBQ1IsVUFBVTtZQUNWLHVHQUF1RztZQUN2Ryx3QkFBd0I7WUFDeEIsMENBQTBDO1lBQzFDLGdDQUFnQztZQUNoQyx1QkFBdUI7WUFDdkIsNkJBQTZCO1lBQzdCLGlDQUFpQztZQUNqQyx3Q0FBd0M7WUFDeEMsZ0pBQWdKO1NBQ2pKO1FBQ0RDLE1BQU0sRUFBRVQsc0VBQW1CLENBQUNZLEdBQUcsQ0FBQ0gsTUFBTTtRQUN0Q0MsVUFBVSxFQUFFVixzRUFBbUIsQ0FBQ1ksR0FBQUE7SUFDbEM7QUFDRixDQUFVO0FBSVY7QUFDTyxTQUFTQyxXQUFXQSxDQUFDQyxNQUFjLEVBQStCO0lBQ3ZFLE9BQU9iLEtBQUssQ0FBQ2EsTUFBTSxDQUFXLElBQUksSUFBSTtBQUN4QztBQUVBO0FBQ08sU0FBU0MsV0FBV0EsQ0FBQ0QsTUFBYyxFQUFvQjtJQUM1RCxPQUFPQSxNQUFNLElBQUliLEtBQUs7QUFDeEI7QUFFQTtBQUNPLFNBQVNlLGlCQUFpQkEsQ0FBQ0YsTUFBYyxFQUFFO0lBQ2hELElBQU1HLElBQUksR0FBR0osV0FBVyxDQUFDQyxNQUFNLENBQUM7SUFDaEMsT0FBTyxDQUFBRyxJQUFJLGFBQUpBLElBQUksdUJBQUpBLElBQUksQ0FBRVAsVUFBQUEsS0FBYyxJQUFJO0FBQ2pDO0FBRUE7QUFDTyxJQUFNUSxtQkFBbUIsR0FBRztJQUNqQ0MsTUFBTSxFQUFFO1FBQ05oQixFQUFFLEVBQUUsUUFBUTtRQUNaQyxJQUFJLEVBQUUsb0JBQW9CO1FBQzFCZ0IsV0FBVyxFQUFFLDZDQUE2QztRQUMxRGYsS0FBSyxFQUFFLElBQUk7UUFBRTtRQUNiZ0IsV0FBVyxFQUFFLE9BQU87UUFDcEI7UUFDQWYsZUFBZSxFQUFFLHdCQUF3QjtRQUFFO1FBQzNDQyxhQUFhLEVBQUUseUJBQXlCLENBQUU7SUFDNUM7QUFDRixDQUFVO0FBRVY7QUFDTyxJQUFNZSxRQUFRLEdBQUc7SUFDdEJDLE9BQU8sS0FBQUMsTUFBQSxDQUFLQyx1QkFBK0IsZUFBWTtJQUN2REcsTUFBTSxLQUFBSixNQUFBLENBQUtDLHVCQUErQixrQkFBZTtJQUN6REksT0FBTyxLQUFBTCxNQUFBLENBQUtDLHVCQUErQjtBQUM3QyxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTNcXHNyY1xcbGliXFxzdHJpcGVcXHBsYW5zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9saWIvc3RyaXBlL3BsYW5zLnRzXG4vLyBDb25maWd1cmFjacOzbiBkZSBwbGFuZXMgaW50ZWdyYWRhIGNvbiBzaXN0ZW1hIGRlIGzDrW1pdGVzXG5cbmltcG9ydCB7IFBMQU5fQ09ORklHVVJBVElPTlMgfSBmcm9tICdAL2xpYi91dGlscy9wbGFuTGltaXRzJztcblxuZXhwb3J0IGNvbnN0IFBMQU5TID0ge1xuICBmcmVlOiB7XG4gICAgaWQ6ICdmcmVlJyxcbiAgICBuYW1lOiAnUGxhbiBHcmF0aXMnLFxuICAgIHByaWNlOiAwLFxuICAgIHN0cmlwZVByb2R1Y3RJZDogbnVsbCxcbiAgICBzdHJpcGVQcmljZUlkOiBudWxsLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnSW5jbHV5ZTonLFxuICAgICAgJ+KAoiBVc28gZGUgbGEgcGxhdGFmb3JtYSBzb2xvIGR1cmFudGUgNSBkw61hcycsXG4gICAgICAn4oCiIFN1YmlkYSBkZSBkb2N1bWVudG9zOiBtw6F4aW1vIDEgZG9jdW1lbnRvJyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIHRlc3Q6IG3DoXhpbW8gMTAgcHJlZ3VudGFzIHRlc3QnLFxuICAgICAgJ+KAoiBHZW5lcmFkb3IgZGUgZmxhc2hjYXJkczogbcOheGltbyAxMCB0YXJqZXRhcyBmbGFzaGNhcmQnLFxuICAgICAgJ+KAoiBHZW5lcmFkb3IgZGUgbWFwYXMgbWVudGFsZXM6IG3DoXhpbW8gMiBtYXBhcyBtZW50YWxlcycsXG4gICAgICAnTm8gaW5jbHV5ZTonLFxuICAgICAgJ+KAoiBQbGFuaWZpY2FjacOzbiBkZSBlc3R1ZGlvcycsXG4gICAgICAn4oCiIEhhYmxhIGNvbiB0dSBwcmVwYXJhZG9yIElBJyxcbiAgICAgICfigKIgUmVzw7ptZW5lcyBBMiB5IEExJyxcbiAgICBdLFxuICAgIGxpbWl0czogUExBTl9DT05GSUdVUkFUSU9OUy5mcmVlLmxpbWl0cyxcbiAgICBwbGFuQ29uZmlnOiBQTEFOX0NPTkZJR1VSQVRJT05TLmZyZWVcbiAgfSxcbiAgdXN1YXJpbzoge1xuICAgIGlkOiAndXN1YXJpbycsXG4gICAgbmFtZTogJ1BsYW4gVXN1YXJpbycsXG4gICAgcHJpY2U6IDEwMDAsIC8vIEVuIGNlbnRhdm9zICjigqwxMC4wMClcbiAgICBzdHJpcGVQcm9kdWN0SWQ6ICdwcm9kX1NSNjVCZEtkZWsxT1hkJyxcbiAgICAvLyBJTVBPUlRBTlRFOiBFc3RlIHByZWNpbyBkZWJlIHNlciByZWN1cnJlbnRlIChzdXNjcmlwY2nDs24gbWVuc3VhbCkgZW4gU3RyaXBlXG4gICAgLy8gU2kgYWN0dWFsbWVudGUgZXMgdW4gcGFnbyDDum5pY28sIGNyZWFyIHVuIG51ZXZvIHByZWNpbyByZWN1cnJlbnRlIGVuIFN0cmlwZSBEYXNoYm9hcmRcbiAgICBzdHJpcGVQcmljZUlkOiAncHJpY2VfMVJhZTU4MDdrRm4zc0lYaFJmM2FkWDFuJyxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ0luY2x1eWU6JyxcbiAgICAgICfigKIgVXNvIGRlIGxhIHBsYXRhZm9ybWEgZHVyYW50ZSBlbCBtZXMgKHVuYSB2ZXogZmluYWxpemFkbyBubyBwb2Ryw6Egdm9sdmVyIGEgYWNjZWRlciBoYXN0YSByZW5vdmFjacOzbiknLFxuICAgICAgJ+KAoiBTdWJpZGEgZGUgZG9jdW1lbnRvcycsXG4gICAgICAn4oCiIEhhYmxhIGNvbiB0dSBwcmVwYXJhZG9yIElBIConLFxuICAgICAgJ+KAoiBHZW5lcmFkb3IgZGUgdGVzdCAqJyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIGZsYXNoY2FyZHMgKicsXG4gICAgICAn4oCiIEdlbmVyYWRvciBkZSBtYXBhcyBtZW50YWxlcyAqJyxcbiAgICAgICfigKIgKiBQYXJhIGxhcyB0YXJlYXMgZW4gbGFzIHF1ZSBzZSBoYWdhIHVzbyBkZSBJQSwgZWwgbMOtbWl0ZSBtZW5zdWFsIHNlcsOhIGRlIDEuMDAwLjAwMCBkZSB0b2tlbnMsIHBvZHLDoW4gYW1wbGlhcnNlIGR1cmFudGUgZWwgbWVzIG1lZGlhbnRlIHBhZ28nLFxuICAgICAgJ05vIGluY2x1eWU6JyxcbiAgICAgICfigKIgUGxhbmlmaWNhY2nDs24gZGUgZXN0dWRpb3MnLFxuICAgICAgJ+KAoiBSZXPDum1lbmVzIEEyIHkgQTEnLFxuICAgIF0sXG4gICAgbGltaXRzOiBQTEFOX0NPTkZJR1VSQVRJT05TLnVzdWFyaW8ubGltaXRzLFxuICAgIHBsYW5Db25maWc6IFBMQU5fQ09ORklHVVJBVElPTlMudXN1YXJpb1xuICB9LFxuICBwcm86IHtcbiAgICBpZDogJ3BybycsXG4gICAgbmFtZTogJ1BsYW4gUHJvJyxcbiAgICBwcmljZTogMTUwMCwgLy8gRW4gY2VudGF2b3MgKOKCrDE1LjAwKVxuICAgIHN0cmlwZVByb2R1Y3RJZDogJ3Byb2RfU1I2NlUyRzdiVkpxdTMnLFxuICAgIHN0cmlwZVByaWNlSWQ6ICdwcmljZV8xUmFlM1UwN2tGbjNzSVhoa3ZTdUpjbzEnLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnSW5jbHV5ZTonLFxuICAgICAgJ+KAoiBVc28gZGUgbGEgcGxhdGFmb3JtYSBkdXJhbnRlIGVsIG1lcyAodW5hIHZleiBmaW5hbGl6YWRvIG5vIHBvZHLDoSB2b2x2ZXIgYSBhY2NlZGVyIGhhc3RhIHJlbm92YWNpw7NuKScsXG4gICAgICAn4oCiIFN1YmlkYSBkZSBkb2N1bWVudG9zJyxcbiAgICAgICfigKIgUGxhbmlmaWNhY2nDs24gZGUgZXN0dWRpb3MgbWVkaWFudGUgSUEqJyxcbiAgICAgICfigKIgSGFibGEgY29uIHR1IHByZXBhcmFkb3IgSUEgKicsXG4gICAgICAn4oCiIEdlbmVyYWRvciBkZSB0ZXN0IConLFxuICAgICAgJ+KAoiBHZW5lcmFkb3IgZGUgZmxhc2hjYXJkcyAqJyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIG1hcGFzIG1lbnRhbGVzIConLFxuICAgICAgJ+KAoiBHZW5lcmFjacOzbiBkZSBSZXPDum1lbmVzIHBhcmEgQTIgeSBBMScsXG4gICAgICAn4oCiICogUGFyYSBsYXMgdGFyZWFzIGVuIGxhcyBxdWUgc2UgaGFnYSB1c28gZGUgSUEsIGVsIGzDrW1pdGUgbWVuc3VhbCBzZXLDoSBkZSAxLjAwMC4wMDAgZGUgdG9rZW5zLCBwb2Ryw6FuIGFtcGxpYXJzZSBkdXJhbnRlIGVsIG1lcyBtZWRpYW50ZSBwYWdvJyxcbiAgICBdLFxuICAgIGxpbWl0czogUExBTl9DT05GSUdVUkFUSU9OUy5wcm8ubGltaXRzLFxuICAgIHBsYW5Db25maWc6IFBMQU5fQ09ORklHVVJBVElPTlMucHJvXG4gIH1cbn0gYXMgY29uc3Q7XG5cbmV4cG9ydCB0eXBlIFBsYW5JZCA9IGtleW9mIHR5cGVvZiBQTEFOUztcblxuLy8gRnVuY2nDs24gcGFyYSBvYnRlbmVyIHBsYW4gcG9yIElEXG5leHBvcnQgZnVuY3Rpb24gZ2V0UGxhbkJ5SWQocGxhbklkOiBzdHJpbmcpOiB0eXBlb2YgUExBTlNbUGxhbklkXSB8IG51bGwge1xuICByZXR1cm4gUExBTlNbcGxhbklkIGFzIFBsYW5JZF0gfHwgbnVsbDtcbn1cblxuLy8gRnVuY2nDs24gcGFyYSB2YWxpZGFyIHNpIHVuIHBsYW4gZXMgdsOhbGlkb1xuZXhwb3J0IGZ1bmN0aW9uIGlzVmFsaWRQbGFuKHBsYW5JZDogc3RyaW5nKTogcGxhbklkIGlzIFBsYW5JZCB7XG4gIHJldHVybiBwbGFuSWQgaW4gUExBTlM7XG59XG5cbi8vIEZ1bmNpw7NuIHBhcmEgb2J0ZW5lciBjb25maWd1cmFjacOzbiBjb21wbGV0YSBkZWwgcGxhblxuZXhwb3J0IGZ1bmN0aW9uIGdldEZ1bGxQbGFuQ29uZmlnKHBsYW5JZDogc3RyaW5nKSB7XG4gIGNvbnN0IHBsYW4gPSBnZXRQbGFuQnlJZChwbGFuSWQpO1xuICByZXR1cm4gcGxhbj8ucGxhbkNvbmZpZyB8fCBudWxsO1xufVxuXG4vLyBDb25maWd1cmFjacOzbiBkZSBwcm9kdWN0b3MgYWRpY2lvbmFsZXNcbmV4cG9ydCBjb25zdCBBRERJVElPTkFMX1BST0RVQ1RTID0ge1xuICB0b2tlbnM6IHtcbiAgICBpZDogJ3Rva2VucycsXG4gICAgbmFtZTogJ1Rva2VucyBBZGljaW9uYWxlcycsXG4gICAgZGVzY3JpcHRpb246ICcxLDAwMCwwMDAgdG9rZW5zIGFkaWNpb25hbGVzIHBhcmEgdHUgY3VlbnRhJyxcbiAgICBwcmljZTogMTAwMCwgLy8gRW4gY2VudGF2b3MgKOKCrDEwLjAwKVxuICAgIHRva2VuQW1vdW50OiAxMDAwMDAwLFxuICAgIC8vIEVzdG9zIElEcyBzZSBkZWJlbiBjcmVhciBlbiBTdHJpcGUgRGFzaGJvYXJkXG4gICAgc3RyaXBlUHJvZHVjdElkOiAncHJvZF90b2tlbnNfYWRkaXRpb25hbCcsIC8vIFBsYWNlaG9sZGVyIC0gY3JlYXIgZW4gU3RyaXBlXG4gICAgc3RyaXBlUHJpY2VJZDogJ3ByaWNlX3Rva2Vuc19hZGRpdGlvbmFsJywgLy8gUGxhY2Vob2xkZXIgLSBjcmVhciBlbiBTdHJpcGVcbiAgfVxufSBhcyBjb25zdDtcblxuLy8gVVJMcyBkZSBsYSBhcGxpY2FjacOzblxuZXhwb3J0IGNvbnN0IEFQUF9VUkxTID0ge1xuICBzdWNjZXNzOiBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVVJMfS90aGFuay15b3VgLFxuICBjYW5jZWw6IGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkx9L3VwZ3JhZGUtcGxhbmAsXG4gIHdlYmhvb2s6IGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkx9L2FwaS9zdHJpcGUvd2ViaG9va2AsXG59IGFzIGNvbnN0O1xuIl0sIm5hbWVzIjpbIlBMQU5fQ09ORklHVVJBVElPTlMiLCJQTEFOUyIsImZyZWUiLCJpZCIsIm5hbWUiLCJwcmljZSIsInN0cmlwZVByb2R1Y3RJZCIsInN0cmlwZVByaWNlSWQiLCJmZWF0dXJlcyIsImxpbWl0cyIsInBsYW5Db25maWciLCJ1c3VhcmlvIiwicHJvIiwiZ2V0UGxhbkJ5SWQiLCJwbGFuSWQiLCJpc1ZhbGlkUGxhbiIsImdldEZ1bGxQbGFuQ29uZmlnIiwicGxhbiIsIkFERElUSU9OQUxfUFJPRFVDVFMiLCJ0b2tlbnMiLCJkZXNjcmlwdGlvbiIsInRva2VuQW1vdW50IiwiQVBQX1VSTFMiLCJzdWNjZXNzIiwiY29uY2F0IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQUF9VUkwiLCJjYW5jZWwiLCJ3ZWJob29rIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stripe/plans.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nvar PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    var config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    var config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    var config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    var config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    var config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: \"Caracter\\xEDstica \".concat(feature, \" no disponible en \").concat(config.name)\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        var weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: \"L\\xEDmite semanal de \".concat(limitType, \" alcanzado (\").concat(weeklyLimit, \")\")\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nfunction checkUserFeatureAccess(_x) {\n    return _checkUserFeatureAccess.apply(this, arguments);\n}\nfunction _checkUserFeatureAccess() {\n    _checkUserFeatureAccess = (0,C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(feature) {\n        var response, _yield$response$json, plan, userPlan;\n        return C_Users_naata_Documents_augment_projects_OposI_v13_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n            while(1)switch(_context.prev = _context.next){\n                case 0:\n                    _context.prev = 0;\n                    _context.next = 3;\n                    return fetch('/api/user/plan');\n                case 3:\n                    response = _context.sent;\n                    if (response.ok) {\n                        _context.next = 7;\n                        break;\n                    }\n                    console.error('Error obteniendo plan del usuario');\n                    // Si hay error, asumir plan gratuito\n                    return _context.abrupt(\"return\", hasFeatureAccess('free', feature));\n                case 7:\n                    _context.next = 9;\n                    return response.json();\n                case 9:\n                    _yield$response$json = _context.sent;\n                    plan = _yield$response$json.plan;\n                    userPlan = plan || 'free'; // Usar la misma lógica que la función hasFeatureAccess\n                    return _context.abrupt(\"return\", hasFeatureAccess(userPlan, feature));\n                case 15:\n                    _context.prev = 15;\n                    _context.t0 = _context[\"catch\"](0);\n                    console.error('Error verificando acceso a característica:', _context.t0);\n                    // En caso de error, asumir plan gratuito\n                    return _context.abrupt(\"return\", hasFeatureAccess('free', feature));\n                case 19:\n                case \"end\":\n                    return _context.stop();\n            }\n        }, _callee, null, [\n            [\n                0,\n                15\n            ]\n        ]);\n    }));\n    return _checkUserFeatureAccess.apply(this, arguments);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/planLimits.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv13%5C%5Csrc%5C%5Capp%5C%5Cthank-you%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);