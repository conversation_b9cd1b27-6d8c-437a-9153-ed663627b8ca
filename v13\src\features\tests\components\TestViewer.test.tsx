import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TestViewer from './TestViewer';
import { obtenerTests, obtenerPreguntasTestCount, obtenerPreguntasPorTestId, obtenerEstadisticasTest, obtenerEstadisticasGeneralesTests, registrarRespuestaTest } from '@/lib/supabase/testsService'; // Adjusted path

// Mock the entire module
jest.mock('@/lib/supabase/testsService', () => ({
  obtenerTests: jest.fn(),
  obtenerPreguntasPorTestId: jest.fn(),
  obtenerPreguntasTestCount: jest.fn(),
  registrarRespuestaTest: jest.fn(),
  obtenerEstadisticasGeneralesTests: jest.fn(),
  obtenerEstadisticasTest: jest.fn(),
}));

// Typed mocks
const mockedObtenerTests = obtenerTests as jest.Mock;
const mockedObtenerPreguntasTestCount = obtenerPreguntasTestCount as jest.Mock;
const mockedObtenerPreguntasPorTestId = obtenerPreguntasPorTestId as jest.Mock;
const mockedObtenerEstadisticasTest = obtenerEstadisticasTest as jest.Mock;


describe('TestViewer', () => {
  beforeEach(() => {
    // Reset mocks before each test
    mockedObtenerTests.mockReset();
    mockedObtenerPreguntasTestCount.mockReset();
    mockedObtenerPreguntasPorTestId.mockReset();
    mockedObtenerEstadisticasTest.mockReset();

    // Default mock for functions that might be called on interaction
    mockedObtenerPreguntasPorTestId.mockResolvedValue([]);
    mockedObtenerEstadisticasTest.mockResolvedValue({
      totalPreguntas: 0,
      totalCorrectas: 0,
      totalIncorrectas: 0,
      porcentajeAcierto: 0,
      fechasRealizacion: [],
      preguntasMasFalladas: []
    });
  });

  test('displays loading state initially', () => {
    mockedObtenerTests.mockReturnValue(new Promise(() => {})); // Keep it pending
    render(<TestViewer />);
    expect(screen.getByRole('status')).toBeInTheDocument(); // Assuming spinner has a role like 'status' or 'progressbar'
  });

  test('displays error message if loading tests fails', async () => {
    mockedObtenerTests.mockRejectedValueOnce(new Error('Failed to fetch tests'));
    render(<TestViewer />);
    expect(await screen.findByText(/No se pudieron cargar los tests/i)).toBeInTheDocument();
  });

  test('displays "No hay tests disponibles" message when no tests are fetched', async () => {
    mockedObtenerTests.mockResolvedValueOnce([]);
    render(<TestViewer />);
    expect(await screen.findByText(/No hay tests disponibles/i)).toBeInTheDocument();
  });
  
  describe('When tests are loaded', () => {
    const mockTestsData = [
      { id: 't1', titulo: 'Math Test Alpha', creado_en: new Date().toISOString(), descripcion: 'Test your math skills Alpha' },
      { id: 't2', titulo: 'History Test Beta', creado_en: new Date().toISOString(), descripcion: 'Test your history knowledge Beta' },
    ];

    test('displays question counts and correct button styles for test cards', async () => {
      mockedObtenerTests.mockResolvedValueOnce(mockTestsData);
      
      mockedObtenerPreguntasTestCount.mockImplementation(async (testId) => {
        if (testId === 't1') return 5;
        if (testId === 't2') return 10;
        return 0;
      });

      render(<TestViewer />);

      // Wait for the first test card's title to ensure tests and counts are loaded
      await screen.findByText('Math Test Alpha');
      
      // Check question counts
      expect(screen.getByText(/Preguntas: 5/i)).toBeInTheDocument();
      expect(screen.getByText(/Preguntas: 10/i)).toBeInTheDocument();

      // Check button presence and styling for the first card
      // To ensure we are scoping to the first card, we find it and then query within.
      // This is a bit verbose, ideally test cards would have test-ids
      const mathTestCard = (await screen.findByText('Math Test Alpha')).closest('div'); // Find the card container
      
      if (!mathTestCard) throw new Error("Math Test Alpha card not found");

      const realizarTestButtonMath = within(mathTestCard).getByRole('button', { name: /Realizar Test/i });
      const estadisticasButtonMath = within(mathTestCard).getByRole('button', { name: /Estadísticas/i });

      expect(realizarTestButtonMath).toBeInTheDocument();
      expect(realizarTestButtonMath).toHaveClass('bg-blue-600');
      expect(realizarTestButtonMath).toHaveClass('font-semibold');


      expect(estadisticasButtonMath).toBeInTheDocument();
      expect(estadisticasButtonMath).toHaveClass('bg-gray-500');
      expect(estadisticasButtonMath).toHaveClass('font-semibold');


      // Check for the second card as well
      const historyTestCard = (await screen.findByText('History Test Beta')).closest('div');
      if (!historyTestCard) throw new Error("History Test Beta card not found");

      const realizarTestButtonHistory = within(historyTestCard).getByRole('button', { name: /Realizar Test/i });
      expect(realizarTestButtonHistory).toHaveClass('bg-blue-600');

    });

    test('shows "Cargando..." for question counts initially, then updates', async () => {
      let resolveCountPromise: (value: number) => void = () => {};
      const countPromise = new Promise<number>(resolve => {
        resolveCountPromise = resolve;
      });

      mockedObtenerTests.mockResolvedValueOnce([mockTestsData[0]]); // Only one test for simplicity
      mockedObtenerPreguntasTestCount.mockImplementation(() => countPromise);

      render(<TestViewer />);
      
      // Wait for the test title to appear
      await screen.findByText('Math Test Alpha');

      // Check for "Cargando..." state for questions
      // This relies on the component rendering "Cargando..." if numero_preguntas is undefined
      expect(screen.getByText(/Preguntas: Cargando.../i)).toBeInTheDocument();
      
      // Resolve the promise for the count
      resolveCountPromise(7);
      
      // Wait for the count to update and check
      expect(await screen.findByText(/Preguntas: 7/i)).toBeInTheDocument();
    });
  });
});

// Helper to re-add `within` as it might be removed by the tool
import { within } from '@testing-library/react';
