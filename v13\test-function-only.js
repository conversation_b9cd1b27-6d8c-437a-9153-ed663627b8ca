// Test directo de la función atómica sin crear usuarios
// Este archivo será eliminado después de las pruebas

const { createClient } = require('@supabase/supabase-js');

// Configuración de Supabase
const supabaseUrl = 'https://fxnhpxjijinfuxxxplzj.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5MTgwNCwiZXhwIjoyMDYzMDY3ODA0fQ.2dZuBBYg8otnLDhCXhGzrJ69-We578rcUDIpAt33zlg';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testFunctionOnly() {
  console.log('🧪 Probando función atómica directamente...\n');

  try {
    // Test 1: Verificar que la función existe
    console.log('Paso 1: Verificando que la función existe...');
    
    const { data: functions, error: funcError } = await supabase
      .rpc('exec_sql', {
        sql: "SELECT routine_name FROM information_schema.routines WHERE routine_name = 'create_user_profile_and_history'"
      });

    if (funcError) {
      console.log('⚠️ No se pudo verificar función, continuando...');
    } else {
      console.log('✅ Función existe en la base de datos');
    }

    // Test 2: Usar usuario existente conocido
    console.log('\nPaso 2: Usando usuario existente para prueba...');
    const existingUserId = '6b830540-eccc-4993-8a45-52b6fc57b9c4'; // <EMAIL>
    console.log(`✅ Usuario ID: ${existingUserId}`);

    // Test 3: Probar función atómica
    console.log('\nPaso 3: Ejecutando función atómica...');
    
    const profileData = {
      subscription_plan: 'pro',
      monthly_token_limit: 500000,
      current_month_tokens: 0,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      payment_verified: true,
      stripe_customer_id: 'cus_test_atomic_' + Date.now(),
      stripe_subscription_id: 'sub_test_atomic_' + Date.now(),
      last_payment_date: new Date().toISOString(),
      auto_renew: true,
      plan_expires_at: null,
      plan_features: { 
        test: true,
        atomic_test: true,
        unlimited_documents: true
      },
      security_flags: { 
        test_atomic_function: true,
        test_timestamp: new Date().toISOString(),
        test_id: Date.now()
      }
    };

    const transactionId = crypto.randomUUID();
    console.log(`Transaction ID: ${transactionId}`);

    const { data: result, error: rpcError } = await supabase
      .rpc('create_user_profile_and_history', {
        p_user_id: existingUserId,
        p_transaction_id: transactionId,
        p_profile_data: profileData
      })
      .single();

    if (rpcError) {
      throw new Error(`Error en función RPC: ${rpcError.message}`);
    }

    console.log('✅ Función atómica ejecutada exitosamente');
    console.log(`   - Profile ID: ${result.created_profile_id}`);
    console.log(`   - History ID: ${result.created_history_id}`);

    // Test 4: Verificar que el perfil fue creado/actualizado
    console.log('\nPaso 4: Verificando perfil en base de datos...');
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', result.created_profile_id)
      .single();

    if (profileError) {
      throw new Error(`Error verificando perfil: ${profileError.message}`);
    }

    console.log('✅ Perfil verificado en base de datos');
    console.log(`   - User ID: ${profile.user_id}`);
    console.log(`   - Plan: ${profile.subscription_plan}`);
    console.log(`   - Tokens: ${profile.monthly_token_limit}`);
    console.log(`   - Payment verified: ${profile.payment_verified}`);
    console.log(`   - Stripe customer: ${profile.stripe_customer_id}`);
    console.log(`   - Auto renew: ${profile.auto_renew}`);

    // Test 5: Verificar que el historial fue creado
    console.log('\nPaso 5: Verificando historial en base de datos...');
    const { data: history, error: historyError } = await supabase
      .from('user_plan_history')
      .select('*')
      .eq('id', result.created_history_id)
      .single();

    if (historyError) {
      throw new Error(`Error verificando historial: ${historyError.message}`);
    }

    console.log('✅ Historial verificado en base de datos');
    console.log(`   - User ID: ${history.user_id}`);
    console.log(`   - Old plan: ${history.old_plan}`);
    console.log(`   - New plan: ${history.new_plan}`);
    console.log(`   - Reason: ${history.reason}`);
    console.log(`   - Transaction ID: ${history.transaction_id}`);
    console.log(`   - Changed by: ${history.changed_by}`);

    // Test 6: Probar upsert (segunda llamada)
    console.log('\nPaso 6: Probando upsert (segunda llamada)...');
    
    const updatedProfileData = {
      subscription_plan: 'usuario',
      monthly_token_limit: 200000,
      current_month_tokens: 5000,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      payment_verified: true,
      stripe_customer_id: 'cus_updated_' + Date.now(),
      stripe_subscription_id: 'sub_updated_' + Date.now(),
      last_payment_date: new Date().toISOString(),
      auto_renew: false,
      plan_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      plan_features: { 
        test: true,
        atomic_test: true,
        updated: true
      },
      security_flags: { 
        test_atomic_function: true,
        test_timestamp: new Date().toISOString(),
        test_id: Date.now(),
        updated: true
      }
    };

    const { data: updateResult, error: updateError } = await supabase
      .rpc('create_user_profile_and_history', {
        p_user_id: existingUserId,
        p_transaction_id: crypto.randomUUID(),
        p_profile_data: updatedProfileData
      })
      .single();

    if (updateError) {
      throw new Error(`Error en actualización: ${updateError.message}`);
    }

    console.log('✅ Upsert ejecutado exitosamente');
    console.log(`   - Mismo Profile ID: ${result.created_profile_id === updateResult.created_profile_id}`);
    console.log(`   - Nuevo History ID: ${updateResult.created_history_id}`);

    // Verificar que el perfil se actualizó
    const { data: updatedProfile } = await supabase
      .from('user_profiles')
      .select('subscription_plan, monthly_token_limit, current_month_tokens, stripe_customer_id')
      .eq('user_id', existingUserId)
      .single();

    console.log('✅ Actualización verificada');
    console.log(`   - Plan actualizado: ${updatedProfile.subscription_plan}`);
    console.log(`   - Límite actualizado: ${updatedProfile.monthly_token_limit}`);
    console.log(`   - Tokens actuales: ${updatedProfile.current_month_tokens}`);
    console.log(`   - Customer actualizado: ${updatedProfile.stripe_customer_id}`);

    console.log('\n🎉 TODOS LOS TESTS PASARON EXITOSAMENTE');
    console.log('✅ La función atómica funciona correctamente');
    console.log('✅ El upsert funciona correctamente');
    console.log('✅ La integridad de datos está garantizada');
    console.log('✅ Los problemas de "usuarios fantasma" están resueltos');

  } catch (error) {
    console.log('\n❌ TEST FALLÓ:', error.message);
    console.log('Stack trace:', error.stack);
  }
}

// Ejecutar test
testFunctionOnly();
