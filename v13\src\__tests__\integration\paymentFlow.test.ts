// src/__tests__/integration/paymentFlow.test.ts
// Tests de integración para el flujo completo de pago

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { StripeWebhookHandler } from '@/lib/stripe/webhookHandler';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { UserManagementService } from '@/lib/supabase/userManagement';
import { PlanValidationService } from '@/lib/services/planValidation';

// Mock de dependencias
jest.mock('@/lib/supabase/admin');
jest.mock('@/lib/supabase/userManagement');
jest.mock('@/lib/services/planValidation');

const mockSupabaseAdmin = SupabaseAdminService as jest.Mocked<typeof SupabaseAdminService>;
const mockUserManagement = UserManagementService as jest.Mocked<typeof UserManagementService>;
const mockPlanValidation = PlanValidationService as jest.Mocked<typeof PlanValidationService>;

describe('Payment Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete payment to access flow', () => {
    it('should handle complete payment flow for new user', async () => {
      // Mock Stripe session data
      const mockSession = {
        id: 'cs_test_123',
        customer_email: '<EMAIL>',
        customer_details: {
          name: 'New User',
          email: '<EMAIL>'
        },
        metadata: {
          plan_id: 'usuario',
          plan_name: 'Plan Usuario'
        },
        amount_total: 1000, // €10.00
        currency: 'eur',
        payment_status: 'paid'
      };

      // Mock user creation
      mockUserManagement.createUserFromPayment.mockResolvedValue({
        user: {
          id: 'new-user-id',
          email: '<EMAIL>'
        },
        profile: {
          user_id: 'new-user-id',
          subscription_plan: 'usuario',
          payment_verified: true,
          monthly_token_limit: 1000000,
          current_month_tokens: 0,
          current_month: '2025-01-01'
        },
        inviteUrl: 'https://app.example.com/auth/callback?token=invite123'
      });

      // Mock transaction logging
      mockSupabaseAdmin.logStripeTransaction.mockResolvedValue(true);

      // Execute webhook handler
      const handler = new StripeWebhookHandler();
      const result = await handler.handleCheckoutCompleted(mockSession as any);

      expect(result.success).toBe(true);

      // Verify user was created with correct plan
      expect(mockUserManagement.createUserFromPayment).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'New User',
        planId: 'usuario',
        planName: 'Plan Usuario',
        paymentAmount: 1000,
        sessionId: 'cs_test_123'
      });

      // Verify transaction was logged
      expect(mockSupabaseAdmin.logStripeTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          session_id: 'cs_test_123',
          customer_email: '<EMAIL>',
          amount: 1000,
          currency: 'eur',
          status: 'completed'
        })
      );
    });

    it('should validate access after payment completion', async () => {
      // Mock existing user profile after payment
      mockPlanValidation.getUserAccessInfo.mockResolvedValue({
        plan: 'usuario',
        features: ['ai_tutor_chat', 'test_generation', 'flashcard_generation'],
        limits: {
          monthlyTokens: 1000000,
          maxTests: 100,
          maxFlashcards: 500
        },
        currentUsage: {
          tokens: 0,
          tests: 0,
          flashcards: 0
        },
        paymentVerified: true,
        planLimits: {
          name: 'Plan Usuario',
          features: ['ai_tutor_chat', 'test_generation', 'flashcard_generation']
        }
      });

      // Test feature access validation
      mockPlanValidation.validateFeatureAccess.mockResolvedValue({
        allowed: true,
        remainingUsage: {
          tokens: 1000000,
          tests: 100,
          flashcards: 500
        }
      });

      const accessInfo = await PlanValidationService.getUserAccessInfo('new-user-id');
      expect(accessInfo?.paymentVerified).toBe(true);
      expect(accessInfo?.features).toContain('ai_tutor_chat');

      const featureAccess = await PlanValidationService.validateFeatureAccess(
        'new-user-id',
        'ai_tutor_chat',
        1000
      );
      expect(featureAccess.allowed).toBe(true);
    });

    it('should handle payment failure gracefully', async () => {
      const mockFailedSession = {
        id: 'cs_test_failed',
        customer_email: '<EMAIL>',
        payment_status: 'failed',
        metadata: {
          plan_id: 'usuario'
        }
      };

      mockSupabaseAdmin.logStripeTransaction.mockResolvedValue(true);

      const handler = new StripeWebhookHandler();
      const result = await handler.handleCheckoutCompleted(mockFailedSession as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Payment not completed');

      // Should still log the failed transaction
      expect(mockSupabaseAdmin.logStripeTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'failed'
        })
      );

      // Should not create user for failed payment
      expect(mockUserManagement.createUserFromPayment).not.toHaveBeenCalled();
    });
  });

  describe('Plan upgrade flow', () => {
    it('should handle plan upgrade from free to usuario', async () => {
      // Mock existing free user
      const existingProfile = {
        user_id: 'existing-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 25000,
        current_month: '2025-01-01'
      };

      mockSupabaseAdmin.getUserProfile.mockResolvedValue(existingProfile);

      // Mock upgrade session
      const upgradeSession = {
        id: 'cs_upgrade_123',
        customer_email: '<EMAIL>',
        metadata: {
          plan_id: 'usuario',
          user_id: 'existing-user',
          upgrade: 'true'
        },
        payment_status: 'paid'
      };

      // Mock profile update
      mockSupabaseAdmin.upsertUserProfile.mockResolvedValue(true);

      const handler = new StripeWebhookHandler();
      const result = await handler.handleCheckoutCompleted(upgradeSession as any);

      expect(result.success).toBe(true);

      // Verify profile was updated with new plan
      expect(mockSupabaseAdmin.upsertUserProfile).toHaveBeenCalledWith(
        expect.objectContaining({
          subscription_plan: 'usuario',
          payment_verified: true,
          monthly_token_limit: 1000000 // New limit for usuario plan
        })
      );
    });

    it('should preserve token usage during plan upgrade', async () => {
      const existingProfile = {
        user_id: 'existing-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 25000, // Existing usage
        current_month: '2025-01-01'
      };

      mockSupabaseAdmin.getUserProfile.mockResolvedValue(existingProfile);
      mockSupabaseAdmin.upsertUserProfile.mockResolvedValue(true);

      const upgradeSession = {
        id: 'cs_upgrade_123',
        customer_email: '<EMAIL>',
        metadata: {
          plan_id: 'usuario',
          user_id: 'existing-user',
          upgrade: 'true'
        },
        payment_status: 'paid'
      };

      const handler = new StripeWebhookHandler();
      await handler.handleCheckoutCompleted(upgradeSession as any);

      // Verify token usage was preserved
      expect(mockSupabaseAdmin.upsertUserProfile).toHaveBeenCalledWith(
        expect.objectContaining({
          current_month_tokens: 25000, // Should preserve existing usage
          monthly_token_limit: 1000000 // But update the limit
        })
      );
    });
  });

  describe('Access validation after payment', () => {
    it('should allow premium features after successful payment', async () => {
      // Mock paid user profile
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'paid-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 10000,
        current_month: '2025-01-01'
      });

      // Test AI tutor access (premium feature)
      const aiTutorAccess = await PlanValidationService.validateFeatureAccess(
        'paid-user',
        'ai_tutor_chat',
        5000
      );

      expect(aiTutorAccess.allowed).toBe(true);
      expect(aiTutorAccess.remainingUsage?.tokens).toBe(990000);
    });

    it('should deny premium features for unverified payment', async () => {
      // Mock user with unverified payment
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'unverified-user',
        subscription_plan: 'usuario',
        payment_verified: false, // Payment not verified
        monthly_token_limit: 1000000,
        current_month_tokens: 10000,
        current_month: '2025-01-01'
      });

      const aiTutorAccess = await PlanValidationService.validateFeatureAccess(
        'unverified-user',
        'ai_tutor_chat',
        5000
      );

      expect(aiTutorAccess.allowed).toBe(false);
      expect(aiTutorAccess.reason).toContain('pago no verificado');
    });

    it('should handle token limit enforcement for paid users', async () => {
      // Mock user near token limit
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'near-limit-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 999000, // Very close to limit
        current_month: '2025-01-01'
      });

      // Should allow small token usage
      const smallUsage = await PlanValidationService.validateFeatureAccess(
        'near-limit-user',
        'ai_tutor_chat',
        500
      );
      expect(smallUsage.allowed).toBe(true);

      // Should deny large token usage
      const largeUsage = await PlanValidationService.validateFeatureAccess(
        'near-limit-user',
        'ai_tutor_chat',
        5000
      );
      expect(largeUsage.allowed).toBe(false);
      expect(largeUsage.reason).toContain('límite mensual');
    });
  });

  describe('Error scenarios', () => {
    it('should handle database errors during payment processing', async () => {
      const mockSession = {
        id: 'cs_error_test',
        customer_email: '<EMAIL>',
        payment_status: 'paid',
        metadata: { plan_id: 'usuario' }
      };

      // Mock database error
      mockUserManagement.createUserFromPayment.mockRejectedValue(
        new Error('Database connection failed')
      );

      const handler = new StripeWebhookHandler();
      const result = await handler.handleCheckoutCompleted(mockSession as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection failed');
    });

    it('should handle invalid plan IDs gracefully', async () => {
      const mockSession = {
        id: 'cs_invalid_plan',
        customer_email: '<EMAIL>',
        payment_status: 'paid',
        metadata: { plan_id: 'invalid_plan' }
      };

      mockUserManagement.createUserFromPayment.mockRejectedValue(
        new Error('Invalid plan configuration')
      );

      const handler = new StripeWebhookHandler();
      const result = await handler.handleCheckoutCompleted(mockSession as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid plan configuration');
    });

    it('should handle missing customer information', async () => {
      const mockSession = {
        id: 'cs_missing_info',
        customer_email: null, // Missing email
        payment_status: 'paid',
        metadata: { plan_id: 'usuario' }
      };

      const handler = new StripeWebhookHandler();
      const result = await handler.handleCheckoutCompleted(mockSession as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing customer information');
    });
  });
});
