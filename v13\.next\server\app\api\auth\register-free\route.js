/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register-free/route";
exports.ids = ["app/api/auth/register-free/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register-free/route.ts */ \"(rsc)/./src/app/api/auth/register-free/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register-free/route\",\n        pathname: \"/api/auth/register-free\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register-free/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v13\\\\src\\\\app\\\\api\\\\auth\\\\register-free\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v13_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/register-free/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/register-free/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/freeAccountService */ \"(rsc)/./src/lib/services/freeAccountService.ts\");\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n// src/app/api/auth/register-free/route.ts\n// Endpoint para registro automático de cuentas gratuitas\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_2__.Resend(process.env.RESEND_API_KEY);\n// Rate limiting simple en memoria (en producción usar Redis)\nconst registrationAttempts = new Map();\nconst RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos\nconst MAX_ATTEMPTS = 3; // Máximo 3 intentos por IP en 15 minutos\nasync function POST(request) {\n    try {\n        const startTime = Date.now();\n        console.log('🆓 Iniciando registro de cuenta gratuita');\n        // 1. Rate limiting básico\n        const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';\n        const now = Date.now();\n        const attempts = registrationAttempts.get(clientIP);\n        if (attempts) {\n            // Limpiar intentos antiguos\n            if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {\n                registrationAttempts.delete(clientIP);\n            } else if (attempts.count >= MAX_ATTEMPTS) {\n                console.log(`❌ Rate limit excedido para IP: ${clientIP}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Demasiados intentos de registro. Inténtalo más tarde.',\n                    retryAfter: Math.ceil((RATE_LIMIT_WINDOW - (now - attempts.lastAttempt)) / 1000)\n                }, {\n                    status: 429\n                });\n            }\n        }\n        // 2. Validar datos de entrada\n        const body = await request.json();\n        const { email, customerName } = body;\n        if (!email || typeof email !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email es requerido y debe ser válido'\n            }, {\n                status: 400\n            });\n        }\n        // Validación básica de email\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Formato de email inválido'\n            }, {\n                status: 400\n            });\n        }\n        // Normalizar email\n        const normalizedEmail = email.toLowerCase().trim();\n        console.log('📧 Procesando registro para:', normalizedEmail);\n        // 3. Actualizar contador de intentos\n        const currentAttempts = registrationAttempts.get(clientIP) || {\n            count: 0,\n            lastAttempt: 0\n        };\n        registrationAttempts.set(clientIP, {\n            count: currentAttempts.count + 1,\n            lastAttempt: now\n        });\n        // 4. Crear cuenta gratuita\n        console.log('🔄 [REGISTER_FREE] Iniciando creación de cuenta gratuita:', {\n            email: normalizedEmail,\n            name: customerName,\n            clientIP,\n            timestamp: new Date().toISOString()\n        });\n        const result = await _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_1__.FreeAccountService.createFreeAccount({\n            email: normalizedEmail,\n            name: customerName || undefined\n        });\n        console.log('📊 [REGISTER_FREE] Resultado de createFreeAccount:', {\n            success: result.success,\n            userId: result.userId,\n            profileId: result.profileId,\n            expiresAt: result.expiresAt,\n            error: result.error\n        });\n        if (!result.success) {\n            console.log('❌ [REGISTER_FREE] Error creando cuenta gratuita:', {\n                error: result.error,\n                email: normalizedEmail,\n                timestamp: new Date().toISOString()\n            });\n            // Si el email ya existe, no es un error de rate limiting\n            if (result.error?.includes('Ya existe una cuenta')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: result.error,\n                    code: 'EMAIL_EXISTS'\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error || 'Error interno al crear la cuenta'\n            }, {\n                status: 500\n            });\n        }\n        console.log('✅ [REGISTER_FREE] Cuenta gratuita creada exitosamente:', {\n            userId: result.userId,\n            profileId: result.profileId,\n            expiresAt: result.expiresAt,\n            email: normalizedEmail\n        });\n        // 5. Enviar email de bienvenida\n        try {\n            if (process.env.RESEND_API_KEY) {\n                const welcomeEmailData = {\n                    from: 'OposiAI <<EMAIL>>',\n                    to: [\n                        normalizedEmail\n                    ],\n                    subject: '🎉 ¡Bienvenido a OposiAI! Tu cuenta gratuita está lista',\n                    html: generateWelcomeEmail(customerName || 'Usuario', result.expiresAt)\n                };\n                await resend.emails.send(welcomeEmailData);\n                console.log('📧 Email de bienvenida enviado');\n            }\n        } catch (emailError) {\n            console.error('⚠️ Error enviando email de bienvenida:', emailError);\n        // No fallar el registro por error de email\n        }\n        // 6. Enviar notificación al administrador\n        try {\n            if (process.env.NOTIFICATION_EMAIL) {\n                const adminNotificationData = {\n                    from: 'OposiAI Notificaciones <<EMAIL>>',\n                    to: [\n                        process.env.NOTIFICATION_EMAIL\n                    ],\n                    subject: '🆓 Nueva Cuenta Gratuita Creada - OposiAI',\n                    html: generateAdminNotification(normalizedEmail, customerName, result.expiresAt)\n                };\n                await resend.emails.send(adminNotificationData);\n                console.log('📧 Notificación de administrador enviada');\n            }\n        } catch (notificationError) {\n            console.error('⚠️ Error enviando notificación de administrador:', notificationError);\n        // No fallar el registro por error de notificación\n        }\n        // 7. Limpiar rate limiting en caso de éxito\n        registrationAttempts.delete(clientIP);\n        const processingTime = Date.now() - startTime;\n        console.log(`🎉 Registro gratuito completado en ${processingTime}ms`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Cuenta gratuita creada exitosamente',\n            data: {\n                userId: result.userId,\n                expiresAt: result.expiresAt,\n                sessionId: `free_${result.userId}_${Date.now()}` // Simular session ID para compatibilidad\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error crítico en registro gratuito:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor',\n            details:  true ? error instanceof Error ? error.message : 'Error desconocido' : 0\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Generar HTML para email de bienvenida\n */ function generateWelcomeEmail(name, expiresAt) {\n    const expirationDate = new Date(expiresAt).toLocaleDateString('es-ES', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n    return `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n      <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin-bottom: 10px;\">¡Bienvenido a OposiAI!</h1>\n        <p style=\"color: #6b7280; font-size: 18px;\">Tu asistente inteligente para oposiciones</p>\n      </div>\n      \n      <div style=\"background-color: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;\">\n        <h2 style=\"color: #1e40af; margin-top: 0;\">¡Hola ${name}!</h2>\n        <p style=\"color: #374151; line-height: 1.6;\">\n          Tu cuenta gratuita de OposiAI ha sido creada exitosamente. Ya puedes empezar a usar \n          todas las funcionalidades disponibles en tu plan.\n        </p>\n      </div>\n      \n      <div style=\"background-color: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 25px;\">\n        <h3 style=\"color: #92400e; margin-top: 0;\">📅 Tu cuenta gratuita expira el:</h3>\n        <p style=\"color: #92400e; font-size: 18px; font-weight: bold; margin-bottom: 10px;\">\n          ${expirationDate}\n        </p>\n        <p style=\"color: #92400e; font-size: 14px; margin-bottom: 0;\">\n          Tienes 5 días completos para explorar todas las funcionalidades.\n        </p>\n      </div>\n      \n      <div style=\"margin-bottom: 25px;\">\n        <h3 style=\"color: #1f2937;\">🎯 ¿Qué puedes hacer con tu cuenta gratuita?</h3>\n        <ul style=\"color: #374151; line-height: 1.8;\">\n          <li>📄 Subir hasta 1 documento de estudio</li>\n          <li>❓ Generar hasta 10 preguntas de test</li>\n          <li>🃏 Crear hasta 10 flashcards</li>\n          <li>🗺️ Generar hasta 2 mapas mentales</li>\n          <li>🤖 Usar hasta 50,000 tokens de IA</li>\n        </ul>\n      </div>\n      \n      <div style=\"text-align: center; margin-bottom: 25px;\">\n        <a href=\"${\"http://localhost:3000\"}/app\" \n           style=\"display: inline-block; background-color: #2563eb; color: white; padding: 15px 30px; \n                  text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px;\">\n          🚀 Comenzar a usar OposiAI\n        </a>\n      </div>\n      \n      <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n        <h3 style=\"color: #1f2937; margin-top: 0;\">💡 Consejos para aprovechar al máximo tu cuenta:</h3>\n        <ol style=\"color: #374151; line-height: 1.6;\">\n          <li>Sube tu documento más importante primero</li>\n          <li>Genera tests para evaluar tu conocimiento</li>\n          <li>Usa flashcards para memorizar conceptos clave</li>\n          <li>Crea mapas mentales para visualizar la información</li>\n        </ol>\n      </div>\n      \n      <div style=\"text-align: center; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n        <p style=\"color: #6b7280; font-size: 14px; margin-bottom: 10px;\">\n          ¿Quieres más funcionalidades? Descubre nuestros planes premium\n        </p>\n        <a href=\"${\"http://localhost:3000\"}/payment\" \n           style=\"color: #2563eb; text-decoration: none; font-weight: bold;\">\n          Ver Planes Premium →\n        </a>\n      </div>\n      \n      <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center;\">\n        <p style=\"color: #6b7280; font-size: 12px;\">\n          Este email fue generado automáticamente por OposiAI.<br>\n          Si tienes alguna pregunta, contacta con nuestro soporte.\n        </p>\n      </div>\n    </div>\n  `;\n}\n/**\n * Generar HTML para notificación de administrador\n */ function generateAdminNotification(email, name, expiresAt) {\n    return `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n      <h2 style=\"color: #2563eb;\">🆓 Nueva Cuenta Gratuita Creada</h2>\n      \n      <div style=\"background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0; color: #1e40af;\">Detalles del Usuario</h3>\n        <p><strong>Email:</strong> ${email}</p>\n        <p><strong>Nombre:</strong> ${name || 'No proporcionado'}</p>\n        <p><strong>Plan:</strong> Gratuito (5 días)</p>\n        <p><strong>Expira:</strong> ${new Date(expiresAt).toLocaleString('es-ES')}</p>\n        <p><strong>Fecha de registro:</strong> ${new Date().toLocaleString('es-ES')}</p>\n      </div>\n\n      <div style=\"background-color: #dcfce7; padding: 15px; border-radius: 8px; border-left: 4px solid #16a34a;\">\n        <h4 style=\"margin-top: 0; color: #166534;\">✅ Cuenta Creada Automáticamente</h4>\n        <p style=\"margin-bottom: 0; color: #166534;\">\n          La cuenta fue creada automáticamente por el sistema. No se requiere acción manual.\n        </p>\n      </div>\n\n      <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n        <p style=\"color: #6b7280; font-size: 14px;\">\n          Este email fue generado automáticamente por el sistema de registro gratuito de OposiAI.\n        </p>\n      </div>\n    </div>\n  `;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register-free/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/freeAccountService.ts":
/*!************************************************!*\
  !*** ./src/lib/services/freeAccountService.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FreeAccountService: () => (/* binding */ FreeAccountService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/services/freeAccountService.ts\n// Servicio para gestión automatizada de cuentas gratuitas\n\n\n\nclass FreeAccountService {\n    /**\n   * Crear cuenta gratuita automáticamente\n   */ static async createFreeAccount(request) {\n        try {\n            console.log('🆓 Iniciando creación de cuenta gratuita:', request.email);\n            // 1. Validar que el email no esté ya registrado\n            // Usar getUserByEmail que es más eficiente para verificar existencia\n            try {\n                const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                if (existingUser) {\n                    return {\n                        success: false,\n                        error: 'Ya existe una cuenta con este email'\n                    };\n                }\n            } catch (error) {\n                // Si no se encuentra el usuario, continuamos (esto es lo que queremos)\n                console.log('Usuario no existe, continuando con la creación');\n            }\n            // 2. Calcular fecha de expiración (5 días desde ahora)\n            const expiresAt = new Date();\n            expiresAt.setDate(expiresAt.getDate() + 5);\n            // 3. Crear usuario con invitación\n            const userData = {\n                name: request.name,\n                plan: 'free',\n                free_account: true,\n                expires_at: expiresAt.toISOString(),\n                created_via: 'free_registration',\n                // Flags adicionales para identificación en middleware\n                legitimate_user: true,\n                registration_type: 'automatic_free',\n                registration_timestamp: new Date().toISOString()\n            };\n            console.log('🔄 Creando invitación de usuario con datos:', {\n                email: request.email,\n                userData: userData,\n                timestamp: new Date().toISOString()\n            });\n            const userInvitation = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createUserWithInvitation(request.email, userData);\n            console.log('📊 Resultado de createUserWithInvitation:', {\n                hasUser: !!userInvitation.user,\n                userId: userInvitation.user?.id,\n                userEmail: userInvitation.user?.email,\n                userAud: userInvitation.user?.aud,\n                userRole: userInvitation.user?.role,\n                emailConfirmed: userInvitation.user?.email_confirmed_at,\n                createdAt: userInvitation.user?.created_at,\n                userMetadata: userInvitation.user?.user_metadata,\n                appMetadata: userInvitation.user?.app_metadata,\n                fullResponse: userInvitation\n            });\n            if (!userInvitation.user) {\n                throw new Error('Error creando usuario - no se devolvió objeto user');\n            }\n            console.log('✅ Usuario gratuito creado exitosamente:', {\n                userId: userInvitation.user.id,\n                email: userInvitation.user.email,\n                needsEmailConfirmation: !userInvitation.user.email_confirmed_at\n            });\n            // 4. Crear perfil de usuario y registrar historial de forma atómica\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            if (!planConfig) {\n                throw new Error('Configuración de plan gratuito no encontrada');\n            }\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const profileData = {\n                // Preparamos el objeto JSON para la función\n                subscription_plan: 'free',\n                monthly_token_limit: (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)('free'),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                // Las cuentas gratuitas se consideran \"verificadas\"\n                stripe_customer_id: null,\n                stripe_subscription_id: null,\n                last_payment_date: null,\n                auto_renew: false,\n                plan_expires_at: expiresAt.toISOString(),\n                plan_features: planConfig.features,\n                security_flags: {\n                    created_via_free_registration: true,\n                    free_account: true,\n                    expires_at: expiresAt.toISOString(),\n                    activation_date: new Date().toISOString(),\n                    usage_count: {\n                        documents: 0,\n                        tests: 0,\n                        flashcards: 0,\n                        mindMaps: 0,\n                        tokens: 0\n                    }\n                }\n            };\n            // Las cuentas gratuitas no tienen transacción asociada, pasar null\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: userInvitation.user.id,\n                p_transaction_id: null,\n                // NULL para cuentas gratuitas\n                p_profile_data: profileData\n            }).single(); // .single() es importante para obtener un único resultado\n            if (rpcError) {\n                console.error('Error al ejecutar la función create_user_profile_and_history:', rpcError);\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);\n            console.log('🎉 Cuenta gratuita creada exitosamente');\n            return {\n                success: true,\n                userId: userInvitation.user.id,\n                profileId: profileId,\n                // Usamos el ID devuelto por la función\n                expiresAt: expiresAt.toISOString()\n            };\n        } catch (error) {\n            console.error('❌ Error creando cuenta gratuita:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Verificar estado de cuenta gratuita\n   */ static async getFreeAccountStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return null;\n            }\n            const now = new Date();\n            const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;\n            if (!expiresAt) {\n                return null;\n            }\n            const isActive = now < expiresAt;\n            const timeDiff = expiresAt.getTime() - now.getTime();\n            const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n            const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));\n            // Obtener contadores de uso desde security_flags\n            // Para tokens, usar current_month_tokens como fuente única de verdad\n            const usageCount = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0 // No usado, se obtiene de current_month_tokens\n            };\n            // Sobrescribir tokens con la fuente de verdad\n            usageCount.tokens = profile.current_month_tokens || 0;\n            // Límites del plan gratuito (trial de 5 días)\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            const limits = {\n                documents: planConfig?.limits.documents || 1,\n                tests: planConfig?.limits.testsForTrial || 10,\n                // Límite total para trial\n                flashcards: planConfig?.limits.flashcardsForTrial || 10,\n                // Límite total para trial\n                mindMaps: planConfig?.limits.mindMapsForTrial || 2,\n                // Límite total para trial\n                tokens: planConfig?.limits.tokensForTrial || 50000 // Límite total para trial\n            };\n            return {\n                isActive,\n                expiresAt: expiresAt.toISOString(),\n                daysRemaining,\n                hoursRemaining,\n                usageCount,\n                limits\n            };\n        } catch (error) {\n            console.error('Error obteniendo estado de cuenta gratuita:', error);\n            return null;\n        }\n    }\n    /**\n   * Incrementar contador de uso\n   * NOTA: Para tokens, el incremento se maneja automáticamente por tokenTracker.ts\n   * que actualiza current_month_tokens. Este método solo maneja features no-token.\n   */ static async incrementUsageCount(userId, feature, amount = 1) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return false;\n            }\n            const currentUsage = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0 // Mantenemos para compatibilidad, pero no se actualiza aquí\n            };\n            currentUsage[feature] = (currentUsage[feature] || 0) + amount;\n            // Actualizar solo security_flags (no current_month_tokens)\n            // Los tokens se manejan exclusivamente por tokenTracker.ts\n            const updateData = {\n                security_flags: _objectSpread(_objectSpread({}, profile.security_flags), {}, {\n                    usage_count: currentUsage\n                }),\n                updated_at: new Date().toISOString()\n            };\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update(updateData).eq('user_id', userId);\n            return true;\n        } catch (error) {\n            console.error('Error incrementando contador de uso:', error);\n            return false;\n        }\n    }\n    /**\n   * Verificar si se puede realizar una acción\n   */ static async canPerformAction(userId, feature, amount = 1) {\n        try {\n            const status = await this.getFreeAccountStatus(userId);\n            if (!status) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta no encontrada o no es gratuita'\n                };\n            }\n            if (!status.isActive) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta gratuita expirada'\n                };\n            }\n            const currentUsage = status.usageCount[feature] || 0;\n            const limit = status.limits[feature];\n            const remaining = limit - currentUsage;\n            if (currentUsage + amount > limit) {\n                return {\n                    allowed: false,\n                    reason: `Límite de ${feature} alcanzado (${limit})`,\n                    remaining: Math.max(0, remaining)\n                };\n            }\n            return {\n                allowed: true,\n                remaining: remaining - amount\n            };\n        } catch (error) {\n            console.error('Error verificando acción:', error);\n            return {\n                allowed: false,\n                reason: 'Error interno'\n            };\n        }\n    }\n    /**\n   * Limpiar cuentas gratuitas expiradas\n   */ static async cleanupExpiredAccounts() {\n        try {\n            console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');\n            const now = new Date().toISOString();\n            // Buscar cuentas gratuitas expiradas\n            const { data: expiredProfiles, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, id').eq('subscription_plan', 'free').lt('plan_expires_at', now);\n            if (error) {\n                throw new Error(`Error buscando cuentas expiradas: ${error.message}`);\n            }\n            if (!expiredProfiles || expiredProfiles.length === 0) {\n                console.log('✅ No hay cuentas expiradas para limpiar');\n                return {\n                    cleaned: 0,\n                    errors: []\n                };\n            }\n            console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas`);\n            const errors = [];\n            let cleaned = 0;\n            for (const profile of expiredProfiles){\n                try {\n                    // Desactivar usuario en auth\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.updateUserById(profile.user_id, {\n                        user_metadata: {\n                            account_disabled: true,\n                            disabled_reason: 'free_account_expired'\n                        }\n                    });\n                    // Marcar perfil como inactivo\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: false,\n                        security_flags: {\n                            account_disabled: true,\n                            disabled_at: new Date().toISOString(),\n                            disabled_reason: 'free_account_expired'\n                        }\n                    }).eq('user_id', profile.user_id);\n                    cleaned++;\n                } catch (cleanupError) {\n                    const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);\n            return {\n                cleaned,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en limpieza de cuentas:', error);\n            return {\n                cleaned: 0,\n                errors: [\n                    error instanceof Error ? error.message : 'Error desconocido'\n                ]\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2ZyZWVBY2NvdW50U2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUVnRjtBQUNHO0FBQy9CO0FBb0M3QyxNQUFNSSxrQkFBa0IsQ0FBQztJQUU5Qjs7R0FFRixHQUNFLGFBQWFDLGlCQUFpQkEsQ0FBQ0MsT0FBaUMsRUFBOEI7UUFDNUYsSUFBSTtZQUNGQyxPQUFPLENBQUNDLEdBQUcsQ0FBQywyQ0FBMkMsRUFBRUYsT0FBTyxDQUFDRyxLQUFLLENBQUM7WUFFdkU7WUFDQTtZQUNBLElBQUk7Z0JBQ0YsTUFBTUMsWUFBWSxHQUFHLE1BQU1WLHFFQUFvQixDQUFDVyxjQUFjLENBQUNMLE9BQU8sQ0FBQ0csS0FBSyxDQUFDO2dCQUM3RSxJQUFJQyxZQUFZLEVBQUU7b0JBQ2hCLE9BQU87d0JBQ0xFLE9BQU8sRUFBRSxLQUFLO3dCQUNkQyxLQUFLLEVBQUU7b0JBQ1QsQ0FBQztnQkFDSDtZQUNGLENBQUMsQ0FBQyxPQUFPQSxLQUFLLEVBQUU7Z0JBQ2Q7Z0JBQ0FOLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdEQUFnRCxDQUFDO1lBQy9EO1lBRUE7WUFDQSxNQUFNTSxTQUFTLEdBQUcsSUFBSUMsSUFBSSxDQUFDLENBQUM7WUFDNUJELFNBQVMsQ0FBQ0UsT0FBTyxDQUFDRixTQUFTLENBQUNHLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBRTFDO1lBQ0EsTUFBTUMsUUFBUSxHQUFHO2dCQUNmQyxJQUFJLEVBQUViLE9BQU8sQ0FBQ2EsSUFBSTtnQkFDbEJDLElBQUksRUFBRSxNQUFNO2dCQUNaQyxZQUFZLEVBQUUsSUFBSTtnQkFDbEJDLFVBQVUsRUFBRVIsU0FBUyxDQUFDUyxXQUFXLENBQUMsQ0FBQztnQkFDbkNDLFdBQVcsRUFBRSxtQkFBbUI7Z0JBQ2hDO2dCQUNBQyxlQUFlLEVBQUUsSUFBSTtnQkFDckJDLGlCQUFpQixFQUFFLGdCQUFnQjtnQkFDbkNDLHNCQUFzQixFQUFFLElBQUlaLElBQUksQ0FBQyxDQUFDLENBQUNRLFdBQVcsQ0FBQztZQUNqRCxDQUFDO1lBRURoQixPQUFPLENBQUNDLEdBQUcsQ0FBQyw2Q0FBNkMsRUFBRTtnQkFDekRDLEtBQUssRUFBRUgsT0FBTyxDQUFDRyxLQUFLO2dCQUNwQlMsUUFBUSxFQUFFQSxRQUFRO2dCQUNsQlUsU0FBUyxFQUFFLElBQUliLElBQUksQ0FBQyxDQUFDLENBQUNRLFdBQVcsQ0FBQztZQUNwQyxDQUFDLENBQUM7WUFFRixNQUFNTSxjQUFjLEdBQUcsTUFBTTdCLHFFQUFvQixDQUFDOEIsd0JBQXdCLENBQ3hFeEIsT0FBTyxDQUFDRyxLQUFLLEVBQ2JTLFFBQ0YsQ0FBQztZQUVEWCxPQUFPLENBQUNDLEdBQUcsQ0FBQywyQ0FBMkMsRUFBRTtnQkFDdkR1QixPQUFPLEVBQUUsQ0FBQyxDQUFDRixjQUFjLENBQUNHLElBQUk7Z0JBQzlCQyxNQUFNLEVBQUVKLGNBQWMsQ0FBQ0csSUFBSSxFQUFFRSxFQUFFO2dCQUMvQkMsU0FBUyxFQUFFTixjQUFjLENBQUNHLElBQUksRUFBRXZCLEtBQUs7Z0JBQ3JDMkIsT0FBTyxFQUFFUCxjQUFjLENBQUNHLElBQUksRUFBRUssR0FBRztnQkFDakNDLFFBQVEsRUFBRVQsY0FBYyxDQUFDRyxJQUFJLEVBQUVPLElBQUk7Z0JBQ25DQyxjQUFjLEVBQUVYLGNBQWMsQ0FBQ0csSUFBSSxFQUFFUyxrQkFBa0I7Z0JBQ3ZEQyxTQUFTLEVBQUViLGNBQWMsQ0FBQ0csSUFBSSxFQUFFVyxVQUFVO2dCQUMxQ0MsWUFBWSxFQUFFZixjQUFjLENBQUNHLElBQUksRUFBRWEsYUFBYTtnQkFDaERDLFdBQVcsRUFBRWpCLGNBQWMsQ0FBQ0csSUFBSSxFQUFFZSxZQUFZO2dCQUM5Q0MsWUFBWSxFQUFFbkI7WUFDaEIsQ0FBQyxDQUFDO1lBRUYsSUFBSSxDQUFDQSxjQUFjLENBQUNHLElBQUksRUFBRTtnQkFDeEIsTUFBTSxJQUFJaUIsS0FBSyxDQUFDLG9EQUFvRCxDQUFDO1lBQ3ZFO1lBRUExQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyx5Q0FBeUMsRUFBRTtnQkFDckR5QixNQUFNLEVBQUVKLGNBQWMsQ0FBQ0csSUFBSSxDQUFDRSxFQUFFO2dCQUM5QnpCLEtBQUssRUFBRW9CLGNBQWMsQ0FBQ0csSUFBSSxDQUFDdkIsS0FBSztnQkFDaEN5QyxzQkFBc0IsRUFBRSxDQUFDckIsY0FBYyxDQUFDRyxJQUFJLENBQUNTLGtCQUFBQTtZQUMvQyxDQUFDLENBQUM7WUFFRjtZQUNBLE1BQU1VLFVBQVUsR0FBR2xELDJFQUFvQixDQUFDLE1BQU0sQ0FBQztZQUMvQyxJQUFJLENBQUNrRCxVQUFVLEVBQUU7Z0JBQ2YsTUFBTSxJQUFJRixLQUFLLENBQUMsOENBQThDLENBQUM7WUFDakU7WUFFQSxNQUFNRyxZQUFZLEdBQUcsSUFBSXJDLElBQUksQ0FBQyxDQUFDLENBQUNRLFdBQVcsQ0FBQyxDQUFDLENBQUM4QixLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEtBQUs7WUFDakUsTUFBTUMsV0FBVyxHQUFHO2dCQUFFO2dCQUNwQkMsaUJBQWlCLEVBQUUsTUFBTTtnQkFDekJDLG1CQUFtQixFQUFFdEQsMkVBQW9CLENBQUMsTUFBTSxDQUFDO2dCQUNqRHVELG9CQUFvQixFQUFFLENBQUM7Z0JBQ3ZCQyxhQUFhLEVBQUVOLFlBQVk7Z0JBQzNCTyxnQkFBZ0IsRUFBRSxJQUFJO2dCQUFFO2dCQUN4QkMsa0JBQWtCLEVBQUUsSUFBSTtnQkFDeEJDLHNCQUFzQixFQUFFLElBQUk7Z0JBQzVCQyxpQkFBaUIsRUFBRSxJQUFJO2dCQUN2QkMsVUFBVSxFQUFFLEtBQUs7Z0JBQ2pCQyxlQUFlLEVBQUVsRCxTQUFTLENBQUNTLFdBQVcsQ0FBQyxDQUFDO2dCQUN4QzBDLGFBQWEsRUFBRWQsVUFBVSxDQUFDZSxRQUFRO2dCQUNsQ0MsY0FBYyxFQUFFO29CQUNkQyw2QkFBNkIsRUFBRSxJQUFJO29CQUNuQy9DLFlBQVksRUFBRSxJQUFJO29CQUNsQkMsVUFBVSxFQUFFUixTQUFTLENBQUNTLFdBQVcsQ0FBQyxDQUFDO29CQUNuQzhDLGVBQWUsRUFBRSxJQUFJdEQsSUFBSSxDQUFDLENBQUMsQ0FBQ1EsV0FBVyxDQUFDLENBQUM7b0JBQ3pDK0MsV0FBVyxFQUFFO3dCQUNYQyxTQUFTLEVBQUUsQ0FBQzt3QkFDWkMsS0FBSyxFQUFFLENBQUM7d0JBQ1JDLFVBQVUsRUFBRSxDQUFDO3dCQUNiQyxRQUFRLEVBQUUsQ0FBQzt3QkFDWEMsTUFBTSxFQUFFO29CQUNWO2dCQUNGO1lBQ0YsQ0FBQztZQUVEO1lBQ0EsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLGNBQWMsRUFBRWhFLEtBQUssRUFBRWlFLFFBQUFBLEVBQVUsR0FBRyxNQUFNM0UsOERBQWEsQ0FDbEU0RSxHQUFHLENBQUMsaUNBQWlDLEVBQUU7Z0JBQ3RDQyxTQUFTLEVBQUVuRCxjQUFjLENBQUNHLElBQUksQ0FBQ0UsRUFBRTtnQkFDakMrQyxnQkFBZ0IsRUFBRSxJQUFJO2dCQUFFO2dCQUN4QkMsY0FBYyxFQUFFNUI7WUFDbEIsQ0FBQyxDQUFDLENBQ0Q2QixNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFYixJQUFJTCxRQUFRLEVBQUU7Z0JBQ1p2RSxPQUFPLENBQUNNLEtBQUssQ0FBQywrREFBK0QsRUFBRWlFLFFBQVEsQ0FBQztnQkFDeEYsTUFBTSxJQUFJN0IsS0FBSyxDQUFFLDRDQUEyQzZCLFFBQVEsQ0FBQ00sT0FBUSxFQUFDLENBQUM7WUFDakY7WUFFQSxNQUFNQyxTQUFTLEdBQUlSLGNBQWMsQ0FBU1Msa0JBQWtCO1lBQzVEL0UsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUVBQWlFLEVBQUU2RSxTQUFTLENBQUM7WUFFekY5RSxPQUFPLENBQUNDLEdBQUcsQ0FBQyx3Q0FBd0MsQ0FBQztZQUVyRCxPQUFPO2dCQUNMSSxPQUFPLEVBQUUsSUFBSTtnQkFDYnFCLE1BQU0sRUFBRUosY0FBYyxDQUFDRyxJQUFJLENBQUNFLEVBQUU7Z0JBQzlCbUQsU0FBUyxFQUFFQSxTQUFTO2dCQUFFO2dCQUN0QnZFLFNBQVMsRUFBRUEsU0FBUyxDQUFDUyxXQUFXLENBQUM7WUFDbkMsQ0FBQztRQUVILENBQUMsQ0FBQyxPQUFPVixLQUFLLEVBQUU7WUFDZE4sT0FBTyxDQUFDTSxLQUFLLENBQUMsa0NBQWtDLEVBQUVBLEtBQUssQ0FBQztZQUN4RCxPQUFPO2dCQUNMRCxPQUFPLEVBQUUsS0FBSztnQkFDZEMsS0FBSyxFQUFFQSxLQUFLLFlBQVlvQyxLQUFLLEdBQUdwQyxLQUFLLENBQUN1RSxPQUFPLEdBQUc7WUFDbEQsQ0FBQztRQUNIO0lBQ0Y7SUFFQTs7R0FFRixHQUNFLGFBQWFHLG9CQUFvQkEsQ0FBQ3RELE1BQWMsRUFBcUM7UUFDbkYsSUFBSTtZQUNGLE1BQU11RCxPQUFPLEdBQUcsTUFBTXhGLHFFQUFvQixDQUFDeUYsY0FBYyxDQUFDeEQsTUFBTSxDQUFDO1lBRWpFLElBQUksQ0FBQ3VELE9BQU8sSUFBSUEsT0FBTyxDQUFDakMsaUJBQWlCLEtBQUssTUFBTSxFQUFFO2dCQUNwRCxPQUFPLElBQUk7WUFDYjtZQUVBLE1BQU1tQyxHQUFHLEdBQUcsSUFBSTNFLElBQUksQ0FBQyxDQUFDO1lBQ3RCLE1BQU1ELFNBQVMsR0FBRzBFLE9BQU8sQ0FBQ3hCLGVBQWUsR0FBRyxJQUFJakQsSUFBSSxDQUFDeUUsT0FBTyxDQUFDeEIsZUFBZSxDQUFDLEdBQUcsSUFBSTtZQUVwRixJQUFJLENBQUNsRCxTQUFTLEVBQUU7Z0JBQ2QsT0FBTyxJQUFJO1lBQ2I7WUFFQSxNQUFNNkUsUUFBUSxHQUFHRCxHQUFHLEdBQUc1RSxTQUFTO1lBQ2hDLE1BQU04RSxRQUFRLEdBQUc5RSxTQUFTLENBQUMrRSxPQUFPLENBQUMsQ0FBQyxHQUFHSCxHQUFHLENBQUNHLE9BQU8sQ0FBQyxDQUFDO1lBQ3BELE1BQU1DLGFBQWEsR0FBR0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFRCxJQUFJLENBQUNFLElBQUksQ0FBQ0wsUUFBUSxJQUFJLElBQUksR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEdBQUUsQ0FBQyxDQUFDLENBQUM7WUFDOUUsTUFBTU0sY0FBYyxHQUFHSCxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUVELElBQUksQ0FBQ0UsSUFBSSxDQUFDTCxRQUFRLElBQUksSUFBSSxHQUFHLEVBQUUsR0FBRyxHQUFFLENBQUMsQ0FBQyxDQUFDO1lBRTFFO1lBQ0E7WUFDQSxNQUFNTyxVQUFVLEdBQUdYLE9BQU8sQ0FBQ3JCLGNBQWMsRUFBRUcsV0FBVyxJQUFJO2dCQUN4REMsU0FBUyxFQUFFLENBQUM7Z0JBQ1pDLEtBQUssRUFBRSxDQUFDO2dCQUNSQyxVQUFVLEVBQUUsQ0FBQztnQkFDYkMsUUFBUSxFQUFFLENBQUM7Z0JBQ1hDLE1BQU0sRUFBRSxDQUFDLENBQUM7WUFDWixDQUFDO1lBRUQ7WUFDQXdCLFVBQVUsQ0FBQ3hCLE1BQU0sR0FBR2EsT0FBTyxDQUFDL0Isb0JBQW9CLElBQUksQ0FBQztZQUVyRDtZQUNBLE1BQU1OLFVBQVUsR0FBR2xELDJFQUFvQixDQUFDLE1BQU0sQ0FBQztZQUMvQyxNQUFNbUcsTUFBTSxHQUFHO2dCQUNiN0IsU0FBUyxFQUFFcEIsVUFBVSxFQUFFaUQsTUFBTSxDQUFDN0IsU0FBUyxJQUFJLENBQUM7Z0JBQzVDQyxLQUFLLEVBQUVyQixVQUFVLEVBQUVpRCxNQUFNLENBQUNDLGFBQWEsSUFBSSxFQUFFO2dCQUFTO2dCQUN0RDVCLFVBQVUsRUFBRXRCLFVBQVUsRUFBRWlELE1BQU0sQ0FBQ0Usa0JBQWtCLElBQUksRUFBRTtnQkFBRztnQkFDMUQ1QixRQUFRLEVBQUV2QixVQUFVLEVBQUVpRCxNQUFNLENBQUNHLGdCQUFnQixJQUFJLENBQUM7Z0JBQVE7Z0JBQzFENUIsTUFBTSxFQUFFeEIsVUFBVSxFQUFFaUQsTUFBTSxDQUFDSSxjQUFjLElBQUksS0FBSyxDQUFNO1lBQzFELENBQUM7WUFFRCxPQUFPO2dCQUNMYixRQUFRO2dCQUNSN0UsU0FBUyxFQUFFQSxTQUFTLENBQUNTLFdBQVcsQ0FBQyxDQUFDO2dCQUNsQ3VFLGFBQWE7Z0JBQ2JJLGNBQWM7Z0JBQ2RDLFVBQVU7Z0JBQ1ZDO1lBQ0YsQ0FBQztRQUVILENBQUMsQ0FBQyxPQUFPdkYsS0FBSyxFQUFFO1lBQ2ROLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLDZDQUE2QyxFQUFFQSxLQUFLLENBQUM7WUFDbkUsT0FBTyxJQUFJO1FBQ2I7SUFDRjtJQUVBOzs7O0dBSUYsR0FDRSxhQUFhNEYsbUJBQW1CQSxDQUM5QnhFLE1BQWMsRUFDZHlFLE9BQTBELEVBQzFEQyxNQUFjLEdBQUcsQ0FBQyxFQUNBO1FBQ2xCLElBQUk7WUFDRixNQUFNbkIsT0FBTyxHQUFHLE1BQU14RixxRUFBb0IsQ0FBQ3lGLGNBQWMsQ0FBQ3hELE1BQU0sQ0FBQztZQUVqRSxJQUFJLENBQUN1RCxPQUFPLElBQUlBLE9BQU8sQ0FBQ2pDLGlCQUFpQixLQUFLLE1BQU0sRUFBRTtnQkFDcEQsT0FBTyxLQUFLO1lBQ2Q7WUFFQSxNQUFNcUQsWUFBWSxHQUFHcEIsT0FBTyxDQUFDckIsY0FBYyxFQUFFRyxXQUFXLElBQUk7Z0JBQzFEQyxTQUFTLEVBQUUsQ0FBQztnQkFDWkMsS0FBSyxFQUFFLENBQUM7Z0JBQ1JDLFVBQVUsRUFBRSxDQUFDO2dCQUNiQyxRQUFRLEVBQUUsQ0FBQztnQkFDWEMsTUFBTSxFQUFFLENBQUMsQ0FBQztZQUNaLENBQUM7WUFFRGlDLFlBQVksQ0FBQ0YsT0FBTyxDQUFDLEdBQUcsQ0FBQ0UsWUFBWSxDQUFDRixPQUFPLENBQUMsS0FBSSxDQUFDLEdBQUlDLE1BQU07WUFFN0Q7WUFDQTtZQUNBLE1BQU1FLFVBQXdDLEdBQUc7Z0JBQy9DMUMsY0FBYyxFQUFBMkMsYUFBQSxDQUFBQSxhQUFBLEtBQ1R0QixPQUFPLENBQUNyQixjQUFjO29CQUN6QkcsV0FBVyxFQUFFc0M7Z0JBQVksRUFDMUI7Z0JBQ0RHLFVBQVUsRUFBRSxJQUFJaEcsSUFBSSxDQUFDLENBQUMsQ0FBQ1EsV0FBVyxDQUFDO1lBQ3JDLENBQUM7WUFFRCxNQUFNcEIsOERBQWEsQ0FDaEI2RyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQ3JCQyxNQUFNLENBQUNKLFVBQVUsQ0FBQyxDQUNsQkssRUFBRSxDQUFDLFNBQVMsRUFBRWpGLE1BQU0sQ0FBQztZQUV4QixPQUFPLElBQUk7UUFFYixDQUFDLENBQUMsT0FBT3BCLEtBQUssRUFBRTtZQUNkTixPQUFPLENBQUNNLEtBQUssQ0FBQyxzQ0FBc0MsRUFBRUEsS0FBSyxDQUFDO1lBQzVELE9BQU8sS0FBSztRQUNkO0lBQ0Y7SUFFQTs7R0FFRixHQUNFLGFBQWFzRyxnQkFBZ0JBLENBQzNCbEYsTUFBYyxFQUNkeUUsT0FBcUUsRUFDckVDLE1BQWMsR0FBRyxDQUFDLEVBQ2tEO1FBQ3BFLElBQUk7WUFDRixNQUFNUyxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUM3QixvQkFBb0IsQ0FBQ3RELE1BQU0sQ0FBQztZQUV0RCxJQUFJLENBQUNtRixNQUFNLEVBQUU7Z0JBQ1gsT0FBTztvQkFBRUMsT0FBTyxFQUFFLEtBQUs7b0JBQUVDLE1BQU0sRUFBRTtnQkFBd0MsQ0FBQztZQUM1RTtZQUVBLElBQUksQ0FBQ0YsTUFBTSxDQUFDekIsUUFBUSxFQUFFO2dCQUNwQixPQUFPO29CQUFFMEIsT0FBTyxFQUFFLEtBQUs7b0JBQUVDLE1BQU0sRUFBRTtnQkFBMkIsQ0FBQztZQUMvRDtZQUVBLE1BQU1WLFlBQVksR0FBR1EsTUFBTSxDQUFDakIsVUFBVSxDQUFDTyxPQUFPLENBQUMsSUFBSSxDQUFDO1lBQ3BELE1BQU1hLEtBQUssR0FBR0gsTUFBTSxDQUFDaEIsTUFBTSxDQUFDTSxPQUFPLENBQUM7WUFDcEMsTUFBTWMsU0FBUyxHQUFHRCxLQUFLLEdBQUdYLFlBQVk7WUFFdEMsSUFBSUEsWUFBWSxHQUFHRCxNQUFNLEdBQUdZLEtBQUssRUFBRTtnQkFDakMsT0FBTztvQkFDTEYsT0FBTyxFQUFFLEtBQUs7b0JBQ2RDLE1BQU0sRUFBRyxhQUFZWixPQUFRLGVBQWNhLEtBQU0sR0FBRTtvQkFDbkRDLFNBQVMsRUFBRXpCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRXdCLFNBQVM7Z0JBQ2xDLENBQUM7WUFDSDtZQUVBLE9BQU87Z0JBQ0xILE9BQU8sRUFBRSxJQUFJO2dCQUNiRyxTQUFTLEVBQUVBLFNBQVMsR0FBR2I7WUFDekIsQ0FBQztRQUVILENBQUMsQ0FBQyxPQUFPOUYsS0FBSyxFQUFFO1lBQ2ROLE9BQU8sQ0FBQ00sS0FBSyxDQUFDLDJCQUEyQixFQUFFQSxLQUFLLENBQUM7WUFDakQsT0FBTztnQkFBRXdHLE9BQU8sRUFBRSxLQUFLO2dCQUFFQyxNQUFNLEVBQUU7WUFBZ0IsQ0FBQztRQUNwRDtJQUNGO0lBRUE7O0dBRUYsR0FDRSxhQUFhRyxzQkFBc0JBLENBQUEsRUFHaEM7UUFDRCxJQUFJO1lBQ0ZsSCxPQUFPLENBQUNDLEdBQUcsQ0FBQyxzREFBc0QsQ0FBQztZQUVuRSxNQUFNa0YsR0FBRyxHQUFHLElBQUkzRSxJQUFJLENBQUMsQ0FBQyxDQUFDUSxXQUFXLENBQUMsQ0FBQztZQUVwQztZQUNBLE1BQU0sRUFBRXFELElBQUksRUFBRThDLGVBQWUsRUFBRTdHLEtBQUFBLEVBQU8sR0FBRyxNQUFNViw4REFBYSxDQUN6RDZHLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FDckJXLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FDckJULEVBQUUsQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FDL0JVLEVBQUUsQ0FBQyxpQkFBaUIsRUFBRWxDLEdBQUcsQ0FBQztZQUU3QixJQUFJN0UsS0FBSyxFQUFFO2dCQUNULE1BQU0sSUFBSW9DLEtBQUssQ0FBRSxxQ0FBb0NwQyxLQUFLLENBQUN1RSxPQUFRLEVBQUMsQ0FBQztZQUN2RTtZQUVBLElBQUksQ0FBQ3NDLGVBQWUsSUFBSUEsZUFBZSxDQUFDRyxNQUFNLEtBQUssQ0FBQyxFQUFFO2dCQUNwRHRILE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlDQUF5QyxDQUFDO2dCQUN0RCxPQUFPO29CQUFFc0gsT0FBTyxFQUFFLENBQUM7b0JBQUVDLE1BQU0sRUFBRTtnQkFBRyxDQUFDO1lBQ25DO1lBRUF4SCxPQUFPLENBQUNDLEdBQUcsQ0FBRSxtQkFBa0JrSCxlQUFlLENBQUNHLE1BQU8sb0JBQW1CLENBQUM7WUFFMUUsTUFBTUUsTUFBZ0IsR0FBRyxFQUFFO1lBQzNCLElBQUlELE9BQU8sR0FBRyxDQUFDO1lBRWYsS0FBSyxNQUFNdEMsT0FBTyxJQUFJa0MsZUFBZSxDQUFFO2dCQUNyQyxJQUFJO29CQUNGO29CQUNBLE1BQU12SCw4REFBYSxDQUFDNkgsSUFBSSxDQUFDQyxLQUFLLENBQUNDLGNBQWMsQ0FBQzFDLE9BQU8sQ0FBQzJDLE9BQU8sRUFBRTt3QkFDN0R0RixhQUFhLEVBQUU7NEJBQUV1RixnQkFBZ0IsRUFBRSxJQUFJOzRCQUFFQyxlQUFlLEVBQUU7d0JBQXVCO29CQUNuRixDQUFDLENBQUM7b0JBRUY7b0JBQ0EsTUFBTWxJLDhEQUFhLENBQ2hCNkcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUNyQkMsTUFBTSxDQUFDO3dCQUNOdEQsZ0JBQWdCLEVBQUUsS0FBSzt3QkFDdkJRLGNBQWMsRUFBRTs0QkFDZGlFLGdCQUFnQixFQUFFLElBQUk7NEJBQ3RCRSxXQUFXLEVBQUUsSUFBSXZILElBQUksQ0FBQyxDQUFDLENBQUNRLFdBQVcsQ0FBQyxDQUFDOzRCQUNyQzhHLGVBQWUsRUFBRTt3QkFDbkI7b0JBQ0YsQ0FBQyxDQUFDLENBQ0RuQixFQUFFLENBQUMsU0FBUyxFQUFFMUIsT0FBTyxDQUFDMkMsT0FBTyxDQUFDO29CQUVqQ0wsT0FBTyxFQUFFO2dCQUVYLENBQUMsQ0FBQyxPQUFPUyxZQUFZLEVBQUU7b0JBQ3JCLE1BQU1DLFFBQVEsR0FBSSwyQkFBMEJoRCxPQUFPLENBQUMyQyxPQUFRLEtBQUlJLFlBQWEsRUFBQztvQkFDOUVoSSxPQUFPLENBQUNNLEtBQUssQ0FBQzJILFFBQVEsQ0FBQztvQkFDdkJULE1BQU0sQ0FBQ1UsSUFBSSxDQUFDRCxRQUFRLENBQUM7Z0JBQ3ZCO1lBQ0Y7WUFFQWpJLE9BQU8sQ0FBQ0MsR0FBRyxDQUFFLDBCQUF5QnNILE9BQVEsd0JBQXVCQyxNQUFNLENBQUNGLE1BQU8sVUFBUyxDQUFDO1lBRTdGLE9BQU87Z0JBQUVDLE9BQU87Z0JBQUVDO1lBQU8sQ0FBQztRQUU1QixDQUFDLENBQUMsT0FBT2xILEtBQUssRUFBRTtZQUNkTixPQUFPLENBQUNNLEtBQUssQ0FBQyxpQ0FBaUMsRUFBRUEsS0FBSyxDQUFDO1lBQ3ZELE9BQU87Z0JBQ0xpSCxPQUFPLEVBQUUsQ0FBQztnQkFDVkMsTUFBTSxFQUFFO29CQUFDbEgsS0FBSyxZQUFZb0MsS0FBSyxHQUFHcEMsS0FBSyxDQUFDdUUsT0FBTyxHQUFHLG1CQUFtQjtpQkFBQTtZQUN2RSxDQUFDO1FBQ0g7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTNcXHNyY1xcbGliXFxzZXJ2aWNlc1xcZnJlZUFjY291bnRTZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9saWIvc2VydmljZXMvZnJlZUFjY291bnRTZXJ2aWNlLnRzXG4vLyBTZXJ2aWNpbyBwYXJhIGdlc3Rpw7NuIGF1dG9tYXRpemFkYSBkZSBjdWVudGFzIGdyYXR1aXRhc1xuXG5pbXBvcnQgeyBTdXBhYmFzZUFkbWluU2VydmljZSwgRXh0ZW5kZWRVc2VyUHJvZmlsZSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL2FkbWluJztcbmltcG9ydCB7IGdldFBsYW5Db25maWd1cmF0aW9uLCBnZXRUb2tlbkxpbWl0Rm9yUGxhbiB9IGZyb20gJ0AvbGliL3V0aWxzL3BsYW5MaW1pdHMnO1xuaW1wb3J0IHsgc3VwYWJhc2VBZG1pbiB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL2FkbWluJztcblxuZXhwb3J0IGludGVyZmFjZSBDcmVhdGVGcmVlQWNjb3VudFJlcXVlc3Qge1xuICBlbWFpbDogc3RyaW5nO1xuICBuYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEZyZWVBY2NvdW50UmVzdWx0IHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgdXNlcklkPzogc3RyaW5nO1xuICBwcm9maWxlSWQ/OiBzdHJpbmc7XG4gIGVycm9yPzogc3RyaW5nO1xuICBleHBpcmVzQXQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRnJlZUFjY291bnRTdGF0dXMge1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgZXhwaXJlc0F0OiBzdHJpbmcgfCBudWxsO1xuICBkYXlzUmVtYWluaW5nOiBudW1iZXI7XG4gIGhvdXJzUmVtYWluaW5nOiBudW1iZXI7XG4gIHVzYWdlQ291bnQ6IHtcbiAgICBkb2N1bWVudHM6IG51bWJlcjtcbiAgICB0ZXN0czogbnVtYmVyO1xuICAgIGZsYXNoY2FyZHM6IG51bWJlcjtcbiAgICBtaW5kTWFwczogbnVtYmVyO1xuICAgIHRva2VuczogbnVtYmVyO1xuICB9O1xuICBsaW1pdHM6IHtcbiAgICBkb2N1bWVudHM6IG51bWJlcjtcbiAgICB0ZXN0czogbnVtYmVyO1xuICAgIGZsYXNoY2FyZHM6IG51bWJlcjtcbiAgICBtaW5kTWFwczogbnVtYmVyO1xuICAgIHRva2VuczogbnVtYmVyO1xuICB9O1xufVxuXG5leHBvcnQgY2xhc3MgRnJlZUFjY291bnRTZXJ2aWNlIHtcbiAgXG4gIC8qKlxuICAgKiBDcmVhciBjdWVudGEgZ3JhdHVpdGEgYXV0b23DoXRpY2FtZW50ZVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZUZyZWVBY2NvdW50KHJlcXVlc3Q6IENyZWF0ZUZyZWVBY2NvdW50UmVxdWVzdCk6IFByb21pc2U8RnJlZUFjY291bnRSZXN1bHQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CfhpMgSW5pY2lhbmRvIGNyZWFjacOzbiBkZSBjdWVudGEgZ3JhdHVpdGE6JywgcmVxdWVzdC5lbWFpbCk7XG4gICAgICBcbiAgICAgIC8vIDEuIFZhbGlkYXIgcXVlIGVsIGVtYWlsIG5vIGVzdMOpIHlhIHJlZ2lzdHJhZG9cbiAgICAgIC8vIFVzYXIgZ2V0VXNlckJ5RW1haWwgcXVlIGVzIG3DoXMgZWZpY2llbnRlIHBhcmEgdmVyaWZpY2FyIGV4aXN0ZW5jaWFcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGV4aXN0aW5nVXNlciA9IGF3YWl0IFN1cGFiYXNlQWRtaW5TZXJ2aWNlLmdldFVzZXJCeUVtYWlsKHJlcXVlc3QuZW1haWwpO1xuICAgICAgICBpZiAoZXhpc3RpbmdVc2VyKSB7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgZXJyb3I6ICdZYSBleGlzdGUgdW5hIGN1ZW50YSBjb24gZXN0ZSBlbWFpbCdcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBTaSBubyBzZSBlbmN1ZW50cmEgZWwgdXN1YXJpbywgY29udGludWFtb3MgKGVzdG8gZXMgbG8gcXVlIHF1ZXJlbW9zKVxuICAgICAgICBjb25zb2xlLmxvZygnVXN1YXJpbyBubyBleGlzdGUsIGNvbnRpbnVhbmRvIGNvbiBsYSBjcmVhY2nDs24nKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gMi4gQ2FsY3VsYXIgZmVjaGEgZGUgZXhwaXJhY2nDs24gKDUgZMOtYXMgZGVzZGUgYWhvcmEpXG4gICAgICBjb25zdCBleHBpcmVzQXQgPSBuZXcgRGF0ZSgpO1xuICAgICAgZXhwaXJlc0F0LnNldERhdGUoZXhwaXJlc0F0LmdldERhdGUoKSArIDUpO1xuICAgICAgXG4gICAgICAvLyAzLiBDcmVhciB1c3VhcmlvIGNvbiBpbnZpdGFjacOzblxuICAgICAgY29uc3QgdXNlckRhdGEgPSB7XG4gICAgICAgIG5hbWU6IHJlcXVlc3QubmFtZSxcbiAgICAgICAgcGxhbjogJ2ZyZWUnLFxuICAgICAgICBmcmVlX2FjY291bnQ6IHRydWUsXG4gICAgICAgIGV4cGlyZXNfYXQ6IGV4cGlyZXNBdC50b0lTT1N0cmluZygpLFxuICAgICAgICBjcmVhdGVkX3ZpYTogJ2ZyZWVfcmVnaXN0cmF0aW9uJyxcbiAgICAgICAgLy8gRmxhZ3MgYWRpY2lvbmFsZXMgcGFyYSBpZGVudGlmaWNhY2nDs24gZW4gbWlkZGxld2FyZVxuICAgICAgICBsZWdpdGltYXRlX3VzZXI6IHRydWUsXG4gICAgICAgIHJlZ2lzdHJhdGlvbl90eXBlOiAnYXV0b21hdGljX2ZyZWUnLFxuICAgICAgICByZWdpc3RyYXRpb25fdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH07XG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIENyZWFuZG8gaW52aXRhY2nDs24gZGUgdXN1YXJpbyBjb24gZGF0b3M6Jywge1xuICAgICAgICBlbWFpbDogcmVxdWVzdC5lbWFpbCxcbiAgICAgICAgdXNlckRhdGE6IHVzZXJEYXRhLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHVzZXJJbnZpdGF0aW9uID0gYXdhaXQgU3VwYWJhc2VBZG1pblNlcnZpY2UuY3JlYXRlVXNlcldpdGhJbnZpdGF0aW9uKFxuICAgICAgICByZXF1ZXN0LmVtYWlsLFxuICAgICAgICB1c2VyRGF0YVxuICAgICAgKTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk4ogUmVzdWx0YWRvIGRlIGNyZWF0ZVVzZXJXaXRoSW52aXRhdGlvbjonLCB7XG4gICAgICAgIGhhc1VzZXI6ICEhdXNlckludml0YXRpb24udXNlcixcbiAgICAgICAgdXNlcklkOiB1c2VySW52aXRhdGlvbi51c2VyPy5pZCxcbiAgICAgICAgdXNlckVtYWlsOiB1c2VySW52aXRhdGlvbi51c2VyPy5lbWFpbCxcbiAgICAgICAgdXNlckF1ZDogdXNlckludml0YXRpb24udXNlcj8uYXVkLFxuICAgICAgICB1c2VyUm9sZTogdXNlckludml0YXRpb24udXNlcj8ucm9sZSxcbiAgICAgICAgZW1haWxDb25maXJtZWQ6IHVzZXJJbnZpdGF0aW9uLnVzZXI/LmVtYWlsX2NvbmZpcm1lZF9hdCxcbiAgICAgICAgY3JlYXRlZEF0OiB1c2VySW52aXRhdGlvbi51c2VyPy5jcmVhdGVkX2F0LFxuICAgICAgICB1c2VyTWV0YWRhdGE6IHVzZXJJbnZpdGF0aW9uLnVzZXI/LnVzZXJfbWV0YWRhdGEsXG4gICAgICAgIGFwcE1ldGFkYXRhOiB1c2VySW52aXRhdGlvbi51c2VyPy5hcHBfbWV0YWRhdGEsXG4gICAgICAgIGZ1bGxSZXNwb25zZTogdXNlckludml0YXRpb25cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXVzZXJJbnZpdGF0aW9uLnVzZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciBjcmVhbmRvIHVzdWFyaW8gLSBubyBzZSBkZXZvbHZpw7Mgb2JqZXRvIHVzZXInKTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+KchSBVc3VhcmlvIGdyYXR1aXRvIGNyZWFkbyBleGl0b3NhbWVudGU6Jywge1xuICAgICAgICB1c2VySWQ6IHVzZXJJbnZpdGF0aW9uLnVzZXIuaWQsXG4gICAgICAgIGVtYWlsOiB1c2VySW52aXRhdGlvbi51c2VyLmVtYWlsLFxuICAgICAgICBuZWVkc0VtYWlsQ29uZmlybWF0aW9uOiAhdXNlckludml0YXRpb24udXNlci5lbWFpbF9jb25maXJtZWRfYXRcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyA0LiBDcmVhciBwZXJmaWwgZGUgdXN1YXJpbyB5IHJlZ2lzdHJhciBoaXN0b3JpYWwgZGUgZm9ybWEgYXTDs21pY2FcbiAgICAgIGNvbnN0IHBsYW5Db25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbignZnJlZScpO1xuICAgICAgaWYgKCFwbGFuQ29uZmlnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ29uZmlndXJhY2nDs24gZGUgcGxhbiBncmF0dWl0byBubyBlbmNvbnRyYWRhJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCA3KSArICctMDEnO1xuICAgICAgY29uc3QgcHJvZmlsZURhdGEgPSB7IC8vIFByZXBhcmFtb3MgZWwgb2JqZXRvIEpTT04gcGFyYSBsYSBmdW5jacOzblxuICAgICAgICBzdWJzY3JpcHRpb25fcGxhbjogJ2ZyZWUnLFxuICAgICAgICBtb250aGx5X3Rva2VuX2xpbWl0OiBnZXRUb2tlbkxpbWl0Rm9yUGxhbignZnJlZScpLFxuICAgICAgICBjdXJyZW50X21vbnRoX3Rva2VuczogMCxcbiAgICAgICAgY3VycmVudF9tb250aDogY3VycmVudE1vbnRoLFxuICAgICAgICBwYXltZW50X3ZlcmlmaWVkOiB0cnVlLCAvLyBMYXMgY3VlbnRhcyBncmF0dWl0YXMgc2UgY29uc2lkZXJhbiBcInZlcmlmaWNhZGFzXCJcbiAgICAgICAgc3RyaXBlX2N1c3RvbWVyX2lkOiBudWxsLFxuICAgICAgICBzdHJpcGVfc3Vic2NyaXB0aW9uX2lkOiBudWxsLFxuICAgICAgICBsYXN0X3BheW1lbnRfZGF0ZTogbnVsbCxcbiAgICAgICAgYXV0b19yZW5ldzogZmFsc2UsXG4gICAgICAgIHBsYW5fZXhwaXJlc19hdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHBsYW5fZmVhdHVyZXM6IHBsYW5Db25maWcuZmVhdHVyZXMsXG4gICAgICAgIHNlY3VyaXR5X2ZsYWdzOiB7XG4gICAgICAgICAgY3JlYXRlZF92aWFfZnJlZV9yZWdpc3RyYXRpb246IHRydWUsXG4gICAgICAgICAgZnJlZV9hY2NvdW50OiB0cnVlLFxuICAgICAgICAgIGV4cGlyZXNfYXQ6IGV4cGlyZXNBdC50b0lTT1N0cmluZygpLFxuICAgICAgICAgIGFjdGl2YXRpb25fZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHVzYWdlX2NvdW50OiB7XG4gICAgICAgICAgICBkb2N1bWVudHM6IDAsXG4gICAgICAgICAgICB0ZXN0czogMCxcbiAgICAgICAgICAgIGZsYXNoY2FyZHM6IDAsXG4gICAgICAgICAgICBtaW5kTWFwczogMCxcbiAgICAgICAgICAgIHRva2VuczogMFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfTtcblxuICAgICAgLy8gTGFzIGN1ZW50YXMgZ3JhdHVpdGFzIG5vIHRpZW5lbiB0cmFuc2FjY2nDs24gYXNvY2lhZGEsIHBhc2FyIG51bGxcbiAgICAgIGNvbnN0IHsgZGF0YTogY3JlYXRpb25SZXN1bHQsIGVycm9yOiBycGNFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAucnBjKCdjcmVhdGVfdXNlcl9wcm9maWxlX2FuZF9oaXN0b3J5Jywge1xuICAgICAgICAgIHBfdXNlcl9pZDogdXNlckludml0YXRpb24udXNlci5pZCxcbiAgICAgICAgICBwX3RyYW5zYWN0aW9uX2lkOiBudWxsLCAvLyBOVUxMIHBhcmEgY3VlbnRhcyBncmF0dWl0YXNcbiAgICAgICAgICBwX3Byb2ZpbGVfZGF0YTogcHJvZmlsZURhdGFcbiAgICAgICAgfSlcbiAgICAgICAgLnNpbmdsZSgpOyAvLyAuc2luZ2xlKCkgZXMgaW1wb3J0YW50ZSBwYXJhIG9idGVuZXIgdW4gw7puaWNvIHJlc3VsdGFkb1xuXG4gICAgICBpZiAocnBjRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZWplY3V0YXIgbGEgZnVuY2nDs24gY3JlYXRlX3VzZXJfcHJvZmlsZV9hbmRfaGlzdG9yeTonLCBycGNFcnJvcik7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRXJyb3IgZW4gbGEgY3JlYWNpw7NuIGF0w7NtaWNhIGRlbCBwZXJmaWw6ICR7cnBjRXJyb3IubWVzc2FnZX1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcHJvZmlsZUlkID0gKGNyZWF0aW9uUmVzdWx0IGFzIGFueSkuY3JlYXRlZF9wcm9maWxlX2lkO1xuICAgICAgY29uc29sZS5sb2coJ+KchSBQZXJmaWwgZ3JhdHVpdG8geSBoaXN0b3JpYWwgY3JlYWRvcyBhdMOzbWljYW1lbnRlLiBQcm9maWxlIElEOicsIHByb2ZpbGVJZCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn46JIEN1ZW50YSBncmF0dWl0YSBjcmVhZGEgZXhpdG9zYW1lbnRlJyk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIHVzZXJJZDogdXNlckludml0YXRpb24udXNlci5pZCxcbiAgICAgICAgcHJvZmlsZUlkOiBwcm9maWxlSWQsIC8vIFVzYW1vcyBlbCBJRCBkZXZ1ZWx0byBwb3IgbGEgZnVuY2nDs25cbiAgICAgICAgZXhwaXJlc0F0OiBleHBpcmVzQXQudG9JU09TdHJpbmcoKVxuICAgICAgfTtcbiAgICAgIFxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgY3JlYW5kbyBjdWVudGEgZ3JhdHVpdGE6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdFcnJvciBkZXNjb25vY2lkbydcbiAgICAgIH07XG4gICAgfVxuICB9XG4gIFxuICAvKipcbiAgICogVmVyaWZpY2FyIGVzdGFkbyBkZSBjdWVudGEgZ3JhdHVpdGFcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRGcmVlQWNjb3VudFN0YXR1cyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8RnJlZUFjY291bnRTdGF0dXMgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHByb2ZpbGUgPSBhd2FpdCBTdXBhYmFzZUFkbWluU2VydmljZS5nZXRVc2VyUHJvZmlsZSh1c2VySWQpO1xuICAgICAgXG4gICAgICBpZiAoIXByb2ZpbGUgfHwgcHJvZmlsZS5zdWJzY3JpcHRpb25fcGxhbiAhPT0gJ2ZyZWUnKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgICAgY29uc3QgZXhwaXJlc0F0ID0gcHJvZmlsZS5wbGFuX2V4cGlyZXNfYXQgPyBuZXcgRGF0ZShwcm9maWxlLnBsYW5fZXhwaXJlc19hdCkgOiBudWxsO1xuICAgICAgXG4gICAgICBpZiAoIWV4cGlyZXNBdCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgY29uc3QgaXNBY3RpdmUgPSBub3cgPCBleHBpcmVzQXQ7XG4gICAgICBjb25zdCB0aW1lRGlmZiA9IGV4cGlyZXNBdC5nZXRUaW1lKCkgLSBub3cuZ2V0VGltZSgpO1xuICAgICAgY29uc3QgZGF5c1JlbWFpbmluZyA9IE1hdGgubWF4KDAsIE1hdGguY2VpbCh0aW1lRGlmZiAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkpO1xuICAgICAgY29uc3QgaG91cnNSZW1haW5pbmcgPSBNYXRoLm1heCgwLCBNYXRoLmNlaWwodGltZURpZmYgLyAoMTAwMCAqIDYwICogNjApKSk7XG4gICAgICBcbiAgICAgIC8vIE9idGVuZXIgY29udGFkb3JlcyBkZSB1c28gZGVzZGUgc2VjdXJpdHlfZmxhZ3NcbiAgICAgIC8vIFBhcmEgdG9rZW5zLCB1c2FyIGN1cnJlbnRfbW9udGhfdG9rZW5zIGNvbW8gZnVlbnRlIMO6bmljYSBkZSB2ZXJkYWRcbiAgICAgIGNvbnN0IHVzYWdlQ291bnQgPSBwcm9maWxlLnNlY3VyaXR5X2ZsYWdzPy51c2FnZV9jb3VudCB8fCB7XG4gICAgICAgIGRvY3VtZW50czogMCxcbiAgICAgICAgdGVzdHM6IDAsXG4gICAgICAgIGZsYXNoY2FyZHM6IDAsXG4gICAgICAgIG1pbmRNYXBzOiAwLFxuICAgICAgICB0b2tlbnM6IDAgLy8gTm8gdXNhZG8sIHNlIG9idGllbmUgZGUgY3VycmVudF9tb250aF90b2tlbnNcbiAgICAgIH07XG5cbiAgICAgIC8vIFNvYnJlc2NyaWJpciB0b2tlbnMgY29uIGxhIGZ1ZW50ZSBkZSB2ZXJkYWRcbiAgICAgIHVzYWdlQ291bnQudG9rZW5zID0gcHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucyB8fCAwO1xuICAgICAgXG4gICAgICAvLyBMw61taXRlcyBkZWwgcGxhbiBncmF0dWl0byAodHJpYWwgZGUgNSBkw61hcylcbiAgICAgIGNvbnN0IHBsYW5Db25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbignZnJlZScpO1xuICAgICAgY29uc3QgbGltaXRzID0ge1xuICAgICAgICBkb2N1bWVudHM6IHBsYW5Db25maWc/LmxpbWl0cy5kb2N1bWVudHMgfHwgMSxcbiAgICAgICAgdGVzdHM6IHBsYW5Db25maWc/LmxpbWl0cy50ZXN0c0ZvclRyaWFsIHx8IDEwLCAgICAgICAgLy8gTMOtbWl0ZSB0b3RhbCBwYXJhIHRyaWFsXG4gICAgICAgIGZsYXNoY2FyZHM6IHBsYW5Db25maWc/LmxpbWl0cy5mbGFzaGNhcmRzRm9yVHJpYWwgfHwgMTAsICAvLyBMw61taXRlIHRvdGFsIHBhcmEgdHJpYWxcbiAgICAgICAgbWluZE1hcHM6IHBsYW5Db25maWc/LmxpbWl0cy5taW5kTWFwc0ZvclRyaWFsIHx8IDIsICAgICAgIC8vIEzDrW1pdGUgdG90YWwgcGFyYSB0cmlhbFxuICAgICAgICB0b2tlbnM6IHBsYW5Db25maWc/LmxpbWl0cy50b2tlbnNGb3JUcmlhbCB8fCA1MDAwMCAgICAgIC8vIEzDrW1pdGUgdG90YWwgcGFyYSB0cmlhbFxuICAgICAgfTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaXNBY3RpdmUsXG4gICAgICAgIGV4cGlyZXNBdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKCksXG4gICAgICAgIGRheXNSZW1haW5pbmcsXG4gICAgICAgIGhvdXJzUmVtYWluaW5nLFxuICAgICAgICB1c2FnZUNvdW50LFxuICAgICAgICBsaW1pdHNcbiAgICAgIH07XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igb2J0ZW5pZW5kbyBlc3RhZG8gZGUgY3VlbnRhIGdyYXR1aXRhOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfVxuICBcbiAgLyoqXG4gICAqIEluY3JlbWVudGFyIGNvbnRhZG9yIGRlIHVzb1xuICAgKiBOT1RBOiBQYXJhIHRva2VucywgZWwgaW5jcmVtZW50byBzZSBtYW5lamEgYXV0b23DoXRpY2FtZW50ZSBwb3IgdG9rZW5UcmFja2VyLnRzXG4gICAqIHF1ZSBhY3R1YWxpemEgY3VycmVudF9tb250aF90b2tlbnMuIEVzdGUgbcOpdG9kbyBzb2xvIG1hbmVqYSBmZWF0dXJlcyBuby10b2tlbi5cbiAgICovXG4gIHN0YXRpYyBhc3luYyBpbmNyZW1lbnRVc2FnZUNvdW50KFxuICAgIHVzZXJJZDogc3RyaW5nLFxuICAgIGZlYXR1cmU6ICdkb2N1bWVudHMnIHwgJ3Rlc3RzJyB8ICdmbGFzaGNhcmRzJyB8ICdtaW5kTWFwcycsXG4gICAgYW1vdW50OiBudW1iZXIgPSAxXG4gICk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwcm9maWxlID0gYXdhaXQgU3VwYWJhc2VBZG1pblNlcnZpY2UuZ2V0VXNlclByb2ZpbGUodXNlcklkKTtcbiAgICAgIFxuICAgICAgaWYgKCFwcm9maWxlIHx8IHByb2ZpbGUuc3Vic2NyaXB0aW9uX3BsYW4gIT09ICdmcmVlJykge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IGN1cnJlbnRVc2FnZSA9IHByb2ZpbGUuc2VjdXJpdHlfZmxhZ3M/LnVzYWdlX2NvdW50IHx8IHtcbiAgICAgICAgZG9jdW1lbnRzOiAwLFxuICAgICAgICB0ZXN0czogMCxcbiAgICAgICAgZmxhc2hjYXJkczogMCxcbiAgICAgICAgbWluZE1hcHM6IDAsXG4gICAgICAgIHRva2VuczogMCAvLyBNYW50ZW5lbW9zIHBhcmEgY29tcGF0aWJpbGlkYWQsIHBlcm8gbm8gc2UgYWN0dWFsaXphIGFxdcOtXG4gICAgICB9O1xuXG4gICAgICBjdXJyZW50VXNhZ2VbZmVhdHVyZV0gPSAoY3VycmVudFVzYWdlW2ZlYXR1cmVdIHx8IDApICsgYW1vdW50O1xuXG4gICAgICAvLyBBY3R1YWxpemFyIHNvbG8gc2VjdXJpdHlfZmxhZ3MgKG5vIGN1cnJlbnRfbW9udGhfdG9rZW5zKVxuICAgICAgLy8gTG9zIHRva2VucyBzZSBtYW5lamFuIGV4Y2x1c2l2YW1lbnRlIHBvciB0b2tlblRyYWNrZXIudHNcbiAgICAgIGNvbnN0IHVwZGF0ZURhdGE6IFBhcnRpYWw8RXh0ZW5kZWRVc2VyUHJvZmlsZT4gPSB7XG4gICAgICAgIHNlY3VyaXR5X2ZsYWdzOiB7XG4gICAgICAgICAgLi4ucHJvZmlsZS5zZWN1cml0eV9mbGFncyxcbiAgICAgICAgICB1c2FnZV9jb3VudDogY3VycmVudFVzYWdlXG4gICAgICAgIH0sXG4gICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfTtcbiAgICAgIFxuICAgICAgYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC51cGRhdGUodXBkYXRlRGF0YSlcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHRydWU7XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5jcmVtZW50YW5kbyBjb250YWRvciBkZSB1c286JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICBcbiAgLyoqXG4gICAqIFZlcmlmaWNhciBzaSBzZSBwdWVkZSByZWFsaXphciB1bmEgYWNjacOzblxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNhblBlcmZvcm1BY3Rpb24oXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgZmVhdHVyZTogJ2RvY3VtZW50cycgfCAndGVzdHMnIHwgJ2ZsYXNoY2FyZHMnIHwgJ21pbmRNYXBzJyB8ICd0b2tlbnMnLFxuICAgIGFtb3VudDogbnVtYmVyID0gMVxuICApOiBQcm9taXNlPHsgYWxsb3dlZDogYm9vbGVhbjsgcmVhc29uPzogc3RyaW5nOyByZW1haW5pbmc/OiBudW1iZXIgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdGF0dXMgPSBhd2FpdCB0aGlzLmdldEZyZWVBY2NvdW50U3RhdHVzKHVzZXJJZCk7XG4gICAgICBcbiAgICAgIGlmICghc3RhdHVzKSB7XG4gICAgICAgIHJldHVybiB7IGFsbG93ZWQ6IGZhbHNlLCByZWFzb246ICdDdWVudGEgbm8gZW5jb250cmFkYSBvIG5vIGVzIGdyYXR1aXRhJyB9O1xuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoIXN0YXR1cy5pc0FjdGl2ZSkge1xuICAgICAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiAnQ3VlbnRhIGdyYXR1aXRhIGV4cGlyYWRhJyB9O1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCBjdXJyZW50VXNhZ2UgPSBzdGF0dXMudXNhZ2VDb3VudFtmZWF0dXJlXSB8fCAwO1xuICAgICAgY29uc3QgbGltaXQgPSBzdGF0dXMubGltaXRzW2ZlYXR1cmVdO1xuICAgICAgY29uc3QgcmVtYWluaW5nID0gbGltaXQgLSBjdXJyZW50VXNhZ2U7XG4gICAgICBcbiAgICAgIGlmIChjdXJyZW50VXNhZ2UgKyBhbW91bnQgPiBsaW1pdCkge1xuICAgICAgICByZXR1cm4geyBcbiAgICAgICAgICBhbGxvd2VkOiBmYWxzZSwgXG4gICAgICAgICAgcmVhc29uOiBgTMOtbWl0ZSBkZSAke2ZlYXR1cmV9IGFsY2FuemFkbyAoJHtsaW1pdH0pYCxcbiAgICAgICAgICByZW1haW5pbmc6IE1hdGgubWF4KDAsIHJlbWFpbmluZylcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuIHsgXG4gICAgICAgIGFsbG93ZWQ6IHRydWUsIFxuICAgICAgICByZW1haW5pbmc6IHJlbWFpbmluZyAtIGFtb3VudCBcbiAgICAgIH07XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdmVyaWZpY2FuZG8gYWNjacOzbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiAnRXJyb3IgaW50ZXJubycgfTtcbiAgICB9XG4gIH1cbiAgXG4gIC8qKlxuICAgKiBMaW1waWFyIGN1ZW50YXMgZ3JhdHVpdGFzIGV4cGlyYWRhc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNsZWFudXBFeHBpcmVkQWNjb3VudHMoKTogUHJvbWlzZTx7XG4gICAgY2xlYW5lZDogbnVtYmVyO1xuICAgIGVycm9yczogc3RyaW5nW107XG4gIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfp7kgSW5pY2lhbmRvIGxpbXBpZXphIGRlIGN1ZW50YXMgZ3JhdHVpdGFzIGV4cGlyYWRhcycpO1xuICAgICAgXG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG4gICAgICBcbiAgICAgIC8vIEJ1c2NhciBjdWVudGFzIGdyYXR1aXRhcyBleHBpcmFkYXNcbiAgICAgIGNvbnN0IHsgZGF0YTogZXhwaXJlZFByb2ZpbGVzLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC5zZWxlY3QoJ3VzZXJfaWQsIGlkJylcbiAgICAgICAgLmVxKCdzdWJzY3JpcHRpb25fcGxhbicsICdmcmVlJylcbiAgICAgICAgLmx0KCdwbGFuX2V4cGlyZXNfYXQnLCBub3cpO1xuICAgICAgXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvciBidXNjYW5kbyBjdWVudGFzIGV4cGlyYWRhczogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoIWV4cGlyZWRQcm9maWxlcyB8fCBleHBpcmVkUHJvZmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgTm8gaGF5IGN1ZW50YXMgZXhwaXJhZGFzIHBhcmEgbGltcGlhcicpO1xuICAgICAgICByZXR1cm4geyBjbGVhbmVkOiAwLCBlcnJvcnM6IFtdIH07XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKGDwn5eR77iPIEVuY29udHJhZGFzICR7ZXhwaXJlZFByb2ZpbGVzLmxlbmd0aH0gY3VlbnRhcyBleHBpcmFkYXNgKTtcbiAgICAgIFxuICAgICAgY29uc3QgZXJyb3JzOiBzdHJpbmdbXSA9IFtdO1xuICAgICAgbGV0IGNsZWFuZWQgPSAwO1xuICAgICAgXG4gICAgICBmb3IgKGNvbnN0IHByb2ZpbGUgb2YgZXhwaXJlZFByb2ZpbGVzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gRGVzYWN0aXZhciB1c3VhcmlvIGVuIGF1dGhcbiAgICAgICAgICBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4udXBkYXRlVXNlckJ5SWQocHJvZmlsZS51c2VyX2lkLCB7XG4gICAgICAgICAgICB1c2VyX21ldGFkYXRhOiB7IGFjY291bnRfZGlzYWJsZWQ6IHRydWUsIGRpc2FibGVkX3JlYXNvbjogJ2ZyZWVfYWNjb3VudF9leHBpcmVkJyB9XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gTWFyY2FyIHBlcmZpbCBjb21vIGluYWN0aXZvXG4gICAgICAgICAgYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAgICAgLnVwZGF0ZSh7XG4gICAgICAgICAgICAgIHBheW1lbnRfdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICBzZWN1cml0eV9mbGFnczoge1xuICAgICAgICAgICAgICAgIGFjY291bnRfZGlzYWJsZWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgZGlzYWJsZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgICBkaXNhYmxlZF9yZWFzb246ICdmcmVlX2FjY291bnRfZXhwaXJlZCdcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5lcSgndXNlcl9pZCcsIHByb2ZpbGUudXNlcl9pZCk7XG4gICAgICAgICAgXG4gICAgICAgICAgY2xlYW5lZCsrO1xuICAgICAgICAgIFxuICAgICAgICB9IGNhdGNoIChjbGVhbnVwRXJyb3IpIHtcbiAgICAgICAgICBjb25zdCBlcnJvck1zZyA9IGBFcnJvciBsaW1waWFuZG8gdXN1YXJpbyAke3Byb2ZpbGUudXNlcl9pZH06ICR7Y2xlYW51cEVycm9yfWA7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvck1zZyk7XG4gICAgICAgICAgZXJyb3JzLnB1c2goZXJyb3JNc2cpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKGDinIUgTGltcGllemEgY29tcGxldGFkYTogJHtjbGVhbmVkfSBjdWVudGFzIHByb2Nlc2FkYXMsICR7ZXJyb3JzLmxlbmd0aH0gZXJyb3Jlc2ApO1xuICAgICAgXG4gICAgICByZXR1cm4geyBjbGVhbmVkLCBlcnJvcnMgfTtcbiAgICAgIFxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgZW4gbGltcGllemEgZGUgY3VlbnRhczonLCBlcnJvcik7XG4gICAgICByZXR1cm4geyBcbiAgICAgICAgY2xlYW5lZDogMCwgXG4gICAgICAgIGVycm9yczogW2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0Vycm9yIGRlc2Nvbm9jaWRvJ10gXG4gICAgICB9O1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbIlN1cGFiYXNlQWRtaW5TZXJ2aWNlIiwiZ2V0UGxhbkNvbmZpZ3VyYXRpb24iLCJnZXRUb2tlbkxpbWl0Rm9yUGxhbiIsInN1cGFiYXNlQWRtaW4iLCJGcmVlQWNjb3VudFNlcnZpY2UiLCJjcmVhdGVGcmVlQWNjb3VudCIsInJlcXVlc3QiLCJjb25zb2xlIiwibG9nIiwiZW1haWwiLCJleGlzdGluZ1VzZXIiLCJnZXRVc2VyQnlFbWFpbCIsInN1Y2Nlc3MiLCJlcnJvciIsImV4cGlyZXNBdCIsIkRhdGUiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsInVzZXJEYXRhIiwibmFtZSIsInBsYW4iLCJmcmVlX2FjY291bnQiLCJleHBpcmVzX2F0IiwidG9JU09TdHJpbmciLCJjcmVhdGVkX3ZpYSIsImxlZ2l0aW1hdGVfdXNlciIsInJlZ2lzdHJhdGlvbl90eXBlIiwicmVnaXN0cmF0aW9uX3RpbWVzdGFtcCIsInRpbWVzdGFtcCIsInVzZXJJbnZpdGF0aW9uIiwiY3JlYXRlVXNlcldpdGhJbnZpdGF0aW9uIiwiaGFzVXNlciIsInVzZXIiLCJ1c2VySWQiLCJpZCIsInVzZXJFbWFpbCIsInVzZXJBdWQiLCJhdWQiLCJ1c2VyUm9sZSIsInJvbGUiLCJlbWFpbENvbmZpcm1lZCIsImVtYWlsX2NvbmZpcm1lZF9hdCIsImNyZWF0ZWRBdCIsImNyZWF0ZWRfYXQiLCJ1c2VyTWV0YWRhdGEiLCJ1c2VyX21ldGFkYXRhIiwiYXBwTWV0YWRhdGEiLCJhcHBfbWV0YWRhdGEiLCJmdWxsUmVzcG9uc2UiLCJFcnJvciIsIm5lZWRzRW1haWxDb25maXJtYXRpb24iLCJwbGFuQ29uZmlnIiwiY3VycmVudE1vbnRoIiwic2xpY2UiLCJwcm9maWxlRGF0YSIsInN1YnNjcmlwdGlvbl9wbGFuIiwibW9udGhseV90b2tlbl9saW1pdCIsImN1cnJlbnRfbW9udGhfdG9rZW5zIiwiY3VycmVudF9tb250aCIsInBheW1lbnRfdmVyaWZpZWQiLCJzdHJpcGVfY3VzdG9tZXJfaWQiLCJzdHJpcGVfc3Vic2NyaXB0aW9uX2lkIiwibGFzdF9wYXltZW50X2RhdGUiLCJhdXRvX3JlbmV3IiwicGxhbl9leHBpcmVzX2F0IiwicGxhbl9mZWF0dXJlcyIsImZlYXR1cmVzIiwic2VjdXJpdHlfZmxhZ3MiLCJjcmVhdGVkX3ZpYV9mcmVlX3JlZ2lzdHJhdGlvbiIsImFjdGl2YXRpb25fZGF0ZSIsInVzYWdlX2NvdW50IiwiZG9jdW1lbnRzIiwidGVzdHMiLCJmbGFzaGNhcmRzIiwibWluZE1hcHMiLCJ0b2tlbnMiLCJkYXRhIiwiY3JlYXRpb25SZXN1bHQiLCJycGNFcnJvciIsInJwYyIsInBfdXNlcl9pZCIsInBfdHJhbnNhY3Rpb25faWQiLCJwX3Byb2ZpbGVfZGF0YSIsInNpbmdsZSIsIm1lc3NhZ2UiLCJwcm9maWxlSWQiLCJjcmVhdGVkX3Byb2ZpbGVfaWQiLCJnZXRGcmVlQWNjb3VudFN0YXR1cyIsInByb2ZpbGUiLCJnZXRVc2VyUHJvZmlsZSIsIm5vdyIsImlzQWN0aXZlIiwidGltZURpZmYiLCJnZXRUaW1lIiwiZGF5c1JlbWFpbmluZyIsIk1hdGgiLCJtYXgiLCJjZWlsIiwiaG91cnNSZW1haW5pbmciLCJ1c2FnZUNvdW50IiwibGltaXRzIiwidGVzdHNGb3JUcmlhbCIsImZsYXNoY2FyZHNGb3JUcmlhbCIsIm1pbmRNYXBzRm9yVHJpYWwiLCJ0b2tlbnNGb3JUcmlhbCIsImluY3JlbWVudFVzYWdlQ291bnQiLCJmZWF0dXJlIiwiYW1vdW50IiwiY3VycmVudFVzYWdlIiwidXBkYXRlRGF0YSIsIl9vYmplY3RTcHJlYWQiLCJ1cGRhdGVkX2F0IiwiZnJvbSIsInVwZGF0ZSIsImVxIiwiY2FuUGVyZm9ybUFjdGlvbiIsInN0YXR1cyIsImFsbG93ZWQiLCJyZWFzb24iLCJsaW1pdCIsInJlbWFpbmluZyIsImNsZWFudXBFeHBpcmVkQWNjb3VudHMiLCJleHBpcmVkUHJvZmlsZXMiLCJzZWxlY3QiLCJsdCIsImxlbmd0aCIsImNsZWFuZWQiLCJlcnJvcnMiLCJhdXRoIiwiYWRtaW4iLCJ1cGRhdGVVc2VyQnlJZCIsInVzZXJfaWQiLCJhY2NvdW50X2Rpc2FibGVkIiwiZGlzYWJsZWRfcmVhc29uIiwiZGlzYWJsZWRfYXQiLCJjbGVhbnVwRXJyb3IiLCJlcnJvck1zZyIsInB1c2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/freeAccountService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers({\n                email\n            });\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            return {\n                id: users[0].id,\n                email: users[0].email,\n                email_confirmed_at: users[0].email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();