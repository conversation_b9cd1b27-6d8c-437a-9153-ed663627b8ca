/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register-free/route";
exports.ids = ["app/api/auth/register-free/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v13_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register-free/route.ts */ \"(rsc)/./src/app/api/auth/register-free/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register-free/route\",\n        pathname: \"/api/auth/register-free\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register-free/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v13\\\\src\\\\app\\\\api\\\\auth\\\\register-free\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v13_src_app_api_auth_register_free_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/register-free/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/register-free/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/freeAccountService */ \"(rsc)/./src/lib/services/freeAccountService.ts\");\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n// src/app/api/auth/register-free/route.ts\n// Endpoint para registro automático de cuentas gratuitas\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_2__.Resend(process.env.RESEND_API_KEY);\n// Rate limiting simple en memoria (en producción usar Redis)\nconst registrationAttempts = new Map();\nconst RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutos\nconst MAX_ATTEMPTS = 3; // Máximo 3 intentos por IP en 15 minutos\nasync function POST(request) {\n    try {\n        const startTime = Date.now();\n        console.log('🆓 Iniciando registro de cuenta gratuita');\n        // 1. Rate limiting básico\n        const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';\n        const now = Date.now();\n        const attempts = registrationAttempts.get(clientIP);\n        if (attempts) {\n            // Limpiar intentos antiguos\n            if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {\n                registrationAttempts.delete(clientIP);\n            } else if (attempts.count >= MAX_ATTEMPTS) {\n                console.log(`❌ Rate limit excedido para IP: ${clientIP}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Demasiados intentos de registro. Inténtalo más tarde.',\n                    retryAfter: Math.ceil((RATE_LIMIT_WINDOW - (now - attempts.lastAttempt)) / 1000)\n                }, {\n                    status: 429\n                });\n            }\n        }\n        // 2. Validar datos de entrada\n        const body = await request.json();\n        const { email, customerName } = body;\n        if (!email || typeof email !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email es requerido y debe ser válido'\n            }, {\n                status: 400\n            });\n        }\n        // Validación básica de email\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Formato de email inválido'\n            }, {\n                status: 400\n            });\n        }\n        // Normalizar email\n        const normalizedEmail = email.toLowerCase().trim();\n        console.log('📧 Procesando registro para:', normalizedEmail);\n        // 3. Actualizar contador de intentos\n        const currentAttempts = registrationAttempts.get(clientIP) || {\n            count: 0,\n            lastAttempt: 0\n        };\n        registrationAttempts.set(clientIP, {\n            count: currentAttempts.count + 1,\n            lastAttempt: now\n        });\n        // 4. Crear cuenta gratuita\n        const result = await _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_1__.FreeAccountService.createFreeAccount({\n            email: normalizedEmail,\n            name: customerName || undefined\n        });\n        if (!result.success) {\n            console.log('❌ Error creando cuenta gratuita:', result.error);\n            // Si el email ya existe, no es un error de rate limiting\n            if (result.error?.includes('Ya existe una cuenta')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: result.error,\n                    code: 'EMAIL_EXISTS'\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error || 'Error interno al crear la cuenta'\n            }, {\n                status: 500\n            });\n        }\n        console.log('✅ Cuenta gratuita creada exitosamente:', result.userId);\n        // 5. Enviar email de bienvenida\n        try {\n            if (process.env.RESEND_API_KEY) {\n                const welcomeEmailData = {\n                    from: 'OposiAI <<EMAIL>>',\n                    // Dominio temporal de Resend\n                    to: [\n                        normalizedEmail\n                    ],\n                    subject: '🎉 ¡Bienvenido a OposiAI! Tu cuenta gratuita está lista',\n                    html: generateWelcomeEmail(customerName || 'Usuario', result.expiresAt)\n                };\n                const emailResult = await resend.emails.send(welcomeEmailData);\n                console.log('📧 Email de bienvenida enviado exitosamente:', emailResult.data?.id);\n            } else {\n                console.warn('⚠️ RESEND_API_KEY no configurada, saltando envío de email');\n            }\n        } catch (emailError) {\n            console.error('⚠️ Error enviando email de bienvenida:', emailError);\n            // Mostrar detalles del error para debugging\n            if (emailError instanceof Error) {\n                console.error('   - Mensaje:', emailError.message);\n                console.error('   - Stack:', emailError.stack);\n            }\n        // No fallar el registro por error de email\n        }\n        // 6. Enviar notificación al administrador\n        try {\n            if (process.env.NOTIFICATION_EMAIL && process.env.RESEND_API_KEY) {\n                const adminNotificationData = {\n                    from: 'OposiAI Notificaciones <<EMAIL>>',\n                    // Dominio temporal de Resend\n                    to: [\n                        process.env.NOTIFICATION_EMAIL\n                    ],\n                    subject: '🆓 Nueva Cuenta Gratuita Creada - OposiAI',\n                    html: generateAdminNotification(normalizedEmail, customerName, result.expiresAt)\n                };\n                const notificationResult = await resend.emails.send(adminNotificationData);\n                console.log('📧 Notificación de administrador enviada:', notificationResult.data?.id);\n            } else {\n                console.warn('⚠️ NOTIFICATION_EMAIL o RESEND_API_KEY no configurados, saltando notificación');\n            }\n        } catch (notificationError) {\n            console.error('⚠️ Error enviando notificación de administrador:', notificationError);\n            if (notificationError instanceof Error) {\n                console.error('   - Mensaje:', notificationError.message);\n            }\n        // No fallar el registro por error de notificación\n        }\n        // 7. Limpiar rate limiting en caso de éxito\n        registrationAttempts.delete(clientIP);\n        const processingTime = Date.now() - startTime;\n        console.log(`🎉 Registro gratuito completado en ${processingTime}ms`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Cuenta gratuita creada exitosamente',\n            data: {\n                userId: result.userId,\n                expiresAt: result.expiresAt,\n                sessionId: `free_${result.userId}_${Date.now()}` // Simular session ID para compatibilidad\n            }\n        });\n    } catch (error) {\n        console.error('❌ Error crítico en registro gratuito:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor',\n            details:  true ? error instanceof Error ? error.message : 'Error desconocido' : 0\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Generar HTML para email de bienvenida\n */ function generateWelcomeEmail(name, expiresAt) {\n    const expirationDate = new Date(expiresAt).toLocaleDateString('es-ES', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n    return `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n      <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"color: #2563eb; margin-bottom: 10px;\">¡Bienvenido a OposiAI!</h1>\n        <p style=\"color: #6b7280; font-size: 18px;\">Tu asistente inteligente para oposiciones</p>\n      </div>\n      \n      <div style=\"background-color: #f8fafc; padding: 25px; border-radius: 8px; margin-bottom: 25px;\">\n        <h2 style=\"color: #1e40af; margin-top: 0;\">¡Hola ${name}!</h2>\n        <p style=\"color: #374151; line-height: 1.6;\">\n          Tu cuenta gratuita de OposiAI ha sido creada exitosamente. Ya puedes empezar a usar \n          todas las funcionalidades disponibles en tu plan.\n        </p>\n      </div>\n      \n      <div style=\"background-color: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 25px;\">\n        <h3 style=\"color: #92400e; margin-top: 0;\">📅 Tu cuenta gratuita expira el:</h3>\n        <p style=\"color: #92400e; font-size: 18px; font-weight: bold; margin-bottom: 10px;\">\n          ${expirationDate}\n        </p>\n        <p style=\"color: #92400e; font-size: 14px; margin-bottom: 0;\">\n          Tienes 5 días completos para explorar todas las funcionalidades.\n        </p>\n      </div>\n      \n      <div style=\"margin-bottom: 25px;\">\n        <h3 style=\"color: #1f2937;\">🎯 ¿Qué puedes hacer con tu cuenta gratuita?</h3>\n        <ul style=\"color: #374151; line-height: 1.8;\">\n          <li>📄 Subir hasta 1 documento de estudio</li>\n          <li>❓ Generar hasta 10 preguntas de test</li>\n          <li>🃏 Crear hasta 10 flashcards</li>\n          <li>🗺️ Generar hasta 2 mapas mentales</li>\n          <li>🤖 Usar hasta 50,000 tokens de IA</li>\n        </ul>\n      </div>\n      \n      <div style=\"text-align: center; margin-bottom: 25px;\">\n        <a href=\"${\"http://localhost:3000\"}/app\" \n           style=\"display: inline-block; background-color: #2563eb; color: white; padding: 15px 30px; \n                  text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px;\">\n          🚀 Comenzar a usar OposiAI\n        </a>\n      </div>\n      \n      <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n        <h3 style=\"color: #1f2937; margin-top: 0;\">💡 Consejos para aprovechar al máximo tu cuenta:</h3>\n        <ol style=\"color: #374151; line-height: 1.6;\">\n          <li>Sube tu documento más importante primero</li>\n          <li>Genera tests para evaluar tu conocimiento</li>\n          <li>Usa flashcards para memorizar conceptos clave</li>\n          <li>Crea mapas mentales para visualizar la información</li>\n        </ol>\n      </div>\n      \n      <div style=\"text-align: center; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n        <p style=\"color: #6b7280; font-size: 14px; margin-bottom: 10px;\">\n          ¿Quieres más funcionalidades? Descubre nuestros planes premium\n        </p>\n        <a href=\"${\"http://localhost:3000\"}/payment\" \n           style=\"color: #2563eb; text-decoration: none; font-weight: bold;\">\n          Ver Planes Premium →\n        </a>\n      </div>\n      \n      <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center;\">\n        <p style=\"color: #6b7280; font-size: 12px;\">\n          Este email fue generado automáticamente por OposiAI.<br>\n          Si tienes alguna pregunta, contacta con nuestro soporte.\n        </p>\n      </div>\n    </div>\n  `;\n}\n/**\n * Generar HTML para notificación de administrador\n */ function generateAdminNotification(email, name, expiresAt) {\n    return `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n      <h2 style=\"color: #2563eb;\">🆓 Nueva Cuenta Gratuita Creada</h2>\n      \n      <div style=\"background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0; color: #1e40af;\">Detalles del Usuario</h3>\n        <p><strong>Email:</strong> ${email}</p>\n        <p><strong>Nombre:</strong> ${name || 'No proporcionado'}</p>\n        <p><strong>Plan:</strong> Gratuito (5 días)</p>\n        <p><strong>Expira:</strong> ${new Date(expiresAt).toLocaleString('es-ES')}</p>\n        <p><strong>Fecha de registro:</strong> ${new Date().toLocaleString('es-ES')}</p>\n      </div>\n\n      <div style=\"background-color: #dcfce7; padding: 15px; border-radius: 8px; border-left: 4px solid #16a34a;\">\n        <h4 style=\"margin-top: 0; color: #166534;\">✅ Cuenta Creada Automáticamente</h4>\n        <p style=\"margin-bottom: 0; color: #166534;\">\n          La cuenta fue creada automáticamente por el sistema. No se requiere acción manual.\n        </p>\n      </div>\n\n      <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n        <p style=\"color: #6b7280; font-size: 14px;\">\n          Este email fue generado automáticamente por el sistema de registro gratuito de OposiAI.\n        </p>\n      </div>\n    </div>\n  `;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register-free/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/freeAccountService.ts":
/*!************************************************!*\
  !*** ./src/lib/services/freeAccountService.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FreeAccountService: () => (/* binding */ FreeAccountService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/services/freeAccountService.ts\n// Servicio para gestión automatizada de cuentas gratuitas\n\n\n\nclass FreeAccountService {\n    /**\n   * Crear cuenta gratuita automáticamente\n   */ static async createFreeAccount(request) {\n        try {\n            console.log('🆓 Iniciando creación de cuenta gratuita:', request.email);\n            // 1. Validar que el email no esté ya registrado\n            // Usar getUserByEmail que es más eficiente para verificar existencia\n            try {\n                const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                if (existingUser) {\n                    return {\n                        success: false,\n                        error: 'Ya existe una cuenta con este email'\n                    };\n                }\n            } catch (error) {\n                // Si no se encuentra el usuario, continuamos (esto es lo que queremos)\n                console.log('Usuario no existe, continuando con la creación');\n            }\n            // 2. Calcular fecha de expiración (5 días desde ahora)\n            const expiresAt = new Date();\n            expiresAt.setDate(expiresAt.getDate() + 5);\n            // 3. Crear usuario con invitación\n            const userData = {\n                name: request.name,\n                plan: 'free',\n                free_account: true,\n                expires_at: expiresAt.toISOString(),\n                created_via: 'free_registration',\n                // Flags adicionales para identificación en middleware\n                legitimate_user: true,\n                registration_type: 'automatic_free',\n                registration_timestamp: new Date().toISOString()\n            };\n            const userInvitation = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createUserWithInvitation(request.email, userData);\n            if (!userInvitation.user) {\n                throw new Error('Error creando usuario');\n            }\n            console.log('✅ Usuario gratuito creado:', userInvitation.user.id);\n            // 4. Crear perfil de usuario y registrar historial de forma atómica\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            if (!planConfig) {\n                throw new Error('Configuración de plan gratuito no encontrada');\n            }\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const profileData = {\n                // Preparamos el objeto JSON para la función\n                subscription_plan: 'free',\n                monthly_token_limit: (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)('free'),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                // Las cuentas gratuitas se consideran \"verificadas\"\n                stripe_customer_id: null,\n                stripe_subscription_id: null,\n                last_payment_date: null,\n                auto_renew: false,\n                plan_expires_at: expiresAt.toISOString(),\n                plan_features: planConfig.features,\n                security_flags: {\n                    created_via_free_registration: true,\n                    free_account: true,\n                    expires_at: expiresAt.toISOString(),\n                    activation_date: new Date().toISOString(),\n                    usage_count: {\n                        documents: 0,\n                        tests: 0,\n                        flashcards: 0,\n                        mindMaps: 0,\n                        tokens: 0\n                    }\n                }\n            };\n            // Las cuentas gratuitas no tienen transacción asociada, pasar null\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: userInvitation.user.id,\n                p_transaction_id: null,\n                // NULL para cuentas gratuitas\n                p_profile_data: profileData\n            }).single(); // .single() es importante para obtener un único resultado\n            if (rpcError) {\n                console.error('Error al ejecutar la función create_user_profile_and_history:', rpcError);\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);\n            console.log('🎉 Cuenta gratuita creada exitosamente');\n            return {\n                success: true,\n                userId: userInvitation.user.id,\n                profileId: profileId,\n                // Usamos el ID devuelto por la función\n                expiresAt: expiresAt.toISOString()\n            };\n        } catch (error) {\n            console.error('❌ Error creando cuenta gratuita:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Verificar estado de cuenta gratuita\n   */ static async getFreeAccountStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return null;\n            }\n            const now = new Date();\n            const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;\n            if (!expiresAt) {\n                return null;\n            }\n            const isActive = now < expiresAt;\n            const timeDiff = expiresAt.getTime() - now.getTime();\n            const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n            const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));\n            // Obtener contadores de uso desde security_flags\n            // Para tokens, usar current_month_tokens como fuente única de verdad\n            const usageCount = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0 // No usado, se obtiene de current_month_tokens\n            };\n            // Sobrescribir tokens con la fuente de verdad\n            usageCount.tokens = profile.current_month_tokens || 0;\n            // Límites del plan gratuito (trial de 5 días)\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            const limits = {\n                documents: planConfig?.limits.documents || 1,\n                tests: planConfig?.limits.testsForTrial || 10,\n                // Límite total para trial\n                flashcards: planConfig?.limits.flashcardsForTrial || 10,\n                // Límite total para trial\n                mindMaps: planConfig?.limits.mindMapsForTrial || 2,\n                // Límite total para trial\n                tokens: planConfig?.limits.tokensForTrial || 50000 // Límite total para trial\n            };\n            return {\n                isActive,\n                expiresAt: expiresAt.toISOString(),\n                daysRemaining,\n                hoursRemaining,\n                usageCount,\n                limits\n            };\n        } catch (error) {\n            console.error('Error obteniendo estado de cuenta gratuita:', error);\n            return null;\n        }\n    }\n    /**\n   * Incrementar contador de uso\n   * NOTA: Para tokens, el incremento se maneja automáticamente por tokenTracker.ts\n   * que actualiza current_month_tokens. Este método solo maneja features no-token.\n   */ static async incrementUsageCount(userId, feature, amount = 1) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return false;\n            }\n            const currentUsage = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0 // Mantenemos para compatibilidad, pero no se actualiza aquí\n            };\n            currentUsage[feature] = (currentUsage[feature] || 0) + amount;\n            // Actualizar solo security_flags (no current_month_tokens)\n            // Los tokens se manejan exclusivamente por tokenTracker.ts\n            const updateData = {\n                security_flags: _objectSpread(_objectSpread({}, profile.security_flags), {}, {\n                    usage_count: currentUsage\n                }),\n                updated_at: new Date().toISOString()\n            };\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update(updateData).eq('user_id', userId);\n            return true;\n        } catch (error) {\n            console.error('Error incrementando contador de uso:', error);\n            return false;\n        }\n    }\n    /**\n   * Verificar si se puede realizar una acción\n   */ static async canPerformAction(userId, feature, amount = 1) {\n        try {\n            const status = await this.getFreeAccountStatus(userId);\n            if (!status) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta no encontrada o no es gratuita'\n                };\n            }\n            if (!status.isActive) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta gratuita expirada'\n                };\n            }\n            const currentUsage = status.usageCount[feature] || 0;\n            const limit = status.limits[feature];\n            const remaining = limit - currentUsage;\n            if (currentUsage + amount > limit) {\n                return {\n                    allowed: false,\n                    reason: `Límite de ${feature} alcanzado (${limit})`,\n                    remaining: Math.max(0, remaining)\n                };\n            }\n            return {\n                allowed: true,\n                remaining: remaining - amount\n            };\n        } catch (error) {\n            console.error('Error verificando acción:', error);\n            return {\n                allowed: false,\n                reason: 'Error interno'\n            };\n        }\n    }\n    /**\n   * Limpiar cuentas gratuitas expiradas\n   */ static async cleanupExpiredAccounts() {\n        try {\n            console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');\n            const now = new Date().toISOString();\n            // Buscar cuentas gratuitas expiradas\n            const { data: expiredProfiles, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, id').eq('subscription_plan', 'free').lt('plan_expires_at', now);\n            if (error) {\n                throw new Error(`Error buscando cuentas expiradas: ${error.message}`);\n            }\n            if (!expiredProfiles || expiredProfiles.length === 0) {\n                console.log('✅ No hay cuentas expiradas para limpiar');\n                return {\n                    cleaned: 0,\n                    errors: []\n                };\n            }\n            console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas`);\n            const errors = [];\n            let cleaned = 0;\n            for (const profile of expiredProfiles){\n                try {\n                    // Desactivar usuario en auth\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.updateUserById(profile.user_id, {\n                        user_metadata: {\n                            account_disabled: true,\n                            disabled_reason: 'free_account_expired'\n                        }\n                    });\n                    // Marcar perfil como inactivo\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: false,\n                        security_flags: {\n                            account_disabled: true,\n                            disabled_at: new Date().toISOString(),\n                            disabled_reason: 'free_account_expired'\n                        }\n                    }).eq('user_id', profile.user_id);\n                    cleaned++;\n                } catch (cleanupError) {\n                    const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);\n            return {\n                cleaned,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en limpieza de cuentas:', error);\n            return {\n                cleaned: 0,\n                errors: [\n                    error instanceof Error ? error.message : 'Error desconocido'\n                ]\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/freeAccountService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        if (error) {\n            console.error('Error creating user invitation:', error);\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers({\n                email\n            });\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            return {\n                id: users[0].id,\n                email: users[0].email,\n                email_confirmed_at: users[0].email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister-free%2Froute&page=%2Fapi%2Fauth%2Fregister-free%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister-free%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv13&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();