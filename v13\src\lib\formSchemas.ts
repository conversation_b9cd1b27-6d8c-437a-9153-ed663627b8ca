import { z } from 'zod';

export const preguntaFormSchema = z.object({
  pregunta: z.string().min(1, 'La pregunta es obligatoria').max(500, 'Máximo 500 caracteres'),
  documentos: z.array(
    z.object({
      id: z.string().optional(),
      titulo: z.string().min(1),
      contenido: z.string().min(1),
      categoria: z.string().optional().nullable(),
      numero_tema: z.union([z.number().int().positive(), z.string(), z.null(), z.undefined()]).optional(),
      creado_en: z.string().optional(),
      actualizado_en: z.string().optional(),
      user_id: z.string().optional(),
      tipo_original: z.string().optional(),
    })
  ).min(1, 'Debes seleccionar al menos un documento'),
});

// Esquema simplificado para los generadores que solo necesitan la petición
export const generatorFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
});

// Esquema específico para tests que incluye la cantidad de preguntas
export const testFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
  cantidad: z.number().min(1, 'Mínimo 1 pregunta').max(50, 'Máximo 50 preguntas').default(10),
});
// Esquema específico para flashcards que incluye la cantidad de tarjetas
export const flashcardFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
  cantidad: z.number().min(1, 'Mínimo 1 flashcard').max(30, 'Máximo 30 flashcards').default(10),
});
export const mindMapFormSchema = generatorFormSchema;
