"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/resend";
exports.ids = ["vendor-chunks/resend"];
exports.modules = {

/***/ "(rsc)/./node_modules/resend/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/resend/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Resend: () => (/* binding */ Resend)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = value => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = value => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = x => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// package.json\nvar version = \"4.5.1\";\n\n// src/api-keys/api-keys.ts\nvar ApiKeys = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\"/api-keys\", payload, options);\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/api-keys\");\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(`/api-keys/${id}`);\n      return data;\n    });\n  }\n};\n\n// src/audiences/audiences.ts\nvar Audiences = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\"/audiences\", payload, options);\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/audiences\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(`/audiences/${id}`);\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(`/audiences/${id}`);\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-email-to-api-options.ts\nfunction parseEmailToApiOptions(email) {\n  return {\n    attachments: email.attachments,\n    bcc: email.bcc,\n    cc: email.cc,\n    from: email.from,\n    headers: email.headers,\n    html: email.html,\n    reply_to: email.replyTo,\n    scheduled_at: email.scheduledAt,\n    subject: email.subject,\n    tags: email.tags,\n    text: email.text,\n    to: email.to\n  };\n}\n\n// src/batch/batch.ts\nvar Batch = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const emails = [];\n      for (const email of payload) {\n        if (email.react) {\n          if (!this.renderAsync) {\n            try {\n              const {\n                renderAsync\n              } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\"));\n              this.renderAsync = renderAsync;\n            } catch (error) {\n              throw new Error(\"Failed to render React component. Make sure to install `@react-email/render`\");\n            }\n          }\n          email.html = yield this.renderAsync(email.react);\n          email.react = void 0;\n        }\n        emails.push(parseEmailToApiOptions(email));\n      }\n      const data = yield this.resend.post(\"/emails/batch\", emails, options);\n      return data;\n    });\n  }\n};\n\n// src/broadcasts/broadcasts.ts\nvar Broadcasts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const {\n              renderAsync\n            } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\"Failed to render React component. Make sure to install `@react-email/render`\");\n          }\n        }\n        payload.html = yield this.renderAsync(payload.react);\n      }\n      const data = yield this.resend.post(\"/broadcasts\", {\n        name: payload.name,\n        audience_id: payload.audienceId,\n        preview_text: payload.previewText,\n        from: payload.from,\n        html: payload.html,\n        reply_to: payload.replyTo,\n        subject: payload.subject,\n        text: payload.text\n      }, options);\n      return data;\n    });\n  }\n  send(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(`/broadcasts/${id}/send`, {\n        scheduled_at: payload == null ? void 0 : payload.scheduledAt\n      });\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/broadcasts\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(`/broadcasts/${id}`);\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(`/broadcasts/${id}`);\n      return data;\n    });\n  }\n  update(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(`/broadcasts/${id}`, {\n        name: payload.name,\n        audience_id: payload.audienceId,\n        from: payload.from,\n        html: payload.html,\n        text: payload.text,\n        subject: payload.subject,\n        reply_to: payload.replyTo,\n        preview_text: payload.previewText\n      });\n      return data;\n    });\n  }\n};\n\n// src/contacts/contacts.ts\nvar Contacts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(`/audiences/${payload.audienceId}/contacts`, {\n        unsubscribed: payload.unsubscribed,\n        email: payload.email,\n        first_name: payload.firstName,\n        last_name: payload.lastName\n      }, options);\n      return data;\n    });\n  }\n  list(options) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(`/audiences/${options.audienceId}/contacts`);\n      return data;\n    });\n  }\n  get(options) {\n    return __async(this, null, function* () {\n      if (!options.id && !options.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.get(`/audiences/${options.audienceId}/contacts/${(options == null ? void 0 : options.email) ? options == null ? void 0 : options.email : options == null ? void 0 : options.id}`);\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.patch(`/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`, {\n        unsubscribed: payload.unsubscribed,\n        first_name: payload.firstName,\n        last_name: payload.lastName\n      });\n      return data;\n    });\n  }\n  remove(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.delete(`/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`);\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-domain-to-api-options.ts\nfunction parseDomainToApiOptions(domain) {\n  return {\n    name: domain.name,\n    region: domain.region,\n    custom_return_path: domain.customReturnPath\n  };\n}\n\n// src/domains/domains.ts\nvar Domains = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\"/domains\", parseDomainToApiOptions(payload), options);\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/domains\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(`/domains/${id}`);\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(`/domains/${payload.id}`, {\n        click_tracking: payload.clickTracking,\n        open_tracking: payload.openTracking,\n        tls: payload.tls\n      });\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(`/domains/${id}`);\n      return data;\n    });\n  }\n  verify(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(`/domains/${id}/verify`);\n      return data;\n    });\n  }\n};\n\n// src/emails/emails.ts\nvar Emails = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const {\n              renderAsync\n            } = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/dom-serializer\"), __webpack_require__.e(\"vendor-chunks/domutils\"), __webpack_require__.e(\"vendor-chunks/htmlparser2\"), __webpack_require__.e(\"vendor-chunks/peberminta\"), __webpack_require__.e(\"vendor-chunks/domhandler\"), __webpack_require__.e(\"vendor-chunks/selderee\"), __webpack_require__.e(\"vendor-chunks/parseley\"), __webpack_require__.e(\"vendor-chunks/leac\"), __webpack_require__.e(\"vendor-chunks/html-to-text\"), __webpack_require__.e(\"vendor-chunks/domelementtype\"), __webpack_require__.e(\"vendor-chunks/@selderee\"), __webpack_require__.e(\"vendor-chunks/@react-email\"), __webpack_require__.e(\"vendor-chunks/deepmerge\")]).then(__webpack_require__.bind(__webpack_require__, /*! @react-email/render */ \"(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\"));\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\"Failed to render React component. Make sure to install `@react-email/render`\");\n          }\n        }\n        payload.html = yield this.renderAsync(payload.react);\n      }\n      const data = yield this.resend.post(\"/emails\", parseEmailToApiOptions(payload), options);\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(`/emails/${id}`);\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(`/emails/${payload.id}`, {\n        scheduled_at: payload.scheduledAt\n      });\n      return data;\n    });\n  }\n  cancel(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(`/emails/${id}/cancel`);\n      return data;\n    });\n  }\n};\n\n// src/resend.ts\nvar defaultBaseUrl = \"https://api.resend.com\";\nvar defaultUserAgent = `resend-node:${version}`;\nvar baseUrl = typeof process !== \"undefined\" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;\nvar userAgent = typeof process !== \"undefined\" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;\nvar Resend = class {\n  constructor(key) {\n    this.key = key;\n    this.apiKeys = new ApiKeys(this);\n    this.audiences = new Audiences(this);\n    this.batch = new Batch(this);\n    this.broadcasts = new Broadcasts(this);\n    this.contacts = new Contacts(this);\n    this.domains = new Domains(this);\n    this.emails = new Emails(this);\n    if (!key) {\n      if (typeof process !== \"undefined\" && process.env) {\n        this.key = process.env.RESEND_API_KEY;\n      }\n      if (!this.key) {\n        throw new Error('Missing API key. Pass it to the constructor `new Resend(\"re_123\")`');\n      }\n    }\n    this.headers = new Headers({\n      Authorization: `Bearer ${this.key}`,\n      \"User-Agent\": userAgent,\n      \"Content-Type\": \"application/json\"\n    });\n  }\n  fetchRequest(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      try {\n        const response = yield fetch(`${baseUrl}${path}`, options);\n        if (!response.ok) {\n          try {\n            const rawError = yield response.text();\n            return {\n              data: null,\n              error: JSON.parse(rawError)\n            };\n          } catch (err) {\n            if (err instanceof SyntaxError) {\n              return {\n                data: null,\n                error: {\n                  name: \"application_error\",\n                  message: \"Internal server error. We are unable to process your request right now, please try again later.\"\n                }\n              };\n            }\n            const error = {\n              message: response.statusText,\n              name: \"application_error\"\n            };\n            if (err instanceof Error) {\n              return {\n                data: null,\n                error: __spreadProps(__spreadValues({}, error), {\n                  message: err.message\n                })\n              };\n            }\n            return {\n              data: null,\n              error\n            };\n          }\n        }\n        const data = yield response.json();\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            name: \"application_error\",\n            message: \"Unable to fetch data. The request could not be resolved.\"\n          }\n        };\n      }\n    });\n  }\n  post(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const headers = new Headers(this.headers);\n      if (options.idempotencyKey) {\n        headers.set(\"Idempotency-Key\", options.idempotencyKey);\n      }\n      const requestOptions = __spreadValues({\n        method: \"POST\",\n        headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"GET\",\n        headers: this.headers\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  put(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PUT\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  patch(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PATCH\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  delete(path, query) {\n    return __async(this, null, function* () {\n      const requestOptions = {\n        method: \"DELETE\",\n        headers: this.headers,\n        body: JSON.stringify(query)\n      };\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resend/dist/index.mjs\n");

/***/ })

};
;