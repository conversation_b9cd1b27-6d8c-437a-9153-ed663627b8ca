// src/hooks/useUserPlan.ts
// Hook para obtener el plan del usuario actual

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface UserPlanData {
  plan: string;
  isLoading: boolean;
  error: string | null;
}

export function useUserPlan(): UserPlanData {
  const [plan, setPlan] = useState<string>('free');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const fetchUserPlan = async () => {
      if (!user) {
        setPlan('free');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/user/plan');
        
        if (!response.ok) {
          throw new Error('Error obteniendo plan del usuario');
        }

        const data = await response.json();
        setPlan(data.plan || 'free');

      } catch (err) {
        console.error('Error fetching user plan:', err);
        setError(err instanceof Error ? err.message : 'Error desconocido');
        setPlan('free'); // Fallback a plan gratuito
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserPlan();
  }, [user]);

  return {
    plan,
    isLoading,
    error
  };
}
