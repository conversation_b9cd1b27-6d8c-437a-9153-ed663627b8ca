import { supabase, Test, PreguntaTest, EstadisticaTest, EstadisticasGeneralesTest, EstadisticasTestEspecifico } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

/**
 * Crea un nuevo test
 */
export async function crearTest(titulo: string, descripcion?: string, documentosIds?: string[]): Promise<string | null> {
  try {
    console.log('📝 Creando nuevo test:', titulo);

    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('❌ No hay usuario autenticado para crear test');
      return null;
    }

    console.log('👤 Usuario autenticado:', user.id);

    const { data, error } = await supabase
      .from('tests')
      .insert([{
        titulo,
        descripcion,
        documentos_ids: documentosIds,
        user_id: user.id
      }])
      .select();

    if (error) {
      console.error('❌ Error al crear test:', error);
      return null;
    }

    console.log('✅ Test creado exitosamente:', data?.[0]?.id);
    return data?.[0]?.id || null;
  } catch (error) {
    console.error('💥 Error inesperado al crear test:', error);
    return null;
  }
}

/**
 * Obtiene todos los tests del usuario actual
 */
export async function obtenerTests(): Promise<Test[]> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('tests')
      .select('*')
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener tests:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener tests:', error);
    return [];
  }
}

/**
 * Obtiene un test por su ID (solo si pertenece al usuario actual)
 */
export async function obtenerTestPorId(id: string): Promise<Test | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('tests')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener test:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener test:', error);
    return null;
  }
}

/**
 * Crea una nueva pregunta para un test
 */
export async function crearPreguntaTest(
  testId: string,
  pregunta: string,
  opcionA: string,
  opcionB: string,
  opcionC: string,
  opcionD: string,
  respuestaCorrecta: 'a' | 'b' | 'c' | 'd'
): Promise<string | null> {
  const { data, error } = await supabase
    .from('preguntas_test')
    .insert([{
      test_id: testId,
      pregunta,
      opcion_a: opcionA,
      opcion_b: opcionB,
      opcion_c: opcionC,
      opcion_d: opcionD,
      respuesta_correcta: respuestaCorrecta
    }])
    .select();

  if (error) {
    console.error('Error al crear pregunta de test:', error);
    return null;
  }

  return data?.[0]?.id || null;
}

/**
 * Obtiene todas las preguntas de un test
 */
export async function obtenerPreguntasPorTestId(testId: string): Promise<PreguntaTest[]> {
  const { data, error } = await supabase
    .from('preguntas_test')
    .select('*')
    .eq('test_id', testId);

  if (error) {
    console.error('Error al obtener preguntas de test:', error);
    return [];
  }

  return data || [];
}

/**
 * Obtiene el número de preguntas de un test
 */
export async function obtenerPreguntasTestCount(testId: string): Promise<number> {
  const { count, error } = await supabase
    .from('preguntas_test')
    .select('*', { count: 'exact', head: true })
    .eq('test_id', testId);

  if (error) {
    console.error('Error al obtener conteo de preguntas:', error);
    return 0;
  }

  return count || 0;
}

/**
 * Guarda múltiples preguntas de test
 */
export async function guardarPreguntasTest(preguntas: Omit<PreguntaTest, 'id'>[]): Promise<boolean> {
  const { error } = await supabase
    .from('preguntas_test')
    .insert(preguntas);

  if (error) {
    console.error('Error al guardar preguntas de test:', error);
    return false;
  }

  return true;
}

/**
 * Registra la respuesta de un usuario a una pregunta de test
 */
export async function registrarRespuestaTest(
  testId: string,
  preguntaId: string,
  respuestaUsuario: 'a' | 'b' | 'c' | 'd' | 'x',
  esCorrecta: boolean
): Promise<boolean> {
  const { error } = await supabase
    .from('estadisticas_test')
    .insert([{
      test_id: testId,
      pregunta_id: preguntaId,
      respuesta_seleccionada: respuestaUsuario,
      es_correcta: esCorrecta,
      fecha_respuesta: new Date().toISOString()
    }]);

  if (error) {
    console.error('Error al registrar respuesta de test:', error);
    return false;
  }

  return true;
}

/**
 * Obtiene estadísticas generales de todos los tests
 */
export async function obtenerEstadisticasGeneralesTests(): Promise<EstadisticasGeneralesTest> {
  // Obtener todas las respuestas
  const { data: respuestas, error } = await supabase
    .from('estadisticas_test')
    .select('*');

  if (error) {
    console.error('Error al obtener estadísticas de tests:', error);
    return {
      totalTests: 0,
      totalPreguntas: 0,
      totalRespuestasCorrectas: 0,
      totalRespuestasIncorrectas: 0,
      porcentajeAcierto: 0
    };
  }

  // Obtener tests únicos respondidos
  const testsUnicos = new Set(respuestas?.map(r => r.test_id) || []);

  // Obtener preguntas únicas respondidas
  const preguntasUnicas = new Set(respuestas?.map(r => r.pregunta_id) || []);

  // Contar respuestas correctas e incorrectas
  const correctas = respuestas?.filter(r => r.es_correcta).length || 0;
  const incorrectas = (respuestas?.length || 0) - correctas;

  // Calcular porcentaje de acierto
  const porcentaje = respuestas && respuestas.length > 0
    ? Math.round((correctas / respuestas.length) * 100)
    : 0;

  return {
    totalTests: testsUnicos.size,
    totalPreguntas: preguntasUnicas.size,
    totalRespuestasCorrectas: correctas,
    totalRespuestasIncorrectas: incorrectas,
    porcentajeAcierto: porcentaje
  };
}

/**
 * Obtiene estadísticas detalladas de un test específico
 */
export async function obtenerEstadisticasTest(testId: string): Promise<EstadisticasTestEspecifico> {
  // Obtener todas las respuestas del test
  const { data: respuestas, error } = await supabase
    .from('estadisticas_test')
    .select('*')
    .eq('test_id', testId);

  if (error) {
    console.error('Error al obtener estadísticas del test:', error);
    return {
      testId: testId,
      totalPreguntas: 0,
      totalCorrectas: 0,
      totalIncorrectas: 0,
      porcentajeAcierto: 0,
      fechasRealizacion: [],
      preguntasMasFalladas: []
    };
  }

  // Obtener preguntas del test
  const { data: preguntas } = await supabase
    .from('preguntas_test')
    .select('*')
    .eq('test_id', testId);

  // Contar respuestas correctas e incorrectas
  const correctas = respuestas?.filter(r => r.es_correcta).length || 0;
  const incorrectas = (respuestas?.length || 0) - correctas;

  // Calcular porcentaje de acierto
  const porcentaje = respuestas && respuestas.length > 0
    ? Math.round((correctas / respuestas.length) * 100)
    : 0;

  // Obtener fechas únicas de realización
  const fechasSet = new Set<string>();
  respuestas?.forEach(r => {
    const fecha = new Date(r.fecha_respuesta);
    fechasSet.add(`${fecha.getDate()}/${fecha.getMonth() + 1}/${fecha.getFullYear()}`);
  });
  const fechasUnicas = Array.from(fechasSet);

  // Calcular preguntas más falladas
  const fallosPorPregunta = new Map<string, { fallos: number, aciertos: number }>();

  respuestas?.forEach(respuesta => {
    const actual = fallosPorPregunta.get(respuesta.pregunta_id) || { fallos: 0, aciertos: 0 };

    if (respuesta.es_correcta) {
      actual.aciertos++;
    } else {
      actual.fallos++;
    }

    fallosPorPregunta.set(respuesta.pregunta_id, actual);
  });

  // Convertir a array y ordenar por fallos
  const preguntasFalladas = Array.from(fallosPorPregunta.entries())
    .map(([id, stats]) => ({
      preguntaId: id,
      totalFallos: stats.fallos,
      totalAciertos: stats.aciertos,
      // Encontrar la pregunta correspondiente
      pregunta: preguntas?.find(p => p.id === id)?.pregunta || 'Desconocida',
    }))
    .sort((a, b) => b.totalFallos - a.totalFallos)
    .slice(0, 5); // Tomar las 5 más falladas

  return {
    testId: testId,
    totalPreguntas: preguntas?.length || 0,
    totalCorrectas: correctas,
    totalIncorrectas: incorrectas,
    porcentajeAcierto: porcentaje,
    fechasRealizacion: fechasUnicas,
    preguntasMasFalladas: preguntasFalladas
  };
}
