// Exportar todo desde los archivos individuales
export * from './geminiClient';
export * from './questionService';
export * from './flashcardGenerator';
export * from './testGenerator';
export * from './mindMapGenerator';
export * from './resumenGenerator';

// Definición de interfaces para compatibilidad
export interface PreguntaGenerada {
  pregunta: string;
  opciones: {
    a: string;
    b: string;
    c: string;
    d: string;
  };
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}

// Función adaptadora para compatibilidad con la interfaz anterior de flashcards
export async function generarFlashcards(
  peticion: string,
  contextos: string[],
  cantidad: number = 10
): Promise<{ pregunta: string; respuesta: string }[]> {
  // Convertir los contextos al formato esperado por la función original
  const documentos = contextos.map((contenido, index) => ({
    titulo: `Documento ${index + 1}`,
    contenido
  }));

  // Llamar a la función original con los documentos formateados y la petición como instrucción
  return await import('./flashcardGenerator').then(module =>
    module.generarFlashcards(documentos, cantidad, peticion)
  );
}

// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales
export async function generarMapaMental(
  peticion: string,
  contextos: string[]
): Promise<any> {
  // Convertir los contextos al formato esperado por la función original
  const documentos = contextos.map((contenido, index) => ({
    titulo: `Documento ${index + 1}`,
    contenido
  }));

  // Llamar a la función original con los documentos formateados y la petición como instrucción
  return await import('./mindMapGenerator').then(module =>
    module.generarMapaMental(documentos, peticion)
  );
}

// Función adaptadora para compatibilidad con la interfaz anterior de tests
export async function generarTest(
  peticion: string,
  contextos: string[],
  cantidad: number = 10
): Promise<PreguntaGenerada[]> {
  // Convertir los contextos al formato esperado por la función original
  const documentos = contextos.map((contenido, index) => ({
    titulo: `Documento ${index + 1}`,
    contenido
  }));

  // Llamar a la función original con los documentos formateados y la petición como instrucción
  const result = await import('./testGenerator').then(module =>
    module.generarTest(documentos, cantidad, peticion)
  );

  // Convertir el formato de la respuesta al formato esperado por el componente
  return result.map(item => ({
    pregunta: item.pregunta,
    opciones: {
      a: item.opcion_a,
      b: item.opcion_b,
      c: item.opcion_c,
      d: item.opcion_d
    },
    respuesta_correcta: item.respuesta_correcta
  }));
}

// Función adaptadora para compatibilidad con la interfaz anterior de resúmenes
export async function generarResumen(
  documento: { titulo: string; contenido: string; categoria?: string; numero_tema?: number },
  instrucciones?: string
): Promise<string> {
  // Llamar directamente a la función original ya que el formato coincide
  return await import('./resumenGenerator').then(module =>
    module.generarResumen(documento, instrucciones)
  );
}
