"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/peberminta";
exports.ids = ["vendor-chunks/peberminta"];
exports.modules = {

/***/ "(rsc)/./node_modules/peberminta/lib/core.mjs":
/*!**********************************************!*\
  !*** ./node_modules/peberminta/lib/core.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ab: () => (/* binding */ ab),\n/* harmony export */   abc: () => (/* binding */ abc),\n/* harmony export */   action: () => (/* binding */ action),\n/* harmony export */   ahead: () => (/* binding */ ahead),\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   and: () => (/* binding */ all),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   chain: () => (/* binding */ chain),\n/* harmony export */   chainReduce: () => (/* binding */ chainReduce),\n/* harmony export */   choice: () => (/* binding */ choice),\n/* harmony export */   condition: () => (/* binding */ condition),\n/* harmony export */   decide: () => (/* binding */ decide),\n/* harmony export */   discard: () => (/* binding */ skip),\n/* harmony export */   eitherOr: () => (/* binding */ otherwise),\n/* harmony export */   emit: () => (/* binding */ emit),\n/* harmony export */   end: () => (/* binding */ end),\n/* harmony export */   eof: () => (/* binding */ end),\n/* harmony export */   error: () => (/* binding */ error),\n/* harmony export */   fail: () => (/* binding */ fail),\n/* harmony export */   flatten: () => (/* binding */ flatten),\n/* harmony export */   flatten1: () => (/* binding */ flatten1),\n/* harmony export */   left: () => (/* binding */ left),\n/* harmony export */   leftAssoc1: () => (/* binding */ leftAssoc1),\n/* harmony export */   leftAssoc2: () => (/* binding */ leftAssoc2),\n/* harmony export */   longest: () => (/* binding */ longest),\n/* harmony export */   lookAhead: () => (/* binding */ ahead),\n/* harmony export */   make: () => (/* binding */ make),\n/* harmony export */   many: () => (/* binding */ many),\n/* harmony export */   many1: () => (/* binding */ many1),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   map1: () => (/* binding */ map1),\n/* harmony export */   match: () => (/* binding */ match),\n/* harmony export */   middle: () => (/* binding */ middle),\n/* harmony export */   not: () => (/* binding */ not),\n/* harmony export */   of: () => (/* binding */ emit),\n/* harmony export */   option: () => (/* binding */ option),\n/* harmony export */   or: () => (/* binding */ choice),\n/* harmony export */   otherwise: () => (/* binding */ otherwise),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parserPosition: () => (/* binding */ parserPosition),\n/* harmony export */   peek: () => (/* binding */ peek),\n/* harmony export */   recursive: () => (/* binding */ recursive),\n/* harmony export */   reduceLeft: () => (/* binding */ reduceLeft),\n/* harmony export */   reduceRight: () => (/* binding */ reduceRight),\n/* harmony export */   remainingTokensNumber: () => (/* binding */ remainingTokensNumber),\n/* harmony export */   right: () => (/* binding */ right),\n/* harmony export */   rightAssoc1: () => (/* binding */ rightAssoc1),\n/* harmony export */   rightAssoc2: () => (/* binding */ rightAssoc2),\n/* harmony export */   satisfy: () => (/* binding */ satisfy),\n/* harmony export */   sepBy: () => (/* binding */ sepBy),\n/* harmony export */   sepBy1: () => (/* binding */ sepBy1),\n/* harmony export */   skip: () => (/* binding */ skip),\n/* harmony export */   some: () => (/* binding */ many1),\n/* harmony export */   start: () => (/* binding */ start),\n/* harmony export */   takeUntil: () => (/* binding */ takeUntil),\n/* harmony export */   takeUntilP: () => (/* binding */ takeUntilP),\n/* harmony export */   takeWhile: () => (/* binding */ takeWhile),\n/* harmony export */   takeWhileP: () => (/* binding */ takeWhileP),\n/* harmony export */   token: () => (/* binding */ token),\n/* harmony export */   tryParse: () => (/* binding */ tryParse)\n/* harmony export */ });\n/* harmony import */ var _util_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.mjs */ \"(rsc)/./node_modules/peberminta/lib/util.mjs\");\n\nfunction emit(value) {\n  return (data, i) => ({\n    matched: true,\n    position: i,\n    value: value\n  });\n}\nfunction make(f) {\n  return (data, i) => ({\n    matched: true,\n    position: i,\n    value: f(data, i)\n  });\n}\nfunction action(f) {\n  return (data, i) => {\n    f(data, i);\n    return {\n      matched: true,\n      position: i,\n      value: null\n    };\n  };\n}\nfunction fail(data, i) {\n  return {\n    matched: false\n  };\n}\nfunction error(message) {\n  return (data, i) => {\n    throw new Error(message instanceof Function ? message(data, i) : message);\n  };\n}\nfunction token(onToken, onEnd) {\n  return (data, i) => {\n    let position = i;\n    let value = undefined;\n    if (i < data.tokens.length) {\n      value = onToken(data.tokens[i], data, i);\n      if (value !== undefined) {\n        position++;\n      }\n    } else {\n      onEnd?.(data, i);\n    }\n    return value === undefined ? {\n      matched: false\n    } : {\n      matched: true,\n      position: position,\n      value: value\n    };\n  };\n}\nfunction any(data, i) {\n  return i < data.tokens.length ? {\n    matched: true,\n    position: i + 1,\n    value: data.tokens[i]\n  } : {\n    matched: false\n  };\n}\nfunction satisfy(test) {\n  return (data, i) => i < data.tokens.length && test(data.tokens[i], data, i) ? {\n    matched: true,\n    position: i + 1,\n    value: data.tokens[i]\n  } : {\n    matched: false\n  };\n}\nfunction mapInner(r, f) {\n  return r.matched ? {\n    matched: true,\n    position: r.position,\n    value: f(r.value, r.position)\n  } : r;\n}\nfunction mapOuter(r, f) {\n  return r.matched ? f(r) : r;\n}\nfunction map(p, mapper) {\n  return (data, i) => mapInner(p(data, i), (v, j) => mapper(v, data, i, j));\n}\nfunction map1(p, mapper) {\n  return (data, i) => mapOuter(p(data, i), m => mapper(m, data, i));\n}\nfunction peek(p, f) {\n  return (data, i) => {\n    const r = p(data, i);\n    f(r, data, i);\n    return r;\n  };\n}\nfunction option(p, def) {\n  return (data, i) => {\n    const r = p(data, i);\n    return r.matched ? r : {\n      matched: true,\n      position: i,\n      value: def\n    };\n  };\n}\nfunction not(p) {\n  return (data, i) => {\n    const r = p(data, i);\n    return r.matched ? {\n      matched: false\n    } : {\n      matched: true,\n      position: i,\n      value: true\n    };\n  };\n}\nfunction choice(...ps) {\n  return (data, i) => {\n    for (const p of ps) {\n      const result = p(data, i);\n      if (result.matched) {\n        return result;\n      }\n    }\n    return {\n      matched: false\n    };\n  };\n}\nfunction otherwise(pa, pb) {\n  return (data, i) => {\n    const r1 = pa(data, i);\n    return r1.matched ? r1 : pb(data, i);\n  };\n}\nfunction longest(...ps) {\n  return (data, i) => {\n    let match = undefined;\n    for (const p of ps) {\n      const result = p(data, i);\n      if (result.matched && (!match || match.position < result.position)) {\n        match = result;\n      }\n    }\n    return match || {\n      matched: false\n    };\n  };\n}\nfunction takeWhile(p, test) {\n  return (data, i) => {\n    const values = [];\n    let success = true;\n    do {\n      const r = p(data, i);\n      if (r.matched && test(r.value, values.length + 1, data, i, r.position)) {\n        values.push(r.value);\n        i = r.position;\n      } else {\n        success = false;\n      }\n    } while (success);\n    return {\n      matched: true,\n      position: i,\n      value: values\n    };\n  };\n}\nfunction takeUntil(p, test) {\n  return takeWhile(p, (value, n, data, i, j) => !test(value, n, data, i, j));\n}\nfunction takeWhileP(pValue, pTest) {\n  return takeWhile(pValue, (value, n, data, i) => pTest(data, i).matched);\n}\nfunction takeUntilP(pValue, pTest) {\n  return takeWhile(pValue, (value, n, data, i) => !pTest(data, i).matched);\n}\nfunction many(p) {\n  return takeWhile(p, () => true);\n}\nfunction many1(p) {\n  return ab(p, many(p), (head, tail) => [head, ...tail]);\n}\nfunction ab(pa, pb, join) {\n  return (data, i) => mapOuter(pa(data, i), ma => mapInner(pb(data, ma.position), (vb, j) => join(ma.value, vb, data, i, j)));\n}\nfunction left(pa, pb) {\n  return ab(pa, pb, va => va);\n}\nfunction right(pa, pb) {\n  return ab(pa, pb, (va, vb) => vb);\n}\nfunction abc(pa, pb, pc, join) {\n  return (data, i) => mapOuter(pa(data, i), ma => mapOuter(pb(data, ma.position), mb => mapInner(pc(data, mb.position), (vc, j) => join(ma.value, mb.value, vc, data, i, j))));\n}\nfunction middle(pa, pb, pc) {\n  return abc(pa, pb, pc, (ra, rb) => rb);\n}\nfunction all(...ps) {\n  return (data, i) => {\n    const result = [];\n    let position = i;\n    for (const p of ps) {\n      const r1 = p(data, position);\n      if (r1.matched) {\n        result.push(r1.value);\n        position = r1.position;\n      } else {\n        return {\n          matched: false\n        };\n      }\n    }\n    return {\n      matched: true,\n      position: position,\n      value: result\n    };\n  };\n}\nfunction skip(...ps) {\n  return map(all(...ps), () => null);\n}\nfunction flatten(...ps) {\n  return flatten1(all(...ps));\n}\nfunction flatten1(p) {\n  return map(p, vs => vs.flatMap(v => v));\n}\nfunction sepBy1(pValue, pSep) {\n  return ab(pValue, many(right(pSep, pValue)), (head, tail) => [head, ...tail]);\n}\nfunction sepBy(pValue, pSep) {\n  return otherwise(sepBy1(pValue, pSep), emit([]));\n}\nfunction chainReduce(acc, f) {\n  return (data, i) => {\n    let loop = true;\n    let acc1 = acc;\n    let pos = i;\n    do {\n      const r = f(acc1, data, pos)(data, pos);\n      if (r.matched) {\n        acc1 = r.value;\n        pos = r.position;\n      } else {\n        loop = false;\n      }\n    } while (loop);\n    return {\n      matched: true,\n      position: pos,\n      value: acc1\n    };\n  };\n}\nfunction reduceLeft(acc, p, reducer) {\n  return chainReduce(acc, acc => map(p, (v, data, i, j) => reducer(acc, v, data, i, j)));\n}\nfunction reduceRight(p, acc, reducer) {\n  return map(many(p), (vs, data, i, j) => vs.reduceRight((acc, v) => reducer(v, acc, data, i, j), acc));\n}\nfunction leftAssoc1(pLeft, pOper) {\n  return chain(pLeft, v0 => reduceLeft(v0, pOper, (acc, f) => f(acc)));\n}\nfunction rightAssoc1(pOper, pRight) {\n  return ab(reduceRight(pOper, y => y, (f, acc) => y => f(acc(y))), pRight, (f, v) => f(v));\n}\nfunction leftAssoc2(pLeft, pOper, pRight) {\n  return chain(pLeft, v0 => reduceLeft(v0, ab(pOper, pRight, (f, y) => [f, y]), (acc, [f, y]) => f(acc, y)));\n}\nfunction rightAssoc2(pLeft, pOper, pRight) {\n  return ab(reduceRight(ab(pLeft, pOper, (x, f) => [x, f]), y => y, ([x, f], acc) => y => f(x, acc(y))), pRight, (f, v) => f(v));\n}\nfunction condition(cond, pTrue, pFalse) {\n  return (data, i) => cond(data, i) ? pTrue(data, i) : pFalse(data, i);\n}\nfunction decide(p) {\n  return (data, i) => mapOuter(p(data, i), m1 => m1.value(data, m1.position));\n}\nfunction chain(p, f) {\n  return (data, i) => mapOuter(p(data, i), m1 => f(m1.value, data, i, m1.position)(data, m1.position));\n}\nfunction ahead(p) {\n  return (data, i) => mapOuter(p(data, i), m1 => ({\n    matched: true,\n    position: i,\n    value: m1.value\n  }));\n}\nfunction recursive(f) {\n  return function (data, i) {\n    return f()(data, i);\n  };\n}\nfunction start(data, i) {\n  return i !== 0 ? {\n    matched: false\n  } : {\n    matched: true,\n    position: i,\n    value: true\n  };\n}\nfunction end(data, i) {\n  return i < data.tokens.length ? {\n    matched: false\n  } : {\n    matched: true,\n    position: i,\n    value: true\n  };\n}\nfunction remainingTokensNumber(data, i) {\n  return data.tokens.length - i;\n}\nfunction parserPosition(data, i, formatToken, contextTokens = 3) {\n  const len = data.tokens.length;\n  const lowIndex = (0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, i - contextTokens, len - contextTokens);\n  const highIndex = (0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(contextTokens, i + 1 + contextTokens, len);\n  const tokensSlice = data.tokens.slice(lowIndex, highIndex);\n  const lines = [];\n  const indexWidth = String(highIndex - 1).length + 1;\n  if (i < 0) {\n    lines.push(`${String(i).padStart(indexWidth)} >>`);\n  }\n  if (0 < lowIndex) {\n    lines.push('...'.padStart(indexWidth + 6));\n  }\n  for (let j = 0; j < tokensSlice.length; j++) {\n    const index = lowIndex + j;\n    lines.push(`${String(index).padStart(indexWidth)} ${index === i ? '>' : ' '} ${(0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.escapeWhitespace)(formatToken(tokensSlice[j]))}`);\n  }\n  if (highIndex < len) {\n    lines.push('...'.padStart(indexWidth + 6));\n  }\n  if (len <= i) {\n    lines.push(`${String(i).padStart(indexWidth)} >>`);\n  }\n  return lines.join('\\n');\n}\nfunction parse(parser, tokens, options, formatToken = JSON.stringify) {\n  const data = {\n    tokens: tokens,\n    options: options\n  };\n  const result = parser(data, 0);\n  if (!result.matched) {\n    throw new Error('No match');\n  }\n  if (result.position < data.tokens.length) {\n    throw new Error(`Partial match. Parsing stopped at:\\n${parserPosition(data, result.position, formatToken)}`);\n  }\n  return result.value;\n}\nfunction tryParse(parser, tokens, options) {\n  const result = parser({\n    tokens: tokens,\n    options: options\n  }, 0);\n  return result.matched ? result.value : undefined;\n}\nfunction match(matcher, tokens, options) {\n  const result = matcher({\n    tokens: tokens,\n    options: options\n  }, 0);\n  return result.value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGViZXJtaW50YS9saWIvY29yZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxRDtBQUVyRCxTQUFTRSxJQUFJQSxDQUFDQyxLQUFLLEVBQUU7RUFDakIsT0FBTyxDQUFDQyxJQUFJLEVBQUVDLENBQUMsTUFBTTtJQUNqQkMsT0FBTyxFQUFFLElBQUk7SUFDYkMsUUFBUSxFQUFFRixDQUFDO0lBQ1hGLEtBQUssRUFBRUE7RUFDWCxDQUFDLENBQUM7QUFDTjtBQUNBLFNBQVNLLElBQUlBLENBQ2JDLENBQUMsRUFBRTtFQUNDLE9BQU8sQ0FBQ0wsSUFBSSxFQUFFQyxDQUFDLE1BQU07SUFDakJDLE9BQU8sRUFBRSxJQUFJO0lBQ2JDLFFBQVEsRUFBRUYsQ0FBQztJQUNYRixLQUFLLEVBQUVNLENBQUMsQ0FBQ0wsSUFBSSxFQUFFQyxDQUFDO0VBQ3BCLENBQUMsQ0FBQztBQUNOO0FBQ0EsU0FBU0ssTUFBTUEsQ0FDZkQsQ0FBQyxFQUFFO0VBQ0MsT0FBTyxDQUFDTCxJQUFJLEVBQUVDLENBQUMsS0FBSztJQUNoQkksQ0FBQyxDQUFDTCxJQUFJLEVBQUVDLENBQUMsQ0FBQztJQUNWLE9BQU87TUFDSEMsT0FBTyxFQUFFLElBQUk7TUFDYkMsUUFBUSxFQUFFRixDQUFDO01BQ1hGLEtBQUssRUFBRTtJQUNYLENBQUM7RUFDTCxDQUFDO0FBQ0w7QUFDQSxTQUFTUSxJQUFJQSxDQUNiUCxJQUFJLEVBQUVDLENBQUMsRUFBRTtFQUNMLE9BQU87SUFBRUMsT0FBTyxFQUFFO0VBQU0sQ0FBQztBQUM3QjtBQUNBLFNBQVNNLEtBQUtBLENBQUNDLE9BQU8sRUFBRTtFQUNwQixPQUFPLENBQUNULElBQUksRUFBRUMsQ0FBQyxLQUFLO0lBQ2hCLE1BQU0sSUFBSVMsS0FBSyxDQUFFRCxPQUFPLFlBQVlFLFFBQVEsR0FBSUYsT0FBTyxDQUFDVCxJQUFJLEVBQUVDLENBQUMsQ0FBQyxHQUFHUSxPQUFPLENBQUM7RUFDL0UsQ0FBQztBQUNMO0FBQ0EsU0FBU0csS0FBS0EsQ0FDZEMsT0FBTyxFQUNQQyxLQUFLLEVBQUU7RUFDSCxPQUFPLENBQUNkLElBQUksRUFBRUMsQ0FBQyxLQUFLO0lBQ2hCLElBQUlFLFFBQVEsR0FBR0YsQ0FBQztJQUNoQixJQUFJRixLQUFLLEdBQUdnQixTQUFTO0lBQ3JCLElBQUlkLENBQUMsR0FBR0QsSUFBSSxDQUFDZ0IsTUFBTSxDQUFDQyxNQUFNLEVBQUU7TUFDeEJsQixLQUFLLEdBQUdjLE9BQU8sQ0FBQ2IsSUFBSSxDQUFDZ0IsTUFBTSxDQUFDZixDQUFDLENBQUMsRUFBRUQsSUFBSSxFQUFFQyxDQUFDLENBQUM7TUFDeEMsSUFBSUYsS0FBSyxLQUFLZ0IsU0FBUyxFQUFFO1FBQ3JCWixRQUFRLEVBQUU7TUFDZDtJQUNKLENBQUMsTUFDSTtNQUNEVyxLQUFLLEdBQUdkLElBQUksRUFBRUMsQ0FBQyxDQUFDO0lBQ3BCO0lBQ0EsT0FBUUYsS0FBSyxLQUFLZ0IsU0FBUyxHQUNyQjtNQUFFYixPQUFPLEVBQUU7SUFBTSxDQUFDLEdBQ2xCO01BQ0VBLE9BQU8sRUFBRSxJQUFJO01BQ2JDLFFBQVEsRUFBRUEsUUFBUTtNQUNsQkosS0FBSyxFQUFFQTtJQUNYLENBQUM7RUFDVCxDQUFDO0FBQ0w7QUFDQSxTQUFTbUIsR0FBR0EsQ0FBQ2xCLElBQUksRUFBRUMsQ0FBQyxFQUFFO0VBQ2xCLE9BQVFBLENBQUMsR0FBR0QsSUFBSSxDQUFDZ0IsTUFBTSxDQUFDQyxNQUFNLEdBQ3hCO0lBQ0VmLE9BQU8sRUFBRSxJQUFJO0lBQ2JDLFFBQVEsRUFBRUYsQ0FBQyxHQUFHLENBQUM7SUFDZkYsS0FBSyxFQUFFQyxJQUFJLENBQUNnQixNQUFNLENBQUNmLENBQUM7RUFDeEIsQ0FBQyxHQUNDO0lBQUVDLE9BQU8sRUFBRTtFQUFNLENBQUM7QUFDNUI7QUFDQSxTQUFTaUIsT0FBT0EsQ0FDaEJDLElBQUksRUFBRTtFQUNGLE9BQU8sQ0FBQ3BCLElBQUksRUFBRUMsQ0FBQyxLQUFNQSxDQUFDLEdBQUdELElBQUksQ0FBQ2dCLE1BQU0sQ0FBQ0MsTUFBTSxJQUFJRyxJQUFJLENBQUNwQixJQUFJLENBQUNnQixNQUFNLENBQUNmLENBQUMsQ0FBQyxFQUFFRCxJQUFJLEVBQUVDLENBQUMsQ0FBQyxHQUN0RTtJQUNFQyxPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUVGLENBQUMsR0FBRyxDQUFDO0lBQ2ZGLEtBQUssRUFBRUMsSUFBSSxDQUFDZ0IsTUFBTSxDQUFDZixDQUFDO0VBQ3hCLENBQUMsR0FDQztJQUFFQyxPQUFPLEVBQUU7RUFBTSxDQUFDO0FBQzVCO0FBQ0EsU0FBU21CLFFBQVFBLENBQUNDLENBQUMsRUFBRWpCLENBQUMsRUFBRTtFQUNwQixPQUFRaUIsQ0FBQyxDQUFDcEIsT0FBTyxHQUFLO0lBQ2xCQSxPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUVtQixDQUFDLENBQUNuQixRQUFRO0lBQ3BCSixLQUFLLEVBQUVNLENBQUMsQ0FBQ2lCLENBQUMsQ0FBQ3ZCLEtBQUssRUFBRXVCLENBQUMsQ0FBQ25CLFFBQVE7RUFDaEMsQ0FBQyxHQUFJbUIsQ0FBQztBQUNWO0FBQ0EsU0FBU0MsUUFBUUEsQ0FBQ0QsQ0FBQyxFQUFFakIsQ0FBQyxFQUFFO0VBQ3BCLE9BQVFpQixDQUFDLENBQUNwQixPQUFPLEdBQUlHLENBQUMsQ0FBQ2lCLENBQUMsQ0FBQyxHQUFHQSxDQUFDO0FBQ2pDO0FBQ0EsU0FBU0UsR0FBR0EsQ0FBQ0MsQ0FBQyxFQUFFQyxNQUFNLEVBQUU7RUFDcEIsT0FBTyxDQUFDMUIsSUFBSSxFQUFFQyxDQUFDLEtBQUtvQixRQUFRLENBQUNJLENBQUMsQ0FBQ3pCLElBQUksRUFBRUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQzBCLENBQUMsRUFBRUMsQ0FBQyxLQUFLRixNQUFNLENBQUNDLENBQUMsRUFBRTNCLElBQUksRUFBRUMsQ0FBQyxFQUFFMkIsQ0FBQyxDQUFDLENBQUM7QUFDN0U7QUFDQSxTQUFTQyxJQUFJQSxDQUFDSixDQUFDLEVBQ2ZDLE1BQU0sRUFBRTtFQUNKLE9BQU8sQ0FBQzFCLElBQUksRUFBRUMsQ0FBQyxLQUFLc0IsUUFBUSxDQUFDRSxDQUFDLENBQUN6QixJQUFJLEVBQUVDLENBQUMsQ0FBQyxFQUFHNkIsQ0FBQyxJQUFLSixNQUFNLENBQUNJLENBQUMsRUFBRTlCLElBQUksRUFBRUMsQ0FBQyxDQUFDLENBQUM7QUFDdkU7QUFDQSxTQUFTOEIsSUFBSUEsQ0FBQ04sQ0FBQyxFQUFFcEIsQ0FBQyxFQUFFO0VBQ2hCLE9BQU8sQ0FBQ0wsSUFBSSxFQUFFQyxDQUFDLEtBQUs7SUFDaEIsTUFBTXFCLENBQUMsR0FBR0csQ0FBQyxDQUFDekIsSUFBSSxFQUFFQyxDQUFDLENBQUM7SUFDcEJJLENBQUMsQ0FBQ2lCLENBQUMsRUFBRXRCLElBQUksRUFBRUMsQ0FBQyxDQUFDO0lBQ2IsT0FBT3FCLENBQUM7RUFDWixDQUFDO0FBQ0w7QUFDQSxTQUFTVSxNQUFNQSxDQUFDUCxDQUFDLEVBQUVRLEdBQUcsRUFBRTtFQUNwQixPQUFPLENBQUNqQyxJQUFJLEVBQUVDLENBQUMsS0FBSztJQUNoQixNQUFNcUIsQ0FBQyxHQUFHRyxDQUFDLENBQUN6QixJQUFJLEVBQUVDLENBQUMsQ0FBQztJQUNwQixPQUFRcUIsQ0FBQyxDQUFDcEIsT0FBTyxHQUNYb0IsQ0FBQyxHQUNEO01BQ0VwQixPQUFPLEVBQUUsSUFBSTtNQUNiQyxRQUFRLEVBQUVGLENBQUM7TUFDWEYsS0FBSyxFQUFFa0M7SUFDWCxDQUFDO0VBQ1QsQ0FBQztBQUNMO0FBQ0EsU0FBU0MsR0FBR0EsQ0FBQ1QsQ0FBQyxFQUFFO0VBQ1osT0FBTyxDQUFDekIsSUFBSSxFQUFFQyxDQUFDLEtBQUs7SUFDaEIsTUFBTXFCLENBQUMsR0FBR0csQ0FBQyxDQUFDekIsSUFBSSxFQUFFQyxDQUFDLENBQUM7SUFDcEIsT0FBUXFCLENBQUMsQ0FBQ3BCLE9BQU8sR0FDWDtNQUFFQSxPQUFPLEVBQUU7SUFBTSxDQUFDLEdBQ2xCO01BQ0VBLE9BQU8sRUFBRSxJQUFJO01BQ2JDLFFBQVEsRUFBRUYsQ0FBQztNQUNYRixLQUFLLEVBQUU7SUFDWCxDQUFDO0VBQ1QsQ0FBQztBQUNMO0FBQ0EsU0FBU29DLE1BQU1BLENBQUMsR0FBR0MsRUFBRSxFQUFFO0VBQ25CLE9BQU8sQ0FBQ3BDLElBQUksRUFBRUMsQ0FBQyxLQUFLO0lBQ2hCLEtBQUssTUFBTXdCLENBQUMsSUFBSVcsRUFBRSxFQUFFO01BQ2hCLE1BQU1DLE1BQU0sR0FBR1osQ0FBQyxDQUFDekIsSUFBSSxFQUFFQyxDQUFDLENBQUM7TUFDekIsSUFBSW9DLE1BQU0sQ0FBQ25DLE9BQU8sRUFBRTtRQUNoQixPQUFPbUMsTUFBTTtNQUNqQjtJQUNKO0lBQ0EsT0FBTztNQUFFbkMsT0FBTyxFQUFFO0lBQU0sQ0FBQztFQUM3QixDQUFDO0FBQ0w7QUFDQSxTQUFTb0MsU0FBU0EsQ0FBQ0MsRUFBRSxFQUFFQyxFQUFFLEVBQUU7RUFDdkIsT0FBTyxDQUFDeEMsSUFBSSxFQUFFQyxDQUFDLEtBQUs7SUFDaEIsTUFBTXdDLEVBQUUsR0FBR0YsRUFBRSxDQUFDdkMsSUFBSSxFQUFFQyxDQUFDLENBQUM7SUFDdEIsT0FBUXdDLEVBQUUsQ0FBQ3ZDLE9BQU8sR0FDWnVDLEVBQUUsR0FDRkQsRUFBRSxDQUFDeEMsSUFBSSxFQUFFQyxDQUFDLENBQUM7RUFDckIsQ0FBQztBQUNMO0FBQ0EsU0FBU3lDLE9BQU9BLENBQUMsR0FBR04sRUFBRSxFQUFFO0VBQ3BCLE9BQU8sQ0FBQ3BDLElBQUksRUFBRUMsQ0FBQyxLQUFLO0lBQ2hCLElBQUkwQyxLQUFLLEdBQUc1QixTQUFTO0lBQ3JCLEtBQUssTUFBTVUsQ0FBQyxJQUFJVyxFQUFFLEVBQUU7TUFDaEIsTUFBTUMsTUFBTSxHQUFHWixDQUFDLENBQUN6QixJQUFJLEVBQUVDLENBQUMsQ0FBQztNQUN6QixJQUFJb0MsTUFBTSxDQUFDbkMsT0FBTyxLQUFLLENBQUN5QyxLQUFLLElBQUlBLEtBQUssQ0FBQ3hDLFFBQVEsR0FBR2tDLE1BQU0sQ0FBQ2xDLFFBQVEsQ0FBQyxFQUFFO1FBQ2hFd0MsS0FBSyxHQUFHTixNQUFNO01BQ2xCO0lBQ0o7SUFDQSxPQUFPTSxLQUFLLElBQUk7TUFBRXpDLE9BQU8sRUFBRTtJQUFNLENBQUM7RUFDdEMsQ0FBQztBQUNMO0FBQ0EsU0FBUzBDLFNBQVNBLENBQUNuQixDQUFDLEVBQ3BCTCxJQUFJLEVBQUU7RUFDRixPQUFPLENBQUNwQixJQUFJLEVBQUVDLENBQUMsS0FBSztJQUNoQixNQUFNNEMsTUFBTSxHQUFHLEVBQUU7SUFDakIsSUFBSUMsT0FBTyxHQUFHLElBQUk7SUFDbEIsR0FBRztNQUNDLE1BQU14QixDQUFDLEdBQUdHLENBQUMsQ0FBQ3pCLElBQUksRUFBRUMsQ0FBQyxDQUFDO01BQ3BCLElBQUlxQixDQUFDLENBQUNwQixPQUFPLElBQUlrQixJQUFJLENBQUNFLENBQUMsQ0FBQ3ZCLEtBQUssRUFBRThDLE1BQU0sQ0FBQzVCLE1BQU0sR0FBRyxDQUFDLEVBQUVqQixJQUFJLEVBQUVDLENBQUMsRUFBRXFCLENBQUMsQ0FBQ25CLFFBQVEsQ0FBQyxFQUFFO1FBQ3BFMEMsTUFBTSxDQUFDRSxJQUFJLENBQUN6QixDQUFDLENBQUN2QixLQUFLLENBQUM7UUFDcEJFLENBQUMsR0FBR3FCLENBQUMsQ0FBQ25CLFFBQVE7TUFDbEIsQ0FBQyxNQUNJO1FBQ0QyQyxPQUFPLEdBQUcsS0FBSztNQUNuQjtJQUNKLENBQUMsUUFBUUEsT0FBTztJQUNoQixPQUFPO01BQ0g1QyxPQUFPLEVBQUUsSUFBSTtNQUNiQyxRQUFRLEVBQUVGLENBQUM7TUFDWEYsS0FBSyxFQUFFOEM7SUFDWCxDQUFDO0VBQ0wsQ0FBQztBQUNMO0FBQ0EsU0FBU0csU0FBU0EsQ0FBQ3ZCLENBQUMsRUFDcEJMLElBQUksRUFBRTtFQUNGLE9BQU93QixTQUFTLENBQUNuQixDQUFDLEVBQUUsQ0FBQzFCLEtBQUssRUFBRWtELENBQUMsRUFBRWpELElBQUksRUFBRUMsQ0FBQyxFQUFFMkIsQ0FBQyxLQUFLLENBQUNSLElBQUksQ0FBQ3JCLEtBQUssRUFBRWtELENBQUMsRUFBRWpELElBQUksRUFBRUMsQ0FBQyxFQUFFMkIsQ0FBQyxDQUFDLENBQUM7QUFDOUU7QUFDQSxTQUFTc0IsVUFBVUEsQ0FBQ0MsTUFBTSxFQUFFQyxLQUFLLEVBQUU7RUFDL0IsT0FBT1IsU0FBUyxDQUFDTyxNQUFNLEVBQUUsQ0FBQ3BELEtBQUssRUFBRWtELENBQUMsRUFBRWpELElBQUksRUFBRUMsQ0FBQyxLQUFLbUQsS0FBSyxDQUFDcEQsSUFBSSxFQUFFQyxDQUFDLENBQUMsQ0FBQ0MsT0FBTyxDQUFDO0FBQzNFO0FBQ0EsU0FBU21ELFVBQVVBLENBQUNGLE1BQU0sRUFBRUMsS0FBSyxFQUFFO0VBQy9CLE9BQU9SLFNBQVMsQ0FBQ08sTUFBTSxFQUFFLENBQUNwRCxLQUFLLEVBQUVrRCxDQUFDLEVBQUVqRCxJQUFJLEVBQUVDLENBQUMsS0FBSyxDQUFDbUQsS0FBSyxDQUFDcEQsSUFBSSxFQUFFQyxDQUFDLENBQUMsQ0FBQ0MsT0FBTyxDQUFDO0FBQzVFO0FBQ0EsU0FBU29ELElBQUlBLENBQUM3QixDQUFDLEVBQUU7RUFDYixPQUFPbUIsU0FBUyxDQUFDbkIsQ0FBQyxFQUFFLE1BQU0sSUFBSSxDQUFDO0FBQ25DO0FBQ0EsU0FBUzhCLEtBQUtBLENBQUM5QixDQUFDLEVBQUU7RUFDZCxPQUFPK0IsRUFBRSxDQUFDL0IsQ0FBQyxFQUFFNkIsSUFBSSxDQUFDN0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQ2dDLElBQUksRUFBRUMsSUFBSSxLQUFLLENBQUNELElBQUksRUFBRSxHQUFHQyxJQUFJLENBQUMsQ0FBQztBQUMxRDtBQUNBLFNBQVNGLEVBQUVBLENBQUNqQixFQUFFLEVBQUVDLEVBQUUsRUFBRW1CLElBQUksRUFBRTtFQUN0QixPQUFPLENBQUMzRCxJQUFJLEVBQUVDLENBQUMsS0FBS3NCLFFBQVEsQ0FBQ2dCLEVBQUUsQ0FBQ3ZDLElBQUksRUFBRUMsQ0FBQyxDQUFDLEVBQUcyRCxFQUFFLElBQUt2QyxRQUFRLENBQUNtQixFQUFFLENBQUN4QyxJQUFJLEVBQUU0RCxFQUFFLENBQUN6RCxRQUFRLENBQUMsRUFBRSxDQUFDMEQsRUFBRSxFQUFFakMsQ0FBQyxLQUFLK0IsSUFBSSxDQUFDQyxFQUFFLENBQUM3RCxLQUFLLEVBQUU4RCxFQUFFLEVBQUU3RCxJQUFJLEVBQUVDLENBQUMsRUFBRTJCLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDakk7QUFDQSxTQUFTa0MsSUFBSUEsQ0FBQ3ZCLEVBQUUsRUFBRUMsRUFBRSxFQUFFO0VBQ2xCLE9BQU9nQixFQUFFLENBQUNqQixFQUFFLEVBQUVDLEVBQUUsRUFBR3VCLEVBQUUsSUFBS0EsRUFBRSxDQUFDO0FBQ2pDO0FBQ0EsU0FBU0MsS0FBS0EsQ0FBQ3pCLEVBQUUsRUFBRUMsRUFBRSxFQUFFO0VBQ25CLE9BQU9nQixFQUFFLENBQUNqQixFQUFFLEVBQUVDLEVBQUUsRUFBRSxDQUFDdUIsRUFBRSxFQUFFRixFQUFFLEtBQUtBLEVBQUUsQ0FBQztBQUNyQztBQUNBLFNBQVNJLEdBQUdBLENBQUMxQixFQUFFLEVBQUVDLEVBQUUsRUFBRTBCLEVBQUUsRUFBRVAsSUFBSSxFQUFFO0VBQzNCLE9BQU8sQ0FBQzNELElBQUksRUFBRUMsQ0FBQyxLQUFLc0IsUUFBUSxDQUFDZ0IsRUFBRSxDQUFDdkMsSUFBSSxFQUFFQyxDQUFDLENBQUMsRUFBRzJELEVBQUUsSUFBS3JDLFFBQVEsQ0FBQ2lCLEVBQUUsQ0FBQ3hDLElBQUksRUFBRTRELEVBQUUsQ0FBQ3pELFFBQVEsQ0FBQyxFQUFHZ0UsRUFBRSxJQUFLOUMsUUFBUSxDQUFDNkMsRUFBRSxDQUFDbEUsSUFBSSxFQUFFbUUsRUFBRSxDQUFDaEUsUUFBUSxDQUFDLEVBQUUsQ0FBQ2lFLEVBQUUsRUFBRXhDLENBQUMsS0FBSytCLElBQUksQ0FBQ0MsRUFBRSxDQUFDN0QsS0FBSyxFQUFFb0UsRUFBRSxDQUFDcEUsS0FBSyxFQUFFcUUsRUFBRSxFQUFFcEUsSUFBSSxFQUFFQyxDQUFDLEVBQUUyQixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDcEw7QUFDQSxTQUFTeUMsTUFBTUEsQ0FBQzlCLEVBQUUsRUFBRUMsRUFBRSxFQUFFMEIsRUFBRSxFQUFFO0VBQ3hCLE9BQU9ELEdBQUcsQ0FBQzFCLEVBQUUsRUFBRUMsRUFBRSxFQUFFMEIsRUFBRSxFQUFFLENBQUNJLEVBQUUsRUFBRUMsRUFBRSxLQUFLQSxFQUFFLENBQUM7QUFDMUM7QUFDQSxTQUFTQyxHQUFHQSxDQUFDLEdBQUdwQyxFQUFFLEVBQUU7RUFDaEIsT0FBTyxDQUFDcEMsSUFBSSxFQUFFQyxDQUFDLEtBQUs7SUFDaEIsTUFBTW9DLE1BQU0sR0FBRyxFQUFFO0lBQ2pCLElBQUlsQyxRQUFRLEdBQUdGLENBQUM7SUFDaEIsS0FBSyxNQUFNd0IsQ0FBQyxJQUFJVyxFQUFFLEVBQUU7TUFDaEIsTUFBTUssRUFBRSxHQUFHaEIsQ0FBQyxDQUFDekIsSUFBSSxFQUFFRyxRQUFRLENBQUM7TUFDNUIsSUFBSXNDLEVBQUUsQ0FBQ3ZDLE9BQU8sRUFBRTtRQUNabUMsTUFBTSxDQUFDVSxJQUFJLENBQUNOLEVBQUUsQ0FBQzFDLEtBQUssQ0FBQztRQUNyQkksUUFBUSxHQUFHc0MsRUFBRSxDQUFDdEMsUUFBUTtNQUMxQixDQUFDLE1BQ0k7UUFDRCxPQUFPO1VBQUVELE9BQU8sRUFBRTtRQUFNLENBQUM7TUFDN0I7SUFDSjtJQUNBLE9BQU87TUFDSEEsT0FBTyxFQUFFLElBQUk7TUFDYkMsUUFBUSxFQUFFQSxRQUFRO01BQ2xCSixLQUFLLEVBQUVzQztJQUNYLENBQUM7RUFDTCxDQUFDO0FBQ0w7QUFDQSxTQUFTb0MsSUFBSUEsQ0FBQyxHQUFHckMsRUFBRSxFQUFFO0VBQ2pCLE9BQU9aLEdBQUcsQ0FBQ2dELEdBQUcsQ0FBQyxHQUFHcEMsRUFBRSxDQUFDLEVBQUUsTUFBTSxJQUFJLENBQUM7QUFDdEM7QUFDQSxTQUFTc0MsT0FBT0EsQ0FBQyxHQUFHdEMsRUFBRSxFQUFFO0VBQ3BCLE9BQU91QyxRQUFRLENBQUNILEdBQUcsQ0FBQyxHQUFHcEMsRUFBRSxDQUFDLENBQUM7QUFDL0I7QUFDQSxTQUFTdUMsUUFBUUEsQ0FBQ2xELENBQUMsRUFBRTtFQUNqQixPQUFPRCxHQUFHLENBQUNDLENBQUMsRUFBR21ELEVBQUUsSUFBS0EsRUFBRSxDQUFDQyxPQUFPLENBQUVsRCxDQUFDLElBQUtBLENBQUMsQ0FBQyxDQUFDO0FBQy9DO0FBQ0EsU0FBU21ELE1BQU1BLENBQUMzQixNQUFNLEVBQUU0QixJQUFJLEVBQUU7RUFDMUIsT0FBT3ZCLEVBQUUsQ0FBQ0wsTUFBTSxFQUFFRyxJQUFJLENBQUNVLEtBQUssQ0FBQ2UsSUFBSSxFQUFFNUIsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDTSxJQUFJLEVBQUVDLElBQUksS0FBSyxDQUFDRCxJQUFJLEVBQUUsR0FBR0MsSUFBSSxDQUFDLENBQUM7QUFDakY7QUFDQSxTQUFTc0IsS0FBS0EsQ0FBQzdCLE1BQU0sRUFBRTRCLElBQUksRUFBRTtFQUN6QixPQUFPekMsU0FBUyxDQUFDd0MsTUFBTSxDQUFDM0IsTUFBTSxFQUFFNEIsSUFBSSxDQUFDLEVBQUVqRixJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7QUFDcEQ7QUFDQSxTQUFTbUYsV0FBV0EsQ0FBQ0MsR0FBRyxFQUN4QjdFLENBQUMsRUFBRTtFQUNDLE9BQU8sQ0FBQ0wsSUFBSSxFQUFFQyxDQUFDLEtBQUs7SUFDaEIsSUFBSWtGLElBQUksR0FBRyxJQUFJO0lBQ2YsSUFBSUMsSUFBSSxHQUFHRixHQUFHO0lBQ2QsSUFBSUcsR0FBRyxHQUFHcEYsQ0FBQztJQUNYLEdBQUc7TUFDQyxNQUFNcUIsQ0FBQyxHQUFHakIsQ0FBQyxDQUFDK0UsSUFBSSxFQUFFcEYsSUFBSSxFQUFFcUYsR0FBRyxDQUFDLENBQUNyRixJQUFJLEVBQUVxRixHQUFHLENBQUM7TUFDdkMsSUFBSS9ELENBQUMsQ0FBQ3BCLE9BQU8sRUFBRTtRQUNYa0YsSUFBSSxHQUFHOUQsQ0FBQyxDQUFDdkIsS0FBSztRQUNkc0YsR0FBRyxHQUFHL0QsQ0FBQyxDQUFDbkIsUUFBUTtNQUNwQixDQUFDLE1BQ0k7UUFDRGdGLElBQUksR0FBRyxLQUFLO01BQ2hCO0lBQ0osQ0FBQyxRQUFRQSxJQUFJO0lBQ2IsT0FBTztNQUNIakYsT0FBTyxFQUFFLElBQUk7TUFDYkMsUUFBUSxFQUFFa0YsR0FBRztNQUNidEYsS0FBSyxFQUFFcUY7SUFDWCxDQUFDO0VBQ0wsQ0FBQztBQUNMO0FBQ0EsU0FBU0UsVUFBVUEsQ0FBQ0osR0FBRyxFQUFFekQsQ0FBQyxFQUMxQjhELE9BQU8sRUFBRTtFQUNMLE9BQU9OLFdBQVcsQ0FBQ0MsR0FBRyxFQUFHQSxHQUFHLElBQUsxRCxHQUFHLENBQUNDLENBQUMsRUFBRSxDQUFDRSxDQUFDLEVBQUUzQixJQUFJLEVBQUVDLENBQUMsRUFBRTJCLENBQUMsS0FBSzJELE9BQU8sQ0FBQ0wsR0FBRyxFQUFFdkQsQ0FBQyxFQUFFM0IsSUFBSSxFQUFFQyxDQUFDLEVBQUUyQixDQUFDLENBQUMsQ0FBQyxDQUFDO0FBQzVGO0FBQ0EsU0FBUzRELFdBQVdBLENBQUMvRCxDQUFDLEVBQUV5RCxHQUFHLEVBQzNCSyxPQUFPLEVBQUU7RUFDTCxPQUFPL0QsR0FBRyxDQUFDOEIsSUFBSSxDQUFDN0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQ21ELEVBQUUsRUFBRTVFLElBQUksRUFBRUMsQ0FBQyxFQUFFMkIsQ0FBQyxLQUFLZ0QsRUFBRSxDQUFDWSxXQUFXLENBQUMsQ0FBQ04sR0FBRyxFQUFFdkQsQ0FBQyxLQUFLNEQsT0FBTyxDQUFDNUQsQ0FBQyxFQUFFdUQsR0FBRyxFQUFFbEYsSUFBSSxFQUFFQyxDQUFDLEVBQUUyQixDQUFDLENBQUMsRUFBRXNELEdBQUcsQ0FBQyxDQUFDO0FBQ3pHO0FBQ0EsU0FBU08sVUFBVUEsQ0FBQ0MsS0FBSyxFQUFFQyxLQUFLLEVBQUU7RUFDOUIsT0FBT0MsS0FBSyxDQUFDRixLQUFLLEVBQUdHLEVBQUUsSUFBS1AsVUFBVSxDQUFDTyxFQUFFLEVBQUVGLEtBQUssRUFBRSxDQUFDVCxHQUFHLEVBQUU3RSxDQUFDLEtBQUtBLENBQUMsQ0FBQzZFLEdBQUcsQ0FBQyxDQUFDLENBQUM7QUFDMUU7QUFDQSxTQUFTWSxXQUFXQSxDQUFDSCxLQUFLLEVBQUVJLE1BQU0sRUFBRTtFQUNoQyxPQUFPdkMsRUFBRSxDQUFDZ0MsV0FBVyxDQUFDRyxLQUFLLEVBQUdLLENBQUMsSUFBS0EsQ0FBQyxFQUFFLENBQUMzRixDQUFDLEVBQUU2RSxHQUFHLEtBQU1jLENBQUMsSUFBSzNGLENBQUMsQ0FBQzZFLEdBQUcsQ0FBQ2MsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFRCxNQUFNLEVBQUUsQ0FBQzFGLENBQUMsRUFBRXNCLENBQUMsS0FBS3RCLENBQUMsQ0FBQ3NCLENBQUMsQ0FBQyxDQUFDO0FBQ2pHO0FBQ0EsU0FBU3NFLFVBQVVBLENBQUNQLEtBQUssRUFBRUMsS0FBSyxFQUFFSSxNQUFNLEVBQUU7RUFDdEMsT0FBT0gsS0FBSyxDQUFDRixLQUFLLEVBQUdHLEVBQUUsSUFBS1AsVUFBVSxDQUFDTyxFQUFFLEVBQUVyQyxFQUFFLENBQUNtQyxLQUFLLEVBQUVJLE1BQU0sRUFBRSxDQUFDMUYsQ0FBQyxFQUFFMkYsQ0FBQyxLQUFLLENBQUMzRixDQUFDLEVBQUUyRixDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUNkLEdBQUcsRUFBRSxDQUFDN0UsQ0FBQyxFQUFFMkYsQ0FBQyxDQUFDLEtBQUszRixDQUFDLENBQUM2RSxHQUFHLEVBQUVjLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDaEg7QUFDQSxTQUFTRSxXQUFXQSxDQUFDUixLQUFLLEVBQUVDLEtBQUssRUFBRUksTUFBTSxFQUFFO0VBQ3ZDLE9BQU92QyxFQUFFLENBQUNnQyxXQUFXLENBQUNoQyxFQUFFLENBQUNrQyxLQUFLLEVBQUVDLEtBQUssRUFBRSxDQUFDUSxDQUFDLEVBQUU5RixDQUFDLEtBQUssQ0FBQzhGLENBQUMsRUFBRTlGLENBQUMsQ0FBQyxDQUFDLEVBQUcyRixDQUFDLElBQUtBLENBQUMsRUFBRSxDQUFDLENBQUNHLENBQUMsRUFBRTlGLENBQUMsQ0FBQyxFQUFFNkUsR0FBRyxLQUFNYyxDQUFDLElBQUszRixDQUFDLENBQUM4RixDQUFDLEVBQUVqQixHQUFHLENBQUNjLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRUQsTUFBTSxFQUFFLENBQUMxRixDQUFDLEVBQUVzQixDQUFDLEtBQUt0QixDQUFDLENBQUNzQixDQUFDLENBQUMsQ0FBQztBQUN0STtBQUNBLFNBQVN5RSxTQUFTQSxDQUFDQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsTUFBTSxFQUFFO0VBQ3BDLE9BQU8sQ0FBQ3ZHLElBQUksRUFBRUMsQ0FBQyxLQUFNb0csSUFBSSxDQUFDckcsSUFBSSxFQUFFQyxDQUFDLENBQUMsR0FDNUJxRyxLQUFLLENBQUN0RyxJQUFJLEVBQUVDLENBQUMsQ0FBQyxHQUNkc0csTUFBTSxDQUFDdkcsSUFBSSxFQUFFQyxDQUFDLENBQUM7QUFDekI7QUFDQSxTQUFTdUcsTUFBTUEsQ0FBQy9FLENBQUMsRUFBRTtFQUNmLE9BQU8sQ0FBQ3pCLElBQUksRUFBRUMsQ0FBQyxLQUFLc0IsUUFBUSxDQUFDRSxDQUFDLENBQUN6QixJQUFJLEVBQUVDLENBQUMsQ0FBQyxFQUFHd0csRUFBRSxJQUFLQSxFQUFFLENBQUMxRyxLQUFLLENBQUNDLElBQUksRUFBRXlHLEVBQUUsQ0FBQ3RHLFFBQVEsQ0FBQyxDQUFDO0FBQ2pGO0FBQ0EsU0FBU3lGLEtBQUtBLENBQUNuRSxDQUFDLEVBQ2hCcEIsQ0FBQyxFQUFFO0VBQ0MsT0FBTyxDQUFDTCxJQUFJLEVBQUVDLENBQUMsS0FBS3NCLFFBQVEsQ0FBQ0UsQ0FBQyxDQUFDekIsSUFBSSxFQUFFQyxDQUFDLENBQUMsRUFBR3dHLEVBQUUsSUFBS3BHLENBQUMsQ0FBQ29HLEVBQUUsQ0FBQzFHLEtBQUssRUFBRUMsSUFBSSxFQUFFQyxDQUFDLEVBQUV3RyxFQUFFLENBQUN0RyxRQUFRLENBQUMsQ0FBQ0gsSUFBSSxFQUFFeUcsRUFBRSxDQUFDdEcsUUFBUSxDQUFDLENBQUM7QUFDMUc7QUFDQSxTQUFTdUcsS0FBS0EsQ0FBQ2pGLENBQUMsRUFBRTtFQUNkLE9BQU8sQ0FBQ3pCLElBQUksRUFBRUMsQ0FBQyxLQUFLc0IsUUFBUSxDQUFDRSxDQUFDLENBQUN6QixJQUFJLEVBQUVDLENBQUMsQ0FBQyxFQUFHd0csRUFBRSxLQUFNO0lBQzlDdkcsT0FBTyxFQUFFLElBQUk7SUFDYkMsUUFBUSxFQUFFRixDQUFDO0lBQ1hGLEtBQUssRUFBRTBHLEVBQUUsQ0FBQzFHO0VBQ2QsQ0FBQyxDQUFDLENBQUM7QUFDUDtBQUNBLFNBQVM0RyxTQUFTQSxDQUFDdEcsQ0FBQyxFQUFFO0VBQ2xCLE9BQU8sVUFBVUwsSUFBSSxFQUFFQyxDQUFDLEVBQUU7SUFDdEIsT0FBT0ksQ0FBQyxDQUFDLENBQUMsQ0FBQ0wsSUFBSSxFQUFFQyxDQUFDLENBQUM7RUFDdkIsQ0FBQztBQUNMO0FBQ0EsU0FBUzJHLEtBQUtBLENBQUM1RyxJQUFJLEVBQUVDLENBQUMsRUFBRTtFQUNwQixPQUFRQSxDQUFDLEtBQUssQ0FBQyxHQUNUO0lBQUVDLE9BQU8sRUFBRTtFQUFNLENBQUMsR0FDbEI7SUFDRUEsT0FBTyxFQUFFLElBQUk7SUFDYkMsUUFBUSxFQUFFRixDQUFDO0lBQ1hGLEtBQUssRUFBRTtFQUNYLENBQUM7QUFDVDtBQUNBLFNBQVM4RyxHQUFHQSxDQUFDN0csSUFBSSxFQUFFQyxDQUFDLEVBQUU7RUFDbEIsT0FBUUEsQ0FBQyxHQUFHRCxJQUFJLENBQUNnQixNQUFNLENBQUNDLE1BQU0sR0FDeEI7SUFBRWYsT0FBTyxFQUFFO0VBQU0sQ0FBQyxHQUNsQjtJQUNFQSxPQUFPLEVBQUUsSUFBSTtJQUNiQyxRQUFRLEVBQUVGLENBQUM7SUFDWEYsS0FBSyxFQUFFO0VBQ1gsQ0FBQztBQUNUO0FBQ0EsU0FBUytHLHFCQUFxQkEsQ0FBQzlHLElBQUksRUFBRUMsQ0FBQyxFQUFFO0VBQ3BDLE9BQU9ELElBQUksQ0FBQ2dCLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHaEIsQ0FBQztBQUNqQztBQUNBLFNBQVM4RyxjQUFjQSxDQUFDL0csSUFBSSxFQUFFQyxDQUFDLEVBQUUrRyxXQUFXLEVBQUVDLGFBQWEsR0FBRyxDQUFDLEVBQUU7RUFDN0QsTUFBTUMsR0FBRyxHQUFHbEgsSUFBSSxDQUFDZ0IsTUFBTSxDQUFDQyxNQUFNO0VBQzlCLE1BQU1rRyxRQUFRLEdBQUd2SCxnREFBSyxDQUFDLENBQUMsRUFBRUssQ0FBQyxHQUFHZ0gsYUFBYSxFQUFFQyxHQUFHLEdBQUdELGFBQWEsQ0FBQztFQUNqRSxNQUFNRyxTQUFTLEdBQUd4SCxnREFBSyxDQUFDcUgsYUFBYSxFQUFFaEgsQ0FBQyxHQUFHLENBQUMsR0FBR2dILGFBQWEsRUFBRUMsR0FBRyxDQUFDO0VBQ2xFLE1BQU1HLFdBQVcsR0FBR3JILElBQUksQ0FBQ2dCLE1BQU0sQ0FBQ3NHLEtBQUssQ0FBQ0gsUUFBUSxFQUFFQyxTQUFTLENBQUM7RUFDMUQsTUFBTUcsS0FBSyxHQUFHLEVBQUU7RUFDaEIsTUFBTUMsVUFBVSxHQUFHQyxNQUFNLENBQUNMLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQ25HLE1BQU0sR0FBRyxDQUFDO0VBQ25ELElBQUloQixDQUFDLEdBQUcsQ0FBQyxFQUFFO0lBQ1BzSCxLQUFLLENBQUN4RSxJQUFJLENBQUUsR0FBRTBFLE1BQU0sQ0FBQ3hILENBQUMsQ0FBQyxDQUFDeUgsUUFBUSxDQUFDRixVQUFVLENBQUUsS0FBSSxDQUFDO0VBQ3REO0VBQ0EsSUFBSSxDQUFDLEdBQUdMLFFBQVEsRUFBRTtJQUNkSSxLQUFLLENBQUN4RSxJQUFJLENBQUMsS0FBSyxDQUFDMkUsUUFBUSxDQUFDRixVQUFVLEdBQUcsQ0FBQyxDQUFDLENBQUM7RUFDOUM7RUFDQSxLQUFLLElBQUk1RixDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUd5RixXQUFXLENBQUNwRyxNQUFNLEVBQUVXLENBQUMsRUFBRSxFQUFFO0lBQ3pDLE1BQU0rRixLQUFLLEdBQUdSLFFBQVEsR0FBR3ZGLENBQUM7SUFDMUIyRixLQUFLLENBQUN4RSxJQUFJLENBQUUsR0FBRTBFLE1BQU0sQ0FBQ0UsS0FBSyxDQUFDLENBQUNELFFBQVEsQ0FBQ0YsVUFBVSxDQUFFLElBQUlHLEtBQUssS0FBSzFILENBQUMsR0FBRyxHQUFHLEdBQUcsR0FBSyxJQUFHSiwyREFBZ0IsQ0FBQ21ILFdBQVcsQ0FBQ0ssV0FBVyxDQUFDekYsQ0FBQyxDQUFDLENBQUMsQ0FBRSxFQUFDLENBQUM7RUFDckk7RUFDQSxJQUFJd0YsU0FBUyxHQUFHRixHQUFHLEVBQUU7SUFDakJLLEtBQUssQ0FBQ3hFLElBQUksQ0FBQyxLQUFLLENBQUMyRSxRQUFRLENBQUNGLFVBQVUsR0FBRyxDQUFDLENBQUMsQ0FBQztFQUM5QztFQUNBLElBQUlOLEdBQUcsSUFBSWpILENBQUMsRUFBRTtJQUNWc0gsS0FBSyxDQUFDeEUsSUFBSSxDQUFFLEdBQUUwRSxNQUFNLENBQUN4SCxDQUFDLENBQUMsQ0FBQ3lILFFBQVEsQ0FBQ0YsVUFBVSxDQUFFLEtBQUksQ0FBQztFQUN0RDtFQUNBLE9BQU9ELEtBQUssQ0FBQzVELElBQUksQ0FBQyxJQUFJLENBQUM7QUFDM0I7QUFDQSxTQUFTaUUsS0FBS0EsQ0FBQ0MsTUFBTSxFQUFFN0csTUFBTSxFQUFFOEcsT0FBTyxFQUFFZCxXQUFXLEdBQUdlLElBQUksQ0FBQ0MsU0FBUyxFQUFFO0VBQ2xFLE1BQU1oSSxJQUFJLEdBQUc7SUFBRWdCLE1BQU0sRUFBRUEsTUFBTTtJQUFFOEcsT0FBTyxFQUFFQTtFQUFRLENBQUM7RUFDakQsTUFBTXpGLE1BQU0sR0FBR3dGLE1BQU0sQ0FBQzdILElBQUksRUFBRSxDQUFDLENBQUM7RUFDOUIsSUFBSSxDQUFDcUMsTUFBTSxDQUFDbkMsT0FBTyxFQUFFO0lBQ2pCLE1BQU0sSUFBSVEsS0FBSyxDQUFDLFVBQVUsQ0FBQztFQUMvQjtFQUNBLElBQUkyQixNQUFNLENBQUNsQyxRQUFRLEdBQUdILElBQUksQ0FBQ2dCLE1BQU0sQ0FBQ0MsTUFBTSxFQUFFO0lBQ3RDLE1BQU0sSUFBSVAsS0FBSyxDQUFFLHVDQUFzQ3FHLGNBQWMsQ0FBQy9HLElBQUksRUFBRXFDLE1BQU0sQ0FBQ2xDLFFBQVEsRUFBRTZHLFdBQVcsQ0FBRSxFQUFDLENBQUM7RUFDaEg7RUFDQSxPQUFPM0UsTUFBTSxDQUFDdEMsS0FBSztBQUN2QjtBQUNBLFNBQVNrSSxRQUFRQSxDQUFDSixNQUFNLEVBQUU3RyxNQUFNLEVBQUU4RyxPQUFPLEVBQUU7RUFDdkMsTUFBTXpGLE1BQU0sR0FBR3dGLE1BQU0sQ0FBQztJQUFFN0csTUFBTSxFQUFFQSxNQUFNO0lBQUU4RyxPQUFPLEVBQUVBO0VBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztFQUM5RCxPQUFRekYsTUFBTSxDQUFDbkMsT0FBTyxHQUNoQm1DLE1BQU0sQ0FBQ3RDLEtBQUssR0FDWmdCLFNBQVM7QUFDbkI7QUFDQSxTQUFTNEIsS0FBS0EsQ0FBQ3VGLE9BQU8sRUFBRWxILE1BQU0sRUFBRThHLE9BQU8sRUFBRTtFQUNyQyxNQUFNekYsTUFBTSxHQUFHNkYsT0FBTyxDQUFDO0lBQUVsSCxNQUFNLEVBQUVBLE1BQU07SUFBRThHLE9BQU8sRUFBRUE7RUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDO0VBQy9ELE9BQU96RixNQUFNLENBQUN0QyxLQUFLO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTNcXG5vZGVfbW9kdWxlc1xccGViZXJtaW50YVxcbGliXFxjb3JlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbGFtcCwgZXNjYXBlV2hpdGVzcGFjZSB9IGZyb20gJy4vdXRpbC5tanMnO1xuXG5mdW5jdGlvbiBlbWl0KHZhbHVlKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiAoe1xuICAgICAgICBtYXRjaGVkOiB0cnVlLFxuICAgICAgICBwb3NpdGlvbjogaSxcbiAgICAgICAgdmFsdWU6IHZhbHVlXG4gICAgfSk7XG59XG5mdW5jdGlvbiBtYWtlKFxuZikge1xuICAgIHJldHVybiAoZGF0YSwgaSkgPT4gKHtcbiAgICAgICAgbWF0Y2hlZDogdHJ1ZSxcbiAgICAgICAgcG9zaXRpb246IGksXG4gICAgICAgIHZhbHVlOiBmKGRhdGEsIGkpXG4gICAgfSk7XG59XG5mdW5jdGlvbiBhY3Rpb24oXG5mKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiB7XG4gICAgICAgIGYoZGF0YSwgaSk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBtYXRjaGVkOiB0cnVlLFxuICAgICAgICAgICAgcG9zaXRpb246IGksXG4gICAgICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgICB9O1xuICAgIH07XG59XG5mdW5jdGlvbiBmYWlsKFxuZGF0YSwgaSkge1xuICAgIHJldHVybiB7IG1hdGNoZWQ6IGZhbHNlIH07XG59XG5mdW5jdGlvbiBlcnJvcihtZXNzYWdlKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcigobWVzc2FnZSBpbnN0YW5jZW9mIEZ1bmN0aW9uKSA/IG1lc3NhZ2UoZGF0YSwgaSkgOiBtZXNzYWdlKTtcbiAgICB9O1xufVxuZnVuY3Rpb24gdG9rZW4oXG5vblRva2VuLFxub25FbmQpIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IHtcbiAgICAgICAgbGV0IHBvc2l0aW9uID0gaTtcbiAgICAgICAgbGV0IHZhbHVlID0gdW5kZWZpbmVkO1xuICAgICAgICBpZiAoaSA8IGRhdGEudG9rZW5zLmxlbmd0aCkge1xuICAgICAgICAgICAgdmFsdWUgPSBvblRva2VuKGRhdGEudG9rZW5zW2ldLCBkYXRhLCBpKTtcbiAgICAgICAgICAgIGlmICh2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgcG9zaXRpb24rKztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIG9uRW5kPy4oZGF0YSwgaSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICh2YWx1ZSA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgPyB7IG1hdGNoZWQ6IGZhbHNlIH1cbiAgICAgICAgICAgIDoge1xuICAgICAgICAgICAgICAgIG1hdGNoZWQ6IHRydWUsXG4gICAgICAgICAgICAgICAgcG9zaXRpb246IHBvc2l0aW9uLFxuICAgICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZVxuICAgICAgICAgICAgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gYW55KGRhdGEsIGkpIHtcbiAgICByZXR1cm4gKGkgPCBkYXRhLnRva2Vucy5sZW5ndGgpXG4gICAgICAgID8ge1xuICAgICAgICAgICAgbWF0Y2hlZDogdHJ1ZSxcbiAgICAgICAgICAgIHBvc2l0aW9uOiBpICsgMSxcbiAgICAgICAgICAgIHZhbHVlOiBkYXRhLnRva2Vuc1tpXVxuICAgICAgICB9XG4gICAgICAgIDogeyBtYXRjaGVkOiBmYWxzZSB9O1xufVxuZnVuY3Rpb24gc2F0aXNmeShcbnRlc3QpIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IChpIDwgZGF0YS50b2tlbnMubGVuZ3RoICYmIHRlc3QoZGF0YS50b2tlbnNbaV0sIGRhdGEsIGkpKVxuICAgICAgICA/IHtcbiAgICAgICAgICAgIG1hdGNoZWQ6IHRydWUsXG4gICAgICAgICAgICBwb3NpdGlvbjogaSArIDEsXG4gICAgICAgICAgICB2YWx1ZTogZGF0YS50b2tlbnNbaV1cbiAgICAgICAgfVxuICAgICAgICA6IHsgbWF0Y2hlZDogZmFsc2UgfTtcbn1cbmZ1bmN0aW9uIG1hcElubmVyKHIsIGYpIHtcbiAgICByZXR1cm4gKHIubWF0Y2hlZCkgPyAoe1xuICAgICAgICBtYXRjaGVkOiB0cnVlLFxuICAgICAgICBwb3NpdGlvbjogci5wb3NpdGlvbixcbiAgICAgICAgdmFsdWU6IGYoci52YWx1ZSwgci5wb3NpdGlvbilcbiAgICB9KSA6IHI7XG59XG5mdW5jdGlvbiBtYXBPdXRlcihyLCBmKSB7XG4gICAgcmV0dXJuIChyLm1hdGNoZWQpID8gZihyKSA6IHI7XG59XG5mdW5jdGlvbiBtYXAocCwgbWFwcGVyKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiBtYXBJbm5lcihwKGRhdGEsIGkpLCAodiwgaikgPT4gbWFwcGVyKHYsIGRhdGEsIGksIGopKTtcbn1cbmZ1bmN0aW9uIG1hcDEocCxcbm1hcHBlcikge1xuICAgIHJldHVybiAoZGF0YSwgaSkgPT4gbWFwT3V0ZXIocChkYXRhLCBpKSwgKG0pID0+IG1hcHBlcihtLCBkYXRhLCBpKSk7XG59XG5mdW5jdGlvbiBwZWVrKHAsIGYpIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IHtcbiAgICAgICAgY29uc3QgciA9IHAoZGF0YSwgaSk7XG4gICAgICAgIGYociwgZGF0YSwgaSk7XG4gICAgICAgIHJldHVybiByO1xuICAgIH07XG59XG5mdW5jdGlvbiBvcHRpb24ocCwgZGVmKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiB7XG4gICAgICAgIGNvbnN0IHIgPSBwKGRhdGEsIGkpO1xuICAgICAgICByZXR1cm4gKHIubWF0Y2hlZClcbiAgICAgICAgICAgID8gclxuICAgICAgICAgICAgOiB7XG4gICAgICAgICAgICAgICAgbWF0Y2hlZDogdHJ1ZSxcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogaSxcbiAgICAgICAgICAgICAgICB2YWx1ZTogZGVmXG4gICAgICAgICAgICB9O1xuICAgIH07XG59XG5mdW5jdGlvbiBub3QocCkge1xuICAgIHJldHVybiAoZGF0YSwgaSkgPT4ge1xuICAgICAgICBjb25zdCByID0gcChkYXRhLCBpKTtcbiAgICAgICAgcmV0dXJuIChyLm1hdGNoZWQpXG4gICAgICAgICAgICA/IHsgbWF0Y2hlZDogZmFsc2UgfVxuICAgICAgICAgICAgOiB7XG4gICAgICAgICAgICAgICAgbWF0Y2hlZDogdHJ1ZSxcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogaSxcbiAgICAgICAgICAgICAgICB2YWx1ZTogdHJ1ZVxuICAgICAgICAgICAgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gY2hvaWNlKC4uLnBzKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgcCBvZiBwcykge1xuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gcChkYXRhLCBpKTtcbiAgICAgICAgICAgIGlmIChyZXN1bHQubWF0Y2hlZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHsgbWF0Y2hlZDogZmFsc2UgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gb3RoZXJ3aXNlKHBhLCBwYikge1xuICAgIHJldHVybiAoZGF0YSwgaSkgPT4ge1xuICAgICAgICBjb25zdCByMSA9IHBhKGRhdGEsIGkpO1xuICAgICAgICByZXR1cm4gKHIxLm1hdGNoZWQpXG4gICAgICAgICAgICA/IHIxXG4gICAgICAgICAgICA6IHBiKGRhdGEsIGkpO1xuICAgIH07XG59XG5mdW5jdGlvbiBsb25nZXN0KC4uLnBzKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiB7XG4gICAgICAgIGxldCBtYXRjaCA9IHVuZGVmaW5lZDtcbiAgICAgICAgZm9yIChjb25zdCBwIG9mIHBzKSB7XG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBwKGRhdGEsIGkpO1xuICAgICAgICAgICAgaWYgKHJlc3VsdC5tYXRjaGVkICYmICghbWF0Y2ggfHwgbWF0Y2gucG9zaXRpb24gPCByZXN1bHQucG9zaXRpb24pKSB7XG4gICAgICAgICAgICAgICAgbWF0Y2ggPSByZXN1bHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG1hdGNoIHx8IHsgbWF0Y2hlZDogZmFsc2UgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gdGFrZVdoaWxlKHAsXG50ZXN0KSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiB7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IFtdO1xuICAgICAgICBsZXQgc3VjY2VzcyA9IHRydWU7XG4gICAgICAgIGRvIHtcbiAgICAgICAgICAgIGNvbnN0IHIgPSBwKGRhdGEsIGkpO1xuICAgICAgICAgICAgaWYgKHIubWF0Y2hlZCAmJiB0ZXN0KHIudmFsdWUsIHZhbHVlcy5sZW5ndGggKyAxLCBkYXRhLCBpLCByLnBvc2l0aW9uKSkge1xuICAgICAgICAgICAgICAgIHZhbHVlcy5wdXNoKHIudmFsdWUpO1xuICAgICAgICAgICAgICAgIGkgPSByLnBvc2l0aW9uO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgc3VjY2VzcyA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IHdoaWxlIChzdWNjZXNzKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG1hdGNoZWQ6IHRydWUsXG4gICAgICAgICAgICBwb3NpdGlvbjogaSxcbiAgICAgICAgICAgIHZhbHVlOiB2YWx1ZXNcbiAgICAgICAgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gdGFrZVVudGlsKHAsXG50ZXN0KSB7XG4gICAgcmV0dXJuIHRha2VXaGlsZShwLCAodmFsdWUsIG4sIGRhdGEsIGksIGopID0+ICF0ZXN0KHZhbHVlLCBuLCBkYXRhLCBpLCBqKSk7XG59XG5mdW5jdGlvbiB0YWtlV2hpbGVQKHBWYWx1ZSwgcFRlc3QpIHtcbiAgICByZXR1cm4gdGFrZVdoaWxlKHBWYWx1ZSwgKHZhbHVlLCBuLCBkYXRhLCBpKSA9PiBwVGVzdChkYXRhLCBpKS5tYXRjaGVkKTtcbn1cbmZ1bmN0aW9uIHRha2VVbnRpbFAocFZhbHVlLCBwVGVzdCkge1xuICAgIHJldHVybiB0YWtlV2hpbGUocFZhbHVlLCAodmFsdWUsIG4sIGRhdGEsIGkpID0+ICFwVGVzdChkYXRhLCBpKS5tYXRjaGVkKTtcbn1cbmZ1bmN0aW9uIG1hbnkocCkge1xuICAgIHJldHVybiB0YWtlV2hpbGUocCwgKCkgPT4gdHJ1ZSk7XG59XG5mdW5jdGlvbiBtYW55MShwKSB7XG4gICAgcmV0dXJuIGFiKHAsIG1hbnkocCksIChoZWFkLCB0YWlsKSA9PiBbaGVhZCwgLi4udGFpbF0pO1xufVxuZnVuY3Rpb24gYWIocGEsIHBiLCBqb2luKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiBtYXBPdXRlcihwYShkYXRhLCBpKSwgKG1hKSA9PiBtYXBJbm5lcihwYihkYXRhLCBtYS5wb3NpdGlvbiksICh2YiwgaikgPT4gam9pbihtYS52YWx1ZSwgdmIsIGRhdGEsIGksIGopKSk7XG59XG5mdW5jdGlvbiBsZWZ0KHBhLCBwYikge1xuICAgIHJldHVybiBhYihwYSwgcGIsICh2YSkgPT4gdmEpO1xufVxuZnVuY3Rpb24gcmlnaHQocGEsIHBiKSB7XG4gICAgcmV0dXJuIGFiKHBhLCBwYiwgKHZhLCB2YikgPT4gdmIpO1xufVxuZnVuY3Rpb24gYWJjKHBhLCBwYiwgcGMsIGpvaW4pIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IG1hcE91dGVyKHBhKGRhdGEsIGkpLCAobWEpID0+IG1hcE91dGVyKHBiKGRhdGEsIG1hLnBvc2l0aW9uKSwgKG1iKSA9PiBtYXBJbm5lcihwYyhkYXRhLCBtYi5wb3NpdGlvbiksICh2YywgaikgPT4gam9pbihtYS52YWx1ZSwgbWIudmFsdWUsIHZjLCBkYXRhLCBpLCBqKSkpKTtcbn1cbmZ1bmN0aW9uIG1pZGRsZShwYSwgcGIsIHBjKSB7XG4gICAgcmV0dXJuIGFiYyhwYSwgcGIsIHBjLCAocmEsIHJiKSA9PiByYik7XG59XG5mdW5jdGlvbiBhbGwoLi4ucHMpIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gW107XG4gICAgICAgIGxldCBwb3NpdGlvbiA9IGk7XG4gICAgICAgIGZvciAoY29uc3QgcCBvZiBwcykge1xuICAgICAgICAgICAgY29uc3QgcjEgPSBwKGRhdGEsIHBvc2l0aW9uKTtcbiAgICAgICAgICAgIGlmIChyMS5tYXRjaGVkKSB7XG4gICAgICAgICAgICAgICAgcmVzdWx0LnB1c2gocjEudmFsdWUpO1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uID0gcjEucG9zaXRpb247XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXR1cm4geyBtYXRjaGVkOiBmYWxzZSB9O1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBtYXRjaGVkOiB0cnVlLFxuICAgICAgICAgICAgcG9zaXRpb246IHBvc2l0aW9uLFxuICAgICAgICAgICAgdmFsdWU6IHJlc3VsdFxuICAgICAgICB9O1xuICAgIH07XG59XG5mdW5jdGlvbiBza2lwKC4uLnBzKSB7XG4gICAgcmV0dXJuIG1hcChhbGwoLi4ucHMpLCAoKSA9PiBudWxsKTtcbn1cbmZ1bmN0aW9uIGZsYXR0ZW4oLi4ucHMpIHtcbiAgICByZXR1cm4gZmxhdHRlbjEoYWxsKC4uLnBzKSk7XG59XG5mdW5jdGlvbiBmbGF0dGVuMShwKSB7XG4gICAgcmV0dXJuIG1hcChwLCAodnMpID0+IHZzLmZsYXRNYXAoKHYpID0+IHYpKTtcbn1cbmZ1bmN0aW9uIHNlcEJ5MShwVmFsdWUsIHBTZXApIHtcbiAgICByZXR1cm4gYWIocFZhbHVlLCBtYW55KHJpZ2h0KHBTZXAsIHBWYWx1ZSkpLCAoaGVhZCwgdGFpbCkgPT4gW2hlYWQsIC4uLnRhaWxdKTtcbn1cbmZ1bmN0aW9uIHNlcEJ5KHBWYWx1ZSwgcFNlcCkge1xuICAgIHJldHVybiBvdGhlcndpc2Uoc2VwQnkxKHBWYWx1ZSwgcFNlcCksIGVtaXQoW10pKTtcbn1cbmZ1bmN0aW9uIGNoYWluUmVkdWNlKGFjYyxcbmYpIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IHtcbiAgICAgICAgbGV0IGxvb3AgPSB0cnVlO1xuICAgICAgICBsZXQgYWNjMSA9IGFjYztcbiAgICAgICAgbGV0IHBvcyA9IGk7XG4gICAgICAgIGRvIHtcbiAgICAgICAgICAgIGNvbnN0IHIgPSBmKGFjYzEsIGRhdGEsIHBvcykoZGF0YSwgcG9zKTtcbiAgICAgICAgICAgIGlmIChyLm1hdGNoZWQpIHtcbiAgICAgICAgICAgICAgICBhY2MxID0gci52YWx1ZTtcbiAgICAgICAgICAgICAgICBwb3MgPSByLnBvc2l0aW9uO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgbG9vcCA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IHdoaWxlIChsb29wKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG1hdGNoZWQ6IHRydWUsXG4gICAgICAgICAgICBwb3NpdGlvbjogcG9zLFxuICAgICAgICAgICAgdmFsdWU6IGFjYzFcbiAgICAgICAgfTtcbiAgICB9O1xufVxuZnVuY3Rpb24gcmVkdWNlTGVmdChhY2MsIHAsXG5yZWR1Y2VyKSB7XG4gICAgcmV0dXJuIGNoYWluUmVkdWNlKGFjYywgKGFjYykgPT4gbWFwKHAsICh2LCBkYXRhLCBpLCBqKSA9PiByZWR1Y2VyKGFjYywgdiwgZGF0YSwgaSwgaikpKTtcbn1cbmZ1bmN0aW9uIHJlZHVjZVJpZ2h0KHAsIGFjYyxcbnJlZHVjZXIpIHtcbiAgICByZXR1cm4gbWFwKG1hbnkocCksICh2cywgZGF0YSwgaSwgaikgPT4gdnMucmVkdWNlUmlnaHQoKGFjYywgdikgPT4gcmVkdWNlcih2LCBhY2MsIGRhdGEsIGksIGopLCBhY2MpKTtcbn1cbmZ1bmN0aW9uIGxlZnRBc3NvYzEocExlZnQsIHBPcGVyKSB7XG4gICAgcmV0dXJuIGNoYWluKHBMZWZ0LCAodjApID0+IHJlZHVjZUxlZnQodjAsIHBPcGVyLCAoYWNjLCBmKSA9PiBmKGFjYykpKTtcbn1cbmZ1bmN0aW9uIHJpZ2h0QXNzb2MxKHBPcGVyLCBwUmlnaHQpIHtcbiAgICByZXR1cm4gYWIocmVkdWNlUmlnaHQocE9wZXIsICh5KSA9PiB5LCAoZiwgYWNjKSA9PiAoeSkgPT4gZihhY2MoeSkpKSwgcFJpZ2h0LCAoZiwgdikgPT4gZih2KSk7XG59XG5mdW5jdGlvbiBsZWZ0QXNzb2MyKHBMZWZ0LCBwT3BlciwgcFJpZ2h0KSB7XG4gICAgcmV0dXJuIGNoYWluKHBMZWZ0LCAodjApID0+IHJlZHVjZUxlZnQodjAsIGFiKHBPcGVyLCBwUmlnaHQsIChmLCB5KSA9PiBbZiwgeV0pLCAoYWNjLCBbZiwgeV0pID0+IGYoYWNjLCB5KSkpO1xufVxuZnVuY3Rpb24gcmlnaHRBc3NvYzIocExlZnQsIHBPcGVyLCBwUmlnaHQpIHtcbiAgICByZXR1cm4gYWIocmVkdWNlUmlnaHQoYWIocExlZnQsIHBPcGVyLCAoeCwgZikgPT4gW3gsIGZdKSwgKHkpID0+IHksIChbeCwgZl0sIGFjYykgPT4gKHkpID0+IGYoeCwgYWNjKHkpKSksIHBSaWdodCwgKGYsIHYpID0+IGYodikpO1xufVxuZnVuY3Rpb24gY29uZGl0aW9uKGNvbmQsIHBUcnVlLCBwRmFsc2UpIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IChjb25kKGRhdGEsIGkpKVxuICAgICAgICA/IHBUcnVlKGRhdGEsIGkpXG4gICAgICAgIDogcEZhbHNlKGRhdGEsIGkpO1xufVxuZnVuY3Rpb24gZGVjaWRlKHApIHtcbiAgICByZXR1cm4gKGRhdGEsIGkpID0+IG1hcE91dGVyKHAoZGF0YSwgaSksIChtMSkgPT4gbTEudmFsdWUoZGF0YSwgbTEucG9zaXRpb24pKTtcbn1cbmZ1bmN0aW9uIGNoYWluKHAsXG5mKSB7XG4gICAgcmV0dXJuIChkYXRhLCBpKSA9PiBtYXBPdXRlcihwKGRhdGEsIGkpLCAobTEpID0+IGYobTEudmFsdWUsIGRhdGEsIGksIG0xLnBvc2l0aW9uKShkYXRhLCBtMS5wb3NpdGlvbikpO1xufVxuZnVuY3Rpb24gYWhlYWQocCkge1xuICAgIHJldHVybiAoZGF0YSwgaSkgPT4gbWFwT3V0ZXIocChkYXRhLCBpKSwgKG0xKSA9PiAoe1xuICAgICAgICBtYXRjaGVkOiB0cnVlLFxuICAgICAgICBwb3NpdGlvbjogaSxcbiAgICAgICAgdmFsdWU6IG0xLnZhbHVlXG4gICAgfSkpO1xufVxuZnVuY3Rpb24gcmVjdXJzaXZlKGYpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKGRhdGEsIGkpIHtcbiAgICAgICAgcmV0dXJuIGYoKShkYXRhLCBpKTtcbiAgICB9O1xufVxuZnVuY3Rpb24gc3RhcnQoZGF0YSwgaSkge1xuICAgIHJldHVybiAoaSAhPT0gMClcbiAgICAgICAgPyB7IG1hdGNoZWQ6IGZhbHNlIH1cbiAgICAgICAgOiB7XG4gICAgICAgICAgICBtYXRjaGVkOiB0cnVlLFxuICAgICAgICAgICAgcG9zaXRpb246IGksXG4gICAgICAgICAgICB2YWx1ZTogdHJ1ZVxuICAgICAgICB9O1xufVxuZnVuY3Rpb24gZW5kKGRhdGEsIGkpIHtcbiAgICByZXR1cm4gKGkgPCBkYXRhLnRva2Vucy5sZW5ndGgpXG4gICAgICAgID8geyBtYXRjaGVkOiBmYWxzZSB9XG4gICAgICAgIDoge1xuICAgICAgICAgICAgbWF0Y2hlZDogdHJ1ZSxcbiAgICAgICAgICAgIHBvc2l0aW9uOiBpLFxuICAgICAgICAgICAgdmFsdWU6IHRydWVcbiAgICAgICAgfTtcbn1cbmZ1bmN0aW9uIHJlbWFpbmluZ1Rva2Vuc051bWJlcihkYXRhLCBpKSB7XG4gICAgcmV0dXJuIGRhdGEudG9rZW5zLmxlbmd0aCAtIGk7XG59XG5mdW5jdGlvbiBwYXJzZXJQb3NpdGlvbihkYXRhLCBpLCBmb3JtYXRUb2tlbiwgY29udGV4dFRva2VucyA9IDMpIHtcbiAgICBjb25zdCBsZW4gPSBkYXRhLnRva2Vucy5sZW5ndGg7XG4gICAgY29uc3QgbG93SW5kZXggPSBjbGFtcCgwLCBpIC0gY29udGV4dFRva2VucywgbGVuIC0gY29udGV4dFRva2Vucyk7XG4gICAgY29uc3QgaGlnaEluZGV4ID0gY2xhbXAoY29udGV4dFRva2VucywgaSArIDEgKyBjb250ZXh0VG9rZW5zLCBsZW4pO1xuICAgIGNvbnN0IHRva2Vuc1NsaWNlID0gZGF0YS50b2tlbnMuc2xpY2UobG93SW5kZXgsIGhpZ2hJbmRleCk7XG4gICAgY29uc3QgbGluZXMgPSBbXTtcbiAgICBjb25zdCBpbmRleFdpZHRoID0gU3RyaW5nKGhpZ2hJbmRleCAtIDEpLmxlbmd0aCArIDE7XG4gICAgaWYgKGkgPCAwKSB7XG4gICAgICAgIGxpbmVzLnB1c2goYCR7U3RyaW5nKGkpLnBhZFN0YXJ0KGluZGV4V2lkdGgpfSA+PmApO1xuICAgIH1cbiAgICBpZiAoMCA8IGxvd0luZGV4KSB7XG4gICAgICAgIGxpbmVzLnB1c2goJy4uLicucGFkU3RhcnQoaW5kZXhXaWR0aCArIDYpKTtcbiAgICB9XG4gICAgZm9yIChsZXQgaiA9IDA7IGogPCB0b2tlbnNTbGljZS5sZW5ndGg7IGorKykge1xuICAgICAgICBjb25zdCBpbmRleCA9IGxvd0luZGV4ICsgajtcbiAgICAgICAgbGluZXMucHVzaChgJHtTdHJpbmcoaW5kZXgpLnBhZFN0YXJ0KGluZGV4V2lkdGgpfSAkeyhpbmRleCA9PT0gaSA/ICc+JyA6ICcgJyl9ICR7ZXNjYXBlV2hpdGVzcGFjZShmb3JtYXRUb2tlbih0b2tlbnNTbGljZVtqXSkpfWApO1xuICAgIH1cbiAgICBpZiAoaGlnaEluZGV4IDwgbGVuKSB7XG4gICAgICAgIGxpbmVzLnB1c2goJy4uLicucGFkU3RhcnQoaW5kZXhXaWR0aCArIDYpKTtcbiAgICB9XG4gICAgaWYgKGxlbiA8PSBpKSB7XG4gICAgICAgIGxpbmVzLnB1c2goYCR7U3RyaW5nKGkpLnBhZFN0YXJ0KGluZGV4V2lkdGgpfSA+PmApO1xuICAgIH1cbiAgICByZXR1cm4gbGluZXMuam9pbignXFxuJyk7XG59XG5mdW5jdGlvbiBwYXJzZShwYXJzZXIsIHRva2Vucywgb3B0aW9ucywgZm9ybWF0VG9rZW4gPSBKU09OLnN0cmluZ2lmeSkge1xuICAgIGNvbnN0IGRhdGEgPSB7IHRva2VuczogdG9rZW5zLCBvcHRpb25zOiBvcHRpb25zIH07XG4gICAgY29uc3QgcmVzdWx0ID0gcGFyc2VyKGRhdGEsIDApO1xuICAgIGlmICghcmVzdWx0Lm1hdGNoZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBtYXRjaCcpO1xuICAgIH1cbiAgICBpZiAocmVzdWx0LnBvc2l0aW9uIDwgZGF0YS50b2tlbnMubGVuZ3RoKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgUGFydGlhbCBtYXRjaC4gUGFyc2luZyBzdG9wcGVkIGF0OlxcbiR7cGFyc2VyUG9zaXRpb24oZGF0YSwgcmVzdWx0LnBvc2l0aW9uLCBmb3JtYXRUb2tlbil9YCk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQudmFsdWU7XG59XG5mdW5jdGlvbiB0cnlQYXJzZShwYXJzZXIsIHRva2Vucywgb3B0aW9ucykge1xuICAgIGNvbnN0IHJlc3VsdCA9IHBhcnNlcih7IHRva2VuczogdG9rZW5zLCBvcHRpb25zOiBvcHRpb25zIH0sIDApO1xuICAgIHJldHVybiAocmVzdWx0Lm1hdGNoZWQpXG4gICAgICAgID8gcmVzdWx0LnZhbHVlXG4gICAgICAgIDogdW5kZWZpbmVkO1xufVxuZnVuY3Rpb24gbWF0Y2gobWF0Y2hlciwgdG9rZW5zLCBvcHRpb25zKSB7XG4gICAgY29uc3QgcmVzdWx0ID0gbWF0Y2hlcih7IHRva2VuczogdG9rZW5zLCBvcHRpb25zOiBvcHRpb25zIH0sIDApO1xuICAgIHJldHVybiByZXN1bHQudmFsdWU7XG59XG5cbmV4cG9ydCB7IGFiLCBhYmMsIGFjdGlvbiwgYWhlYWQsIGFsbCwgYWxsIGFzIGFuZCwgYW55LCBjaGFpbiwgY2hhaW5SZWR1Y2UsIGNob2ljZSwgY29uZGl0aW9uLCBkZWNpZGUsIHNraXAgYXMgZGlzY2FyZCwgb3RoZXJ3aXNlIGFzIGVpdGhlck9yLCBlbWl0LCBlbmQsIGVuZCBhcyBlb2YsIGVycm9yLCBmYWlsLCBmbGF0dGVuLCBmbGF0dGVuMSwgbGVmdCwgbGVmdEFzc29jMSwgbGVmdEFzc29jMiwgbG9uZ2VzdCwgYWhlYWQgYXMgbG9va0FoZWFkLCBtYWtlLCBtYW55LCBtYW55MSwgbWFwLCBtYXAxLCBtYXRjaCwgbWlkZGxlLCBub3QsIGVtaXQgYXMgb2YsIG9wdGlvbiwgY2hvaWNlIGFzIG9yLCBvdGhlcndpc2UsIHBhcnNlLCBwYXJzZXJQb3NpdGlvbiwgcGVlaywgcmVjdXJzaXZlLCByZWR1Y2VMZWZ0LCByZWR1Y2VSaWdodCwgcmVtYWluaW5nVG9rZW5zTnVtYmVyLCByaWdodCwgcmlnaHRBc3NvYzEsIHJpZ2h0QXNzb2MyLCBzYXRpc2Z5LCBzZXBCeSwgc2VwQnkxLCBza2lwLCBtYW55MSBhcyBzb21lLCBzdGFydCwgdGFrZVVudGlsLCB0YWtlVW50aWxQLCB0YWtlV2hpbGUsIHRha2VXaGlsZVAsIHRva2VuLCB0cnlQYXJzZSB9O1xuIl0sIm5hbWVzIjpbImNsYW1wIiwiZXNjYXBlV2hpdGVzcGFjZSIsImVtaXQiLCJ2YWx1ZSIsImRhdGEiLCJpIiwibWF0Y2hlZCIsInBvc2l0aW9uIiwibWFrZSIsImYiLCJhY3Rpb24iLCJmYWlsIiwiZXJyb3IiLCJtZXNzYWdlIiwiRXJyb3IiLCJGdW5jdGlvbiIsInRva2VuIiwib25Ub2tlbiIsIm9uRW5kIiwidW5kZWZpbmVkIiwidG9rZW5zIiwibGVuZ3RoIiwiYW55Iiwic2F0aXNmeSIsInRlc3QiLCJtYXBJbm5lciIsInIiLCJtYXBPdXRlciIsIm1hcCIsInAiLCJtYXBwZXIiLCJ2IiwiaiIsIm1hcDEiLCJtIiwicGVlayIsIm9wdGlvbiIsImRlZiIsIm5vdCIsImNob2ljZSIsInBzIiwicmVzdWx0Iiwib3RoZXJ3aXNlIiwicGEiLCJwYiIsInIxIiwibG9uZ2VzdCIsIm1hdGNoIiwidGFrZVdoaWxlIiwidmFsdWVzIiwic3VjY2VzcyIsInB1c2giLCJ0YWtlVW50aWwiLCJuIiwidGFrZVdoaWxlUCIsInBWYWx1ZSIsInBUZXN0IiwidGFrZVVudGlsUCIsIm1hbnkiLCJtYW55MSIsImFiIiwiaGVhZCIsInRhaWwiLCJqb2luIiwibWEiLCJ2YiIsImxlZnQiLCJ2YSIsInJpZ2h0IiwiYWJjIiwicGMiLCJtYiIsInZjIiwibWlkZGxlIiwicmEiLCJyYiIsImFsbCIsInNraXAiLCJmbGF0dGVuIiwiZmxhdHRlbjEiLCJ2cyIsImZsYXRNYXAiLCJzZXBCeTEiLCJwU2VwIiwic2VwQnkiLCJjaGFpblJlZHVjZSIsImFjYyIsImxvb3AiLCJhY2MxIiwicG9zIiwicmVkdWNlTGVmdCIsInJlZHVjZXIiLCJyZWR1Y2VSaWdodCIsImxlZnRBc3NvYzEiLCJwTGVmdCIsInBPcGVyIiwiY2hhaW4iLCJ2MCIsInJpZ2h0QXNzb2MxIiwicFJpZ2h0IiwieSIsImxlZnRBc3NvYzIiLCJyaWdodEFzc29jMiIsIngiLCJjb25kaXRpb24iLCJjb25kIiwicFRydWUiLCJwRmFsc2UiLCJkZWNpZGUiLCJtMSIsImFoZWFkIiwicmVjdXJzaXZlIiwic3RhcnQiLCJlbmQiLCJyZW1haW5pbmdUb2tlbnNOdW1iZXIiLCJwYXJzZXJQb3NpdGlvbiIsImZvcm1hdFRva2VuIiwiY29udGV4dFRva2VucyIsImxlbiIsImxvd0luZGV4IiwiaGlnaEluZGV4IiwidG9rZW5zU2xpY2UiLCJzbGljZSIsImxpbmVzIiwiaW5kZXhXaWR0aCIsIlN0cmluZyIsInBhZFN0YXJ0IiwiaW5kZXgiLCJwYXJzZSIsInBhcnNlciIsIm9wdGlvbnMiLCJKU09OIiwic3RyaW5naWZ5IiwidHJ5UGFyc2UiLCJtYXRjaGVyIiwiYW5kIiwiZGlzY2FyZCIsImVpdGhlck9yIiwiZW9mIiwibG9va0FoZWFkIiwib2YiLCJvciIsInNvbWUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peberminta/lib/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/peberminta/lib/util.mjs":
/*!**********************************************!*\
  !*** ./node_modules/peberminta/lib/util.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   escapeWhitespace: () => (/* binding */ escapeWhitespace)\n/* harmony export */ });\nfunction clamp(left, x, right) {\n  return Math.max(left, Math.min(x, right));\n}\nfunction escapeWhitespace(str) {\n  return str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\\\t' : r ? '\\\\r' : '\\\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGViZXJtaW50YS9saWIvdXRpbC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxTQUFTQSxLQUFLQSxDQUFDQyxJQUFJLEVBQUVDLENBQUMsRUFBRUMsS0FBSyxFQUFFO0VBQzNCLE9BQU9DLElBQUksQ0FBQ0MsR0FBRyxDQUFDSixJQUFJLEVBQUVHLElBQUksQ0FBQ0UsR0FBRyxDQUFDSixDQUFDLEVBQUVDLEtBQUssQ0FBQyxDQUFDO0FBQzdDO0FBQ0EsU0FBU0ksZ0JBQWdCQSxDQUFDQyxHQUFHLEVBQUU7RUFDM0IsT0FBT0EsR0FBRyxDQUFDQyxPQUFPLENBQUMsaUJBQWlCLEVBQUUsQ0FBQ0MsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUMsS0FBS0QsQ0FBQyxHQUFHLEtBQUssR0FBR0MsQ0FBQyxHQUFHLEtBQUssR0FBRyxLQUFLLENBQUM7QUFDckYiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxM1xcbm9kZV9tb2R1bGVzXFxwZWJlcm1pbnRhXFxsaWJcXHV0aWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGNsYW1wKGxlZnQsIHgsIHJpZ2h0KSB7XG4gICAgcmV0dXJuIE1hdGgubWF4KGxlZnQsIE1hdGgubWluKHgsIHJpZ2h0KSk7XG59XG5mdW5jdGlvbiBlc2NhcGVXaGl0ZXNwYWNlKHN0cikge1xuICAgIHJldHVybiBzdHIucmVwbGFjZSgvKFxcdCl8KFxccil8KFxcbikvZywgKG0sIHQsIHIpID0+IHQgPyAnXFxcXHQnIDogciA/ICdcXFxccicgOiAnXFxcXG4nKTtcbn1cblxuZXhwb3J0IHsgY2xhbXAsIGVzY2FwZVdoaXRlc3BhY2UgfTtcbiJdLCJuYW1lcyI6WyJjbGFtcCIsImxlZnQiLCJ4IiwicmlnaHQiLCJNYXRoIiwibWF4IiwibWluIiwiZXNjYXBlV2hpdGVzcGFjZSIsInN0ciIsInJlcGxhY2UiLCJtIiwidCIsInIiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peberminta/lib/util.mjs\n");

/***/ })

};
;