"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/middleware.ts\n// Middleware robusto de seguridad para OposiAI\n\n\n// Configuración de rutas y permisos\nconst ROUTE_PERMISSIONS = {\n    // Rutas públicas (no requieren autenticación)\n    public: [\n        '/',\n        '/login',\n        '/payment',\n        '/thank-you',\n        '/contact',\n        '/privacy',\n        '/terms',\n        '/auth/callback',\n        '/auth/unauthorized',\n        '/auth/reset-password',\n        '/auth/confirm-reset',\n        '/api/auth/register-free',\n        '/api/stripe/webhook',\n        '/api/stripe/create-checkout-session',\n        '/api/stripe/create-token-checkout',\n        '/api/notify-signup',\n        '/api/user/status',\n        '/api/health'\n    ],\n    // Rutas que requieren autenticación básica\n    authenticated: [\n        '/app',\n        '/dashboard',\n        '/profile',\n        '/welcome',\n        '/upgrade-plan'\n    ],\n    // Rutas que requieren planes específicos\n    planRestricted: {\n        '/plan-estudios': [\n            'pro'\n        ],\n        '/app/ai-tutor': [\n            'usuario',\n            'pro'\n        ],\n        '/app/summaries': [\n            'pro'\n        ],\n        '/app/advanced-features': [\n            'pro'\n        ]\n    }\n};\n// Configuración de seguridad\nconst SECURITY_CONFIG = {\n    enableStrictValidation: process.env.STRICT_PLAN_VALIDATION === 'true',\n    requirePaymentVerification: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    enableAccessLogging: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    sessionTimeout: 5 * 60 * 1000 // 5 minutos en millisegundos\n};\nasync function middleware(request) {\n    const startTime = Date.now();\n    const { pathname, search, hash } = request.nextUrl;\n    console.log(`\\n🚀 [MIDDLEWARE START] Path: ${pathname}${search}${hash}`);\n    try {\n        let supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request\n        });\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            auth: {\n                persistSession: false,\n                autoRefreshToken: false,\n                detectSessionInUrl: true // Habilitar para detectar tokens de recuperación\n            },\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll (cookiesToSet) {\n                    const filteredCookies = cookiesToSet.filter((cookie)=>!cookie.name.includes('auth-token') && !cookie.name.includes('refresh-token'));\n                    filteredCookies.forEach(({ name, value, options })=>request.cookies.set(name, value));\n                    supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request\n                    });\n                    filteredCookies.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, _objectSpread(_objectSpread({}, options), {}, {\n                            maxAge: undefined,\n                            expires: undefined\n                        })));\n                }\n            }\n        });\n        // --- Log Inicial ---\n        console.log(`[MW_LOG 1] Pathname: ${pathname}`);\n        // --- Logs detallados para diagnóstico de tokens ---\n        const urlParams = new URLSearchParams(search);\n        const hasCode = urlParams.has('code');\n        const hasError = urlParams.has('error');\n        const hasAccessToken = urlParams.has('access_token');\n        const hasRefreshToken = urlParams.has('refresh_token');\n        console.log(`[MW_LOG 1.5] URL Analysis:`, {\n            pathname,\n            search,\n            hasCode,\n            hasError,\n            hasAccessToken,\n            hasRefreshToken,\n            errorParam: urlParams.get('error'),\n            errorDescription: urlParams.get('error_description')\n        });\n        // --- Obtener Sesión TEMPRANO para diagnóstico ---\n        const { data: { session: initialSession }, error: initialSessionError } = await supabase.auth.getSession();\n        console.log(`[MW_LOG 2] Initial getSession():`, initialSession ? {\n            userId: initialSession.user.id,\n            email: initialSession.user.email,\n            aud: initialSession.user.aud,\n            tokenType: initialSession.token_type,\n            expiresAt: initialSession.expires_at,\n            expiresIn: initialSession.expires_in,\n            accessTokenLength: initialSession.access_token?.length,\n            refreshTokenLength: initialSession.refresh_token?.length\n        } : 'No session', initialSessionError ? `Error: ${initialSessionError.message}` : '');\n        // --- Obtener Usuario ---\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        console.log(`[MW_LOG 3] getUser():`, user ? {\n            userId: user.id,\n            email: user.email,\n            aud: user.aud,\n            emailConfirmed: user.email_confirmed_at,\n            lastSignIn: user.last_sign_in_at,\n            createdAt: user.created_at,\n            userMetadata: Object.keys(user.user_metadata || {}),\n            appMetadata: Object.keys(user.app_metadata || {})\n        } : 'No user', authError ? `Error: ${authError.message}` : '');\n        // --- Lógica especial para /auth/reset-password ---\n        // Permitir acceso completo a reset-password (es una ruta pública que maneja su propia autenticación)\n        if (pathname === '/auth/reset-password') {\n            console.log(`[MW_LOG 6] Path is /auth/reset-password. Allowing passthrough (public route with internal auth handling).`);\n            return addSecurityHeaders(supabaseResponse);\n        }\n        // --- Lógica especial para otras rutas de autenticación y configuración ---\n        const authSetupRoutes = [\n            '/auth/callback',\n            '/welcome',\n            '/upgrade-plan'\n        ];\n        if (user && authSetupRoutes.some((route)=>pathname.startsWith(route))) {\n            console.log(`[MW_LOG 6.1] Path is auth/setup route: ${pathname}. User detected: ${user.id}. Checking if legitimate...`);\n            // Para rutas de configuración, verificar si es un usuario legítimo\n            const isLegitimateUser = await checkIfLegitimateUser(user, supabase);\n            if (isLegitimateUser.isLegitimate) {\n                console.log(`[MW_LOG 6.2] User ${user.id} is legitimate for auth setup. Allowing passthrough.`);\n                return addSecurityHeaders(supabaseResponse);\n            } else {\n                console.log(`[MW_LOG 6.3] User ${user.id} is NOT legitimate for auth setup. Redirecting to payment.`);\n                return redirectToPayment(request, 'Invalid account - please complete registration');\n            }\n        }\n        // --- Lógica de Rutas Públicas ---\n        if (isPublicRoute(pathname)) {\n            // Si hay un usuario y está en una ruta pública como /login (que no sea reset-password), redirigir\n            if (user && (pathname === '/' || pathname === '/login')) {\n                console.log(`[MW_LOG 7] Authenticated user on ${pathname}. Redirecting to /app.`);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/app', request.url));\n            }\n            console.log(`[MW_LOG 4] Path is public: ${pathname}. Allowing (or already handled user redirection).`);\n            return addSecurityHeaders(supabaseResponse);\n        }\n        // --- Si NO hay Usuario y NO es ruta pública ---\n        if (!user) {\n            console.log(`[MW_LOG 5] No user, path is NOT public. Redirecting to login.`);\n            return redirectToLogin(request);\n        }\n        // --- Resto de las validaciones para rutas autenticadas ---\n        if (isAuthenticatedRoute(pathname)) {\n            const profileValidation = await validateUserProfile(user.id, supabase);\n            console.log(`[MW_LOG 8] Profile validation for ${pathname}:`, profileValidation);\n            if (!profileValidation.valid) {\n                console.log(`[MW_LOG 8.1] Profile invalid. Reason: ${profileValidation.reason}. Redirecting...`);\n                // Manejar usuarios autenticados sin perfil\n                if (profileValidation.reason === 'Profile not found') {\n                    // Verificar si es un usuario que se registró para cuenta gratuita\n                    const isLegitimateUser = await checkIfLegitimateUser(user, supabase);\n                    if (isLegitimateUser.isLegitimate) {\n                        try {\n                            const accountType = isLegitimateUser.accountType === 'unknown' ? 'free' : isLegitimateUser.accountType;\n                            await createRecoveryProfile(user.id, accountType, supabase);\n                            console.log(`✅ [MIDDLEWARE] Perfil de recuperación creado para usuario legítimo: ${user.id}`);\n                        // Continuar con la ejecución normal\n                        } catch (error) {\n                            console.error('❌ [MIDDLEWARE] Error creando perfil de recuperación:', error);\n                            return redirectToPayment(request, 'Profile recovery failed');\n                        }\n                    } else {\n                        // Usuario sospechoso sin perfil válido\n                        console.log(`🚨 [MIDDLEWARE] Usuario sospechoso sin perfil: ${user.id}`);\n                        return redirectToPayment(request, 'Invalid account - please complete registration');\n                    }\n                } else {\n                    // Para otros errores de validación (cuenta expirada, pago no verificado, etc.)\n                    return redirectToPayment(request, profileValidation.reason);\n                }\n            }\n        }\n        // Para rutas con restricciones de plan\n        if (isPlanRestrictedRoute(pathname)) {\n            const planValidation = await validatePlanAccess(user.id, pathname, supabase);\n            console.log(`[MW_LOG 9] Plan validation for ${pathname}:`, planValidation);\n            if (!planValidation.valid) {\n                console.log(`[MW_LOG 9.1] Plan invalid. Reason: ${planValidation.reason}. Redirecting to unauthorized.`);\n                return redirectToUnauthorized(request, planValidation);\n            }\n        }\n        // --- Log Final ---\n        const processingTime = Date.now() - startTime;\n        console.log(`[MW_LOG 10] ✅ Access granted to ${pathname} in ${processingTime}ms`);\n        // Agregar headers de seguridad y retornar\n        return addSecurityHeaders(supabaseResponse);\n    } catch (error) {\n        console.error('❌ [MIDDLEWARE] Critical error:', error);\n        // En caso de error crítico, denegar acceso por seguridad\n        return redirectToLogin(request);\n    }\n}\n// Funciones auxiliares para validación de rutas\nfunction isPublicRoute(pathname) {\n    return ROUTE_PERMISSIONS.public.some((path)=>pathname === path || pathname.startsWith(path + '/'));\n}\nfunction isAuthenticatedRoute(pathname) {\n    return ROUTE_PERMISSIONS.authenticated.some((path)=>pathname.startsWith(path));\n}\nfunction isPlanRestrictedRoute(pathname) {\n    return Object.keys(ROUTE_PERMISSIONS.planRestricted).some((path)=>pathname.startsWith(path));\n}\n// Funciones de validación\nasync function checkIfLegitimateUser(user, supabase) {\n    try {\n        // Verificar si el usuario tiene metadata que indique registro legítimo\n        const userMetadata = user.user_metadata || {};\n        const appMetadata = user.app_metadata || {};\n        console.log(`🔍 [MIDDLEWARE] Checking user legitimacy for ${user.id}:`, {\n            userMetadata: Object.keys(userMetadata),\n            appMetadata: Object.keys(appMetadata),\n            created_via: userMetadata.created_via,\n            free_account: userMetadata.free_account,\n            plan: userMetadata.plan\n        });\n        // Verificar si es una cuenta gratuita creada automáticamente\n        if (userMetadata.created_via === 'free_registration' || userMetadata.free_account) {\n            console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Free account registration`);\n            return {\n                isLegitimate: true,\n                accountType: 'free',\n                reason: 'Free account registration'\n            };\n        }\n        // Verificar si es un usuario en proceso de configuración de contraseña\n        if (userMetadata.plan && (userMetadata.plan === 'free' || userMetadata.plan === 'usuario' || userMetadata.plan === 'pro')) {\n            const accountType = userMetadata.plan === 'free' ? 'free' : 'paid';\n            console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: User in setup process (${userMetadata.plan})`);\n            return {\n                isLegitimate: true,\n                accountType,\n                reason: 'User in setup process'\n            };\n        }\n        // Verificar si tiene datos de Stripe en metadata (usuario de pago en proceso)\n        if (userMetadata.stripe_session_id || userMetadata.stripe_customer_id) {\n            console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Has Stripe metadata`);\n            return {\n                isLegitimate: true,\n                accountType: 'paid',\n                reason: 'Has Stripe metadata'\n            };\n        }\n        // Verificar si hay transacciones de Stripe asociadas\n        const { data: transactions, error } = await supabase.from('stripe_transactions').select('id, status, plan_id').eq('user_id', user.id).limit(1);\n        if (!error && transactions && transactions.length > 0) {\n            console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Has payment transactions`);\n            return {\n                isLegitimate: true,\n                accountType: 'paid',\n                reason: 'Has payment transactions'\n            };\n        }\n        // Verificar si el email está en la lista de usuarios invitados/creados por admin\n        if (appMetadata.created_by_admin || userMetadata.invited_by_admin) {\n            console.log(`✅ [MIDDLEWARE] User ${user.id} is legitimate: Admin created account`);\n            return {\n                isLegitimate: true,\n                accountType: 'free',\n                reason: 'Admin created account'\n            };\n        }\n        // Si llegamos aquí, es sospechoso\n        console.log(`🚨 [MIDDLEWARE] User ${user.id} is NOT legitimate: No legitimate registration method found`);\n        return {\n            isLegitimate: false,\n            accountType: 'unknown',\n            reason: 'No legitimate registration method found'\n        };\n    } catch (error) {\n        console.error('❌ [MIDDLEWARE] Error checking user legitimacy:', error);\n        return {\n            isLegitimate: false,\n            accountType: 'unknown',\n            reason: 'Verification error'\n        };\n    }\n}\nasync function createRecoveryProfile(userId, accountType, supabase) {\n    try {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        let profileData;\n        if (accountType === 'free') {\n            // Crear perfil gratuito con expiración\n            const expirationDate = new Date();\n            expirationDate.setDate(expirationDate.getDate() + 5); // 5 días\n            profileData = {\n                user_id: userId,\n                subscription_plan: 'free',\n                monthly_token_limit: 50000,\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                // Las cuentas gratuitas se consideran verificadas\n                plan_expires_at: expirationDate.toISOString(),\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    recovered_profile: true,\n                    recovery_date: new Date().toISOString(),\n                    original_account_type: 'free'\n                }\n            };\n        } else {\n            // Para cuentas de pago, crear perfil básico sin verificación\n            profileData = {\n                user_id: userId,\n                subscription_plan: 'free',\n                // Temporal hasta que se verifique el pago\n                monthly_token_limit: 50000,\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: false,\n                // Requiere verificación manual\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    recovered_profile: true,\n                    recovery_date: new Date().toISOString(),\n                    original_account_type: 'paid',\n                    requires_manual_verification: true\n                }\n            };\n        }\n        const { error } = await supabase.from('user_profiles').insert([\n            profileData\n        ]);\n        if (error) {\n            console.error('Error creating recovery profile:', error);\n            throw error;\n        }\n    } catch (error) {\n        console.error('Failed to create recovery profile:', error);\n        throw error;\n    }\n}\nasync function validateUserProfile(userId, supabase) {\n    try {\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, payment_verified, plan_expires_at, auto_renew, security_flags').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                valid: false,\n                reason: 'Profile not found'\n            };\n        }\n        // Verificar expiración para todos los planes que tengan plan_expires_at\n        if (profile.plan_expires_at) {\n            const now = new Date();\n            const expiresAt = new Date(profile.plan_expires_at);\n            if (now > expiresAt) {\n                // Determinar el motivo de expiración basado en el plan\n                let reason = 'Account expired';\n                if (profile.subscription_plan === 'free') {\n                    reason = 'Free account expired';\n                } else {\n                    reason = 'Subscription grace period expired';\n                }\n                return {\n                    valid: false,\n                    reason\n                };\n            }\n        }\n        // Verificar pago para planes de pago\n        if (SECURITY_CONFIG.requirePaymentVerification && profile.subscription_plan !== 'free' && !profile.payment_verified) {\n            return {\n                valid: false,\n                reason: 'Payment not verified'\n            };\n        }\n        return {\n            valid: true\n        };\n    } catch (error) {\n        console.error('Error validating user profile:', error);\n        return {\n            valid: false,\n            reason: 'Validation error'\n        };\n    }\n}\nasync function validatePlanAccess(userId, pathname, supabase) {\n    try {\n        // Encontrar qué planes se requieren para esta ruta\n        const requiredPlans = Object.entries(ROUTE_PERMISSIONS.planRestricted).find(([path])=>pathname.startsWith(path))?.[1];\n        if (!requiredPlans) {\n            return {\n                valid: true\n            };\n        }\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, payment_verified').eq('user_id', userId).single();\n        if (error || !profile) {\n            return {\n                valid: false,\n                reason: 'Profile not found',\n                requiredPlans\n            };\n        }\n        // Verificar si el plan del usuario está en la lista de planes permitidos\n        if (!requiredPlans.includes(profile.subscription_plan)) {\n            return {\n                valid: false,\n                reason: `Plan ${profile.subscription_plan} not sufficient`,\n                requiredPlans\n            };\n        }\n        return {\n            valid: true\n        };\n    } catch (error) {\n        console.error('Error validating plan access:', error);\n        return {\n            valid: false,\n            reason: 'Validation error',\n            requiredPlans: []\n        };\n    }\n}\n// Funciones de redirección\nfunction redirectToLogin(request) {\n    const url = request.nextUrl.clone();\n    url.pathname = '/login';\n    url.searchParams.set('redirect', request.nextUrl.pathname);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n}\nfunction redirectToPayment(request, reason) {\n    const url = request.nextUrl.clone();\n    url.pathname = '/payment';\n    if (reason) {\n        url.searchParams.set('reason', reason);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n}\nfunction redirectToUnauthorized(request, validation) {\n    const url = request.nextUrl.clone();\n    url.pathname = '/auth/unauthorized';\n    url.searchParams.set('reason', validation.reason || 'Access denied');\n    url.searchParams.set('feature', request.nextUrl.pathname);\n    if (validation.requiredPlans) {\n        url.searchParams.set('required_plan', validation.requiredPlans.join(','));\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n}\n// Función para agregar headers de seguridad\nfunction addSecurityHeaders(response) {\n    // Headers de seguridad\n    response.headers.set('X-Frame-Options', 'DENY');\n    response.headers.set('X-Content-Type-Options', 'nosniff');\n    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n    response.headers.set('X-XSS-Protection', '1; mode=block');\n    // CSP básico\n    response.headers.set('Content-Security-Policy', \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://d3js.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.stripe.com;\");\n    return response;\n}\n// Configuración del matcher para definir en qué rutas se ejecutará el middleware.\nconst config = {\n    matcher: [\n        /*\n   * Match all request paths except for the ones starting with:\n   * - _next/static (static files)\n   * - _next/image (image optimization files)\n   * - favicon.ico (favicon file)\n   *\n   * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,\n   * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.\n   * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.\n   *\n   * La expresión regular abajo intenta cubrir los casos más comunes:\n   */ '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\\\..*).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});