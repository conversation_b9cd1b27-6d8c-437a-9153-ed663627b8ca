import React, { useState, useEffect } from 'react';
import { FiX, FiBarChart2, <PERSON>Clock, FiTrendingUp, FiAward, FiTarget } from 'react-icons/fi';
import { obtenerEstadisticasDetalladas, EstadisticasEstudio } from '@/lib/supabase';

interface FlashcardDetailedStatisticsProps {
  coleccionId: string;
  coleccionTitulo: string;
  onClose: () => void;
}

const FlashcardDetailedStatistics: React.FC<FlashcardDetailedStatisticsProps> = ({
  coleccionId,
  coleccionTitulo,
  onClose
}) => {
  const [estadisticas, setEstadisticas] = useState<EstadisticasEstudio | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    cargarEstadisticas();
  }, [coleccionId]);

  const cargarEstadisticas = async () => {
    try {
      setIsLoading(true);
      const stats = await obtenerEstadisticasDetalladas(coleccionId);
      setEstadisticas(stats);
    } catch (error) {
      console.error('Error al cargar estadísticas detalladas:', error);
      setError('No se pudieron cargar las estadísticas detalladas');
    } finally {
      setIsLoading(false);
    }
  };

  const formatearFecha = (fechaStr: string) => {
    const fecha = new Date(fechaStr);
    return fecha.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Error</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <FiX size={24} />
            </button>
          </div>
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    );
  }

  if (!estadisticas) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Estadísticas Detalladas</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-800">{coleccionTitulo}</h3>
        </div>

        {/* Estadísticas generales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiClock className="text-blue-600 mr-2 text-xl" />
              <h4 className="font-semibold">Total Revisiones</h4>
            </div>
            <p className="text-3xl font-bold text-blue-700">{estadisticas.totalRevisiones}</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiTrendingUp className="text-green-600 mr-2 text-xl" />
              <h4 className="font-semibold">Sesiones de Estudio</h4>
            </div>
            <p className="text-3xl font-bold text-green-700">{estadisticas.totalSesiones}</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiTarget className="text-purple-600 mr-2 text-xl" />
              <h4 className="font-semibold">Respuestas Fáciles</h4>
            </div>
            <p className="text-3xl font-bold text-purple-700">{estadisticas.distribucionDificultad.facil}</p>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiAward className="text-orange-600 mr-2 text-xl" />
              <h4 className="font-semibold">Respuestas Difíciles</h4>
            </div>
            <p className="text-3xl font-bold text-orange-700">{estadisticas.distribucionDificultad.dificil}</p>
          </div>
        </div>

        {/* Distribución de dificultad */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold mb-3">Distribución de Respuestas</h4>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{estadisticas.distribucionDificultad.dificil}</div>
                <div className="text-sm text-gray-600">Difíciles</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{estadisticas.distribucionDificultad.normal}</div>
                <div className="text-sm text-gray-600">Normales</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{estadisticas.distribucionDificultad.facil}</div>
                <div className="text-sm text-gray-600">Fáciles</div>
              </div>
            </div>
          </div>
        </div>

        {/* Tarjetas más difíciles */}
        {estadisticas.tarjetasMasDificiles.length > 0 && (
          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-3">Tarjetas que Requieren Más Atención</h4>
            <div className="space-y-3">
              {estadisticas.tarjetasMasDificiles.slice(0, 5).map((tarjeta, index) => (
                <div key={tarjeta.id} className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{tarjeta.pregunta}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm">
                        <span className="text-red-600">Difíciles: {tarjeta.dificil}</span>
                        <span className="text-yellow-600">Normales: {tarjeta.normal}</span>
                        <span className="text-green-600">Fáciles: {tarjeta.facil}</span>
                        <span className="text-gray-600">Total: {tarjeta.totalRevisiones}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-red-600">#{index + 1}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Progreso en el tiempo */}
        {estadisticas.progresoTiempo.length > 0 && (
          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-3">Progreso Reciente</h4>
            <div className="space-y-2">
              {estadisticas.progresoTiempo.slice(-7).map((progreso, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-3">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{formatearFecha(progreso.fecha)}</span>
                    <div className="flex space-x-4 text-sm">
                      <span className="text-blue-600">Nuevas: {progreso.nuevas}</span>
                      <span className="text-yellow-600">Aprendiendo: {progreso.aprendiendo}</span>
                      <span className="text-orange-600">Repasando: {progreso.repasando}</span>
                      <span className="text-green-600">Aprendidas: {progreso.aprendidas}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlashcardDetailedStatistics;
