import React from 'react';
import { FiEdit2, FiTrash2 } from 'react-icons/fi';
// Tipos para las entidades
interface ColeccionFlashcards {
  id: string;
  nombre: string;
  titulo: string;
  descripcion?: string;
  user_id: string;
  creado_en: string;
  actualizado_en: string;
}

interface FlashcardConProgreso {
  id: string;
  pregunta: string;
  respuesta: string;
  coleccion_id: string;
  creado_en: string;
  debeEstudiar?: boolean;
  progreso?: {
    nivel: number;
    proxima_revision: string;
    facilidad: number;
    intervalo: number;
    repeticiones: number;
    estado?: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendida';
  };
}
import { EstadisticasColeccion } from './types';

interface FlashcardCollectionViewProps {
  coleccion: ColeccionFlashcards;
  flashcards: FlashcardConProgreso[];
  estadisticas: EstadisticasColeccion | null;
  isLoading: boolean;
  onStartStudy: () => void;
  onShowStudyOptions: () => void;
  onShowStatistics: () => void;
  onEditFlashcard?: (flashcard: FlashcardConProgreso) => void;
  onDeleteFlashcard?: (flashcardId: string) => void;
  deletingFlashcardId?: string | null;
}

const FlashcardCollectionView: React.FC<FlashcardCollectionViewProps> = ({
  coleccion,
  flashcards,
  estadisticas,
  isLoading,
  onStartStudy,
  onShowStudyOptions,
  onShowStatistics,
  onEditFlashcard,
  onDeleteFlashcard,
  deletingFlashcardId
}) => {
  return (
    <div>
      <h3 className="text-xl font-semibold mb-4">{coleccion.titulo}</h3>

      {/* Estadísticas */}
      {estadisticas && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h4 className="font-medium text-gray-900 mb-2">Estadísticas</h4>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm">
            <div className="text-center">
              <div className="font-semibold text-blue-600">{estadisticas.total}</div>
              <div className="text-gray-500">Total</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-600">{estadisticas.paraHoy}</div>
              <div className="text-gray-500">Para hoy</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-600">{estadisticas.nuevas}</div>
              <div className="text-gray-500">Nuevas</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-yellow-600">{estadisticas.aprendiendo}</div>
              <div className="text-gray-500">Aprendiendo</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">{estadisticas.aprendidas}</div>
              <div className="text-gray-500">Aprendidas</div>
            </div>
          </div>
        </div>
      )}

      {/* Botones de acción */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
        <button
          onClick={onStartStudy}
          className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors"
        >
          {`Estudiar (${estadisticas ? estadisticas.paraHoy : 0} para hoy)`}
        </button>
        <button
          onClick={onShowStudyOptions}
          className="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors"
        >
          Opciones de estudio
        </button>
        <button
          onClick={onShowStatistics}
          className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors"
        >
          Ver estadísticas
        </button>
      </div>

      {/* Lista de flashcards */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : flashcards.length === 0 ? (
        <div className="text-center p-4">
          <p className="text-gray-500">No hay flashcards en esta colección.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {flashcards.map((card, index) => (
            <div
              key={card.id}
              className={`p-3 border rounded-lg ${
                card.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <p className="font-medium flex-1 pr-2">{card.pregunta}</p>
                <div className="flex items-center space-x-1 flex-shrink-0">
                  {onEditFlashcard && (
                    <button
                      onClick={() => onEditFlashcard(card)}
                      className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded"
                      title="Editar flashcard"
                    >
                      <FiEdit2 size={14} />
                    </button>
                  )}
                  {onDeleteFlashcard && (
                    <button
                      onClick={() => onDeleteFlashcard(card.id)}
                      disabled={deletingFlashcardId === card.id}
                      className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded disabled:opacity-50"
                      title="Eliminar flashcard"
                    >
                      {deletingFlashcardId === card.id ? (
                        <div className="animate-spin rounded-full h-3.5 w-3.5 border-b-2 border-red-600"></div>
                      ) : (
                        <FiTrash2 size={14} />
                      )}
                    </button>
                  )}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-xs text-gray-500">Tarjeta {index + 1}</p>
                {card.progreso && (
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    card.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' :
                    card.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' :
                    card.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {card.progreso.estado}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FlashcardCollectionView;
