// src/components/ui/PlanCard.tsx
'use client';

import React from 'react';
import Link from 'next/link';

interface PlanCardProps {
  id: string;
  name: string;
  price: number;
  features: readonly string[];
  isPopular?: boolean;
}

export default function PlanCard({ id, name, price, features, isPopular = false }: PlanCardProps) {
  const formatPrice = (price: number) => {
    if (price === 0) return 'Gratis';
    return `€${(price / 100).toFixed(2)}`;
  };

  const getCardStyle = () => {
    if (id === 'free') {
      return 'bg-white border border-gray-200';
    } else if (id === 'usuario') {
      return 'bg-gradient-to-br from-blue-500 to-blue-600 text-white';
    } else if (id === 'pro') {
      return 'bg-gradient-to-br from-purple-500 to-purple-600 text-white';
    }
    return 'bg-white';
  };

  const getTextColor = () => {
    return id === 'free' ? 'text-gray-900' : 'text-white';
  };

  const getFeatureTextColor = () => {
    return id === 'free' ? 'text-gray-600' : 'text-white/90';
  };

  const getButtonStyle = () => {
    if (id === 'free') {
      return 'bg-blue-600 text-white hover:bg-blue-700';
    } else {
      return 'bg-white text-gray-900 hover:bg-gray-100';
    }
  };

  return (
    <div className={`relative rounded-2xl shadow-xl transform hover:scale-105 transition-all duration-300 ${getCardStyle()} h-full flex flex-col`}>
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-yellow-400 text-gray-900 px-4 py-1 text-sm font-bold rounded-full">
            Recomendado
          </span>
        </div>
      )}

      <div className="p-8 flex flex-col h-full">
        <div className="text-center">
          <h3 className={`text-2xl font-bold mb-2 ${getTextColor()}`}>{name}</h3>
          <div className="mb-6">
            <span className={`text-4xl font-bold ${getTextColor()}`}>
              {formatPrice(price)}
            </span>
            {price > 0 && <span className={`text-lg ${id === 'free' ? 'text-gray-500' : 'text-white/80'}`}>/mes</span>}
          </div>
        </div>

        <ul className="space-y-3 mb-8 flex-grow">
          {features.map((feature, index) => {
            // Para planes con formato especial (free, usuario y pro), manejar formato especial
            if (id === 'free' || id === 'usuario' || id === 'pro') {
              // Si es un encabezado (Incluye: o No incluye:)
              if (feature === 'Incluye:' || feature === 'No incluye:') {
                return (
                  <li key={index} className="mt-4 first:mt-0">
                    <span className={`text-sm font-semibold ${getTextColor()}`}>{feature}</span>
                  </li>
                );
              }

              // Si es una característica que no está incluida
              const isNotIncluded = feature.startsWith('• ') && features[index - 1] === 'No incluye:' ||
                                   (index > 0 && features.slice(0, index).lastIndexOf('No incluye:') > features.slice(0, index).lastIndexOf('Incluye:'));

              return (
                <li key={index} className="flex items-start ml-2">
                  {isNotIncluded ? (
                    // Ícono X para características no incluidas
                    <svg
                      className={`h-4 w-4 mt-0.5 mr-3 flex-shrink-0 ${id === 'free' ? 'text-red-500' : 'text-red-400'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    // Ícono check para características incluidas
                    <svg
                      className={`h-4 w-4 mt-0.5 mr-3 flex-shrink-0 ${id === 'free' ? 'text-green-500' : id === 'usuario' ? 'text-green-400' : 'text-white'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                  <span className={`text-sm ${getFeatureTextColor()}`}>
                    {feature.startsWith('• ') ? feature.substring(2) : feature}
                  </span>
                </li>
              );
            }

            // Para otros planes, mantener el formato original
            return (
              <li key={index} className="flex items-start">
                <svg
                  className={`h-5 w-5 mt-0.5 mr-3 flex-shrink-0 ${id === 'free' ? 'text-green-500' : 'text-white'}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className={`text-sm ${getFeatureTextColor()}`}>{feature}</span>
              </li>
            );
          })}
        </ul>

        <div className="mt-auto">
          <Link
            href={`/payment?plan=${id}`}
            className={`w-full flex justify-center py-3 px-6 rounded-lg font-semibold transition-colors ${getButtonStyle()}`}
          >
            {id === 'free' ? 'Empezar Gratis' : `Seleccionar ${name}`}
          </Link>
        </div>
      </div>
    </div>
  );
}
