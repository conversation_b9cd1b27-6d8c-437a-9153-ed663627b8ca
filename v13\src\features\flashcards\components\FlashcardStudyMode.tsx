// src/components/flashcards/FlashcardStudyMode.tsx
// Fixed 3D flip animation
import React, { useState, useEffect } from 'react';
import { FlashcardConProgreso, DificultadRespuesta } from '@/lib/supabase';
import { IoArrowBack, IoArrowForward } from 'react-icons/io5';
import { motion } from 'framer-motion';
import { FiThumbsUp, FiThumbsDown, FiCheck, FiRotateCw, FiHelpCircle } from 'react-icons/fi';

interface FlashcardStudyModeProps {
  flashcards: FlashcardConProgreso[];
  activeIndex: number;
  respondiendo: boolean; // Para deshabilitar interacciones mientras se guarda la respuesta
  onRespuesta: (dificultad: DificultadRespuesta) => void;
  onNavigate: (direction: 'prev' | 'next') => void;
  onVolver: () => void;
  onReiniciarProgreso?: (flashcardId: string) => void;
  onVerHistorial?: (flashcardId: string) => void;
}

const FlashcardStudyMode: React.FC<FlashcardStudyModeProps> = ({
  flashcards,
  activeIndex,
  respondiendo,
  onRespuesta,
  onNavigate,
  onVolver,
  onReiniciarProgreso,
  onVerHistorial,
}) => {
  const currentFlashcard = flashcards[activeIndex];
  const [isFlipped, setIsFlipped] = useState(false); // Único estado para controlar el volteo

  // Efecto para resetear el estado de volteo cuando cambia la tarjeta (activeIndex)
  useEffect(() => {
    setIsFlipped(false); // Siempre empezar mostrando la pregunta
  }, [activeIndex]); // Solo depende de activeIndex

  const handleCardFlip = (e?: React.MouseEvent) => {
    // Prevenir volteo si el clic fue en un botón dentro de la tarjeta
    if (e) {
        const target = e.target as HTMLElement;
        if (target.closest('button')) { // Si el clic fue en un botón o su hijo
            return;
        }
    }
    if (respondiendo) return; // No voltear si se está procesando una respuesta
    setIsFlipped(prev => !prev);
  };

  const handleDifficultyClick = (e: React.MouseEvent, dificultad: DificultadRespuesta) => {
    e.stopPropagation(); // Prevenir que el clic en el botón voltee la tarjeta
    if (respondiendo) return;
    onRespuesta(dificultad);
  };

  const handleOptionalButtonClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    if (respondiendo) return;
    action();
  }

  if (!currentFlashcard) {
    // Esto puede pasar brevemente si flashcards está vacío o activeIndex es inválido
    return (
      <div className="flex flex-col items-center justify-center h-96 text-gray-500">
        Cargando tarjeta...
        <button
          onClick={onVolver}
          className="mt-4 flex items-center text-blue-600 hover:text-blue-800"
        >
          <IoArrowBack className="mr-1" /> Volver
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full">
      <div className="w-full flex justify-between items-center mb-4 px-2">
        <button
          onClick={onVolver}
          className="flex items-center text-sm text-gray-600 hover:text-gray-900 p-2 rounded-md hover:bg-gray-100 transition-colors"
          disabled={respondiendo}
        >
          <IoArrowBack className="mr-1" /> Volver
        </button>
        <div className="text-sm text-gray-500">
          {activeIndex + 1} / {flashcards.length}
        </div>
      </div>

      <div className="w-full max-w-2xl mx-auto">
        {/* Contenedor que define el espacio 3D y permite el clic para voltear */}
        <div
          className="relative w-full h-[24rem] sm:h-[28rem] perspective-1000"
          onClick={() => handleCardFlip()} // Clic en el área de la tarjeta la voltea
        >
          <motion.div
            className="absolute w-full h-full transform-style-3d" // Crucial para que los hijos se transformen en 3D
            animate={{ rotateY: isFlipped ? 180 : 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Cara Frontal (Pregunta) */}
            <div className="absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between">
              <div>
                {/* ... (código para mostrar estado de progreso, igual que antes) ... */}
                <div className="text-xs text-gray-400 mb-2">
                  {currentFlashcard.progreso?.estado && (
                    <span className={`px-2 py-0.5 rounded-full font-medium ${currentFlashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-700' :
                        currentFlashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-700' :
                        currentFlashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-700' :
                        'bg-green-100 text-green-700' }`}>
                      {currentFlashcard.progreso.estado.charAt(0).toUpperCase() + currentFlashcard.progreso.estado.slice(1)}
                    </span>
                  )}
                  {!currentFlashcard.progreso?.estado && ( <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium text-xs">Nuevo</span> )}
                </div>
                <div className="flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px]">
                  <h3 className="text-lg sm:text-xl font-semibold text-center text-gray-800 break-words">
                    {currentFlashcard.pregunta}
                  </h3>
                </div>
              </div>
              <div className="text-center mt-4">
                <button
                  onClick={(e) => { e.stopPropagation(); handleCardFlip(); }}
                  className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg text-sm transition-colors"
                  disabled={respondiendo}
                >
                  Mostrar respuesta
                </button>
              </div>
            </div>

            {/* Cara Trasera (Respuesta) - Aseguramos que el texto no esté invertido */}
            <div className="absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between rotate-y-180">
              <div className="flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px] overflow-y-auto">
                <p className="text-base sm:text-lg text-center text-gray-700 whitespace-pre-wrap break-words transform-none">
                  {currentFlashcard.respuesta}
                </p>
              </div>
              <div className="mt-4 space-y-3">
                <p className="text-center text-sm font-medium text-gray-600">¿Qué tal te ha resultado?</p>
                <div className="flex justify-around space-x-2 sm:space-x-3">
                  <button
                    onClick={(e) => handleDifficultyClick(e, 'dificil')}
                    disabled={respondiendo}
                    className="flex-1 flex flex-col items-center p-3 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-colors disabled:opacity-50"
                  >
                    <FiThumbsDown className="mb-1 text-xl" /> <span className="text-xs font-medium">Difícil</span>
                  </button>
                  <button
                    onClick={(e) => handleDifficultyClick(e, 'normal')}
                    disabled={respondiendo}
                    className="flex-1 flex flex-col items-center p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 text-yellow-600 transition-colors disabled:opacity-50"
                  >
                    <FiCheck className="mb-1 text-xl" /> <span className="text-xs font-medium">Normal</span>
                  </button>
                  <button
                    onClick={(e) => handleDifficultyClick(e, 'facil')}
                    disabled={respondiendo}
                    className="flex-1 flex flex-col items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-600 transition-colors disabled:opacity-50"
                  >
                    <FiThumbsUp className="mb-1 text-xl" /> <span className="text-xs font-medium">Fácil</span>
                  </button>
                </div>
                {(onReiniciarProgreso || onVerHistorial) && (
                  <div className="flex justify-center space-x-4 pt-2 text-xs">
                    {onReiniciarProgreso && (
                       <button
                        onClick={(e) => handleOptionalButtonClick(e, () => onReiniciarProgreso(currentFlashcard.id))}
                        disabled={respondiendo}
                        className="text-gray-500 hover:text-gray-700 underline flex items-center"
                      >
                        <FiRotateCw size={12} className="mr-1"/> Reiniciar
                      </button>
                    )}
                    {onVerHistorial && (
                      <button
                        onClick={(e) => handleOptionalButtonClick(e, () => onVerHistorial(currentFlashcard.id))}
                        disabled={respondiendo}
                        className="text-blue-500 hover:text-blue-700 underline flex items-center"
                      >
                        <FiHelpCircle size={12} className="mr-1"/> Ver Historial
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Navegación */}
      <div className="w-full max-w-2xl mx-auto flex justify-between mt-6 px-2">
        <button
          onClick={() => onNavigate('prev')}
          disabled={activeIndex === 0 || respondiendo}
          className={`flex items-center text-sm p-2 rounded-md transition-colors ${ (activeIndex === 0 || respondiendo) ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100' }`}
        >
          <IoArrowBack className="mr-1" /> Anterior
        </button>
        <button
          onClick={() => onNavigate('next')}
          disabled={activeIndex === flashcards.length - 1 || respondiendo}
          className={`flex items-center text-sm p-2 rounded-md transition-colors ${ (activeIndex === flashcards.length - 1 || respondiendo) ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100' }`}
        >
          Siguiente <IoArrowForward className="ml-1" />
        </button>
      </div>
    </div>
  );
};

export default FlashcardStudyMode;
