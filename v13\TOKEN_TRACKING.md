# 🤖 Sistema de Tracking de Tokens - Monitoreo de Costos de IA

## 📋 Resumen del Sistema

Se ha implementado un sistema completo de tracking de tokens para monitorear el uso y costo de todas las actividades de IA en la aplicación.

## 🎯 Funcionalidades Implementadas

### **1. ✅ Tracking Automático en Terminal**
Cada llamada a IA muestra en la consola:
```
🤖 [14:32:15] Conversación/Q&A | o4-mini-2025-04-16 | 📥 150 → 📤 300 = 🔢 450 tokens (~$0.002100)
🤖 [14:33:42] Generación de Test (5 preguntas) | o4-mini-2025-04-16 | 📥 800 → 📤 1200 = 🔢 2000 tokens (~$0.016800)
🤖 [14:35:10] Generación de Mapa Mental | o3-mini-2025-01-31 | 📥 600 → 📤 2500 = 🔢 3100 tokens (~$0.031800)
```

### **2. ✅ Actividades Monitoreadas**
- **Conversaciones/Q&A** - Preguntas y respuestas
- **Generación de Tests** - Con cantidad de preguntas
- **Generación de Flashcards** - Con cantidad de tarjetas
- **Generación de Mapas Mentales** - Mapas interactivos
- **Generación de Planes de Estudios** - Planificación personalizada

### **3. ✅ Datos Capturados**
- **Tokens de entrada** (prompt)
- **Tokens de salida** (respuesta)
- **Total de tokens**
- **Modelo utilizado**
- **Costo estimado** en USD
- **Timestamp** de la operación
- **Tipo de actividad**

## 🔧 Arquitectura del Sistema

### **Archivo Principal: `src/lib/ai/tokenTracker.ts`**

#### **Funciones Principales:**
- `logTokenUsage()` - Registra uso en consola y localStorage
- `createOpenAITokenTracking()` - Para modelos OpenAI (datos reales)
- `createGeminiTokenTracking()` - Para Gemini (estimación)
- `calculateEstimatedCost()` - Calcula costos basado en precios públicos
- `getTokenUsageStats()` - Obtiene estadísticas acumuladas

#### **Precios por Modelo (por 1K tokens):**
```typescript
'gpt-4o': { input: $0.0025, output: $0.01 }
'gpt-4o-mini': { input: $0.00015, output: $0.0006 }
'o1-mini': { input: $0.003, output: $0.012 }
'o3-mini': { input: $0.003, output: $0.012 }
'gemini': { input: $0.000125, output: $0.000375 }
```

### **Integración en Endpoints**

#### **1. API AI (`src/app/api/ai/route.ts`)**
- Tracking para todas las acciones de IA
- Nombres descriptivos por actividad
- Datos reales de uso de OpenAI

#### **2. Cliente OpenAI (`src/lib/openai/openaiClient.ts`)**
- Tracking automático en `llamarOpenAI()`
- Datos reales de uso de OpenAI
- Soporte para modelos de razonamiento

#### **3. Servicios Específicos**
- `questionService.ts` - Conversaciones
- `mindMapGenerator.ts` - Mapas mentales
- `flashcardGenerator.ts` - Flashcards
- `testGenerator.ts` - Tests
- `planGeneratorService.ts` - Planes de estudios

## 📊 Interfaz de Usuario

### **Botón de Estadísticas**
- **Ubicación**: Header de la aplicación
- **Icono**: 📊 (FiBarChart3)
- **Función**: Abre modal con estadísticas

### **Modal de Estadísticas (`TokenStatsModal.tsx`)**
- **Resumen general**: Total sesiones, tokens, costo
- **Por actividad**: Desglose detallado
- **Por modelo**: Uso por modelo de IA
- **Datos locales**: Almacenados en localStorage

## 🔍 Ejemplos de Output

### **Terminal/Consola:**
```bash
🤖 [14:32:15] Conversación/Q&A | o4-mini-2025-04-16 | 📥 150 → 📤 300 = 🔢 450 tokens (~$0.002100)
🤖 [14:33:42] Generación de Test (5 preguntas) | o4-mini-2025-04-16 | 📥 800 → 📤 1200 = 🔢 2000 tokens (~$0.016800)
🤖 [14:35:10] Generación de Mapa Mental | o3-mini-2025-01-31 | 📥 600 → 📤 2500 = 🔢 3100 tokens (~$0.031800)
🤖 [14:36:25] Generación de Flashcards (10 tarjetas) | o4-mini-2025-04-16 | 📥 400 → 📤 800 = 🔢 1200 tokens (~$0.010800)
🤖 [14:38:50] Generación de Plan de Estudios | o4-mini-2025-04-16 | 📥 1500 → 📤 5000 = 🔢 6500 tokens (~$0.034500)
```

### **Modal de Estadísticas:**
```
📊 Estadísticas de Uso de IA

Resumen General:
- Total Sesiones: 25
- Total Tokens: 45,230
- Costo Estimado: $0.234567

Por Actividad:
- Conversación/Q&A: 12 sesiones, 8,450 tokens, $0.045123
- Generación de Test: 5 sesiones, 15,200 tokens, $0.089234
- Generación de Mapas: 3 sesiones, 12,800 tokens, $0.067890
- Generación de Flashcards: 3 sesiones, 5,600 tokens, $0.023456
- Plan de Estudios: 2 sesiones, 13,180 tokens, $0.078864

Por Modelo:
- o4-mini-2025-04-16: 20 sesiones, 35,430 tokens, $0.189234
- o3-mini-2025-01-31: 5 sesiones, 9,800 tokens, $0.045333
```

## 💾 Almacenamiento de Datos

### **localStorage**
- **Clave**: `ai-token-logs`
- **Formato**: Array de objetos TokenTrackingData
- **Límite**: 100 registros más recientes
- **Persistencia**: Local al navegador

### **Estructura de Datos:**
```typescript
interface TokenTrackingData {
  activity: string;           // "Conversación/Q&A"
  model: string;             // "o4-mini-2025-04-16"
  usage: {
    promptTokens: number;    // 150
    completionTokens: number; // 300
    totalTokens: number;     // 450
    estimatedCost: number;   // 0.002100
  };
  timestamp: Date;           // 2024-01-15T14:32:15.000Z
  userId?: string;           // ID del usuario (opcional)
}
```

## 🎯 Beneficios del Sistema

### **Para Desarrollo:**
- ✅ **Visibilidad completa** del uso de IA
- ✅ **Identificación** de funciones costosas
- ✅ **Optimización** de prompts
- ✅ **Análisis** de patrones de uso

### **Para Usuarios:**
- ✅ **Transparencia** en el uso de IA
- ✅ **Estadísticas** personales
- ✅ **Conciencia** del costo por actividad

### **Para el Negocio:**
- ✅ **Estimación** de costos operativos
- ✅ **Planificación** de presupuestos
- ✅ **Análisis** de ROI por funcionalidad

## 🔧 Archivos Modificados

1. **`src/lib/ai/tokenTracker.ts`** - Sistema principal de tracking
2. **`src/app/api/ai/route.ts`** - Tracking en endpoint principal
3. **`src/lib/openai/openaiClient.ts`** - Tracking automático en cliente
4. **`src/lib/gemini/questionService.ts`** - Nombre de actividad
5. **`src/lib/gemini/mindMapGenerator.ts`** - Nombre de actividad
6. **`src/lib/gemini/flashcardGenerator.ts`** - Nombre de actividad
7. **`src/lib/gemini/testGenerator.ts`** - Nombre de actividad
8. **`src/features/planificacion/services/planGeneratorService.ts`** - Nombre de actividad
9. **`src/components/ui/TokenStatsModal.tsx`** (NUEVO) - Modal de estadísticas
10. **`src/app/app/page.tsx`** - Botón y modal

## ✅ Verificación

Para verificar que funciona:

1. **Usar cualquier función de IA** (conversación, test, flashcard, etc.)
2. **Revisar la consola** del navegador
3. **Ver logs** con formato: `🤖 [tiempo] actividad | modelo | tokens | costo`
4. **Abrir estadísticas** desde el botón 📊 en el header
5. **Verificar datos** acumulados en el modal

El sistema ahora proporciona **visibilidad completa** del uso y costo de IA en tiempo real.
