"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n// src/middleware.ts\n// Middleware robusto de seguridad para OposiAI (VERSIÓN FINAL COMPLETA Y CORREGIDA)\n\n\n// Configuración de rutas y permisos\nconst ROUTE_PERMISSIONS = {\n    // Rutas públicas (no requieren autenticación)\n    public: [\n        '/',\n        '/login',\n        '/payment',\n        '/thank-you',\n        '/contact',\n        '/privacy',\n        '/terms',\n        '/auth/callback',\n        '/auth/unauthorized',\n        '/auth/reset-password',\n        '/auth/confirm-reset',\n        '/api/auth/register-free',\n        '/api/stripe/webhook',\n        '/api/stripe/create-checkout-session',\n        '/api/stripe/create-token-checkout',\n        '/api/notify-signup',\n        '/api/user/status',\n        '/api/health'\n    ],\n    // Rutas que requieren autenticación básica\n    authenticated: [\n        '/app',\n        '/dashboard',\n        '/profile',\n        '/welcome',\n        '/upgrade-plan'\n    ],\n    // Rutas que requieren planes específicos\n    planRestricted: {\n        '/plan-estudios': [\n            'pro'\n        ],\n        '/app/ai-tutor': [\n            'usuario',\n            'pro'\n        ],\n        '/app/summaries': [\n            'pro'\n        ],\n        '/app/advanced-features': [\n            'pro'\n        ]\n    }\n};\n// Configuración de seguridad\nconst SECURITY_CONFIG = {\n    enableStrictValidation: process.env.STRICT_PLAN_VALIDATION === 'true',\n    requirePaymentVerification: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    enableAccessLogging: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    sessionTimeout: 5 * 60 * 1000 // 5 minutos en millisegundos\n};\nasync function middleware(request) {\n    const startTime = Date.now();\n    const { pathname } = request.nextUrl;\n    console.log(`\\n🚀 [MIDDLEWARE START] Path: ${pathname}`);\n    // ====================================================================\n    //  PASO 1: BYPASS EXPLÍCITO PARA RUTAS DE AUTENTICACIÓN\n    //  Esto permite que las páginas de callback y reseteo de contraseña\n    //  manejen la lógica de sesión en el lado del cliente sin interferencia.\n    // ====================================================================\n    const authBypassRoutes = [\n        '/auth/callback',\n        '/auth/reset-password',\n        '/auth/confirm-reset'\n    ];\n    if (authBypassRoutes.some((route)=>pathname.startsWith(route))) {\n        console.log(`[MW_BYPASS] Permitiendo paso a ${pathname} para manejo de autenticación en el cliente.`);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    }\n    try {\n        let supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request\n        });\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            auth: {\n                persistSession: false,\n                autoRefreshToken: false,\n                detectSessionInUrl: true\n            },\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll (cookiesToSet) {\n                    cookiesToSet.forEach(({ name, value, options })=>request.cookies.set(name, value));\n                    supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request\n                    });\n                    cookiesToSet.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, options));\n                }\n            }\n        });\n        const { data: { user } } = await supabase.auth.getUser();\n        // Si el usuario está autenticado y visita una ruta pública principal, redirigir a la app\n        if (user && (pathname === '/' || pathname === '/login')) {\n            console.log(`[MW_REDIRECT] Usuario autenticado en ${pathname}. Redirigiendo a /app.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/app', request.url));\n        }\n        // Si la ruta es pública (y no es uno de los casos de arriba), permitir el acceso\n        if (isPublicRoute(pathname)) {\n            console.log(`[MW_ALLOW] Ruta pública ${pathname}. Permitiendo acceso.`);\n            return addSecurityHeaders(supabaseResponse);\n        }\n        // Si la ruta no es pública y no hay usuario, redirigir a login\n        if (!user) {\n            console.log(`[MW_DENY] Ruta protegida ${pathname} sin usuario. Redirigiendo a /login.`);\n            return redirectToLogin(request);\n        }\n        // A partir de aquí, el usuario está autenticado y en una ruta protegida\n        const profileValidation = await validateUserProfile(user.id, supabase);\n        if (!profileValidation.valid) {\n            console.log(`[MW_DENY] Perfil de usuario inválido. Razón: ${profileValidation.reason}.`);\n            // Lógica de recuperación de perfil\n            if (profileValidation.reason === 'Profile not found') {\n                const isLegitimateUser = await checkIfLegitimateUser(user, supabase);\n                if (isLegitimateUser.isLegitimate) {\n                    try {\n                        await createRecoveryProfile(user.id, isLegitimateUser.accountType, supabase);\n                        console.log(`✅ [MIDDLEWARE] Perfil de recuperación creado para ${user.id}.`);\n                    // Continuar tras crear el perfil\n                    } catch (error) {\n                        console.error('❌ [MIDDLEWARE] Error creando perfil de recuperación:', error);\n                        return redirectToPayment(request, 'Profile recovery failed');\n                    }\n                } else {\n                    console.log(`🚨 [MIDDLEWARE] Usuario sospechoso sin perfil: ${user.id}`);\n                    return redirectToPayment(request, 'Invalid account - please complete registration');\n                }\n            } else {\n                return redirectToPayment(request, profileValidation.reason);\n            }\n        }\n        const planValidation = await validatePlanAccess(user.id, pathname, supabase);\n        if (!planValidation.valid) {\n            console.log(`[MW_DENY] Acceso denegado por plan. Razón: ${planValidation.reason}.`);\n            return redirectToUnauthorized(request, planValidation);\n        }\n        console.log(`[MW_ALLOW] Acceso permitido a la ruta protegida ${pathname} para el usuario ${user.id}.`);\n        return addSecurityHeaders(supabaseResponse);\n    } catch (error) {\n        console.error('❌ [MIDDLEWARE] Critical error:', error);\n        return redirectToLogin(request);\n    }\n}\n// ====================================================================\n//  FUNCIONES AUXILIARES\n// ====================================================================\nfunction isPublicRoute(pathname) {\n    return ROUTE_PERMISSIONS.public.some((path)=>pathname.startsWith(path));\n}\nfunction isAuthenticatedRoute(pathname) {\n    return ROUTE_PERMISSIONS.authenticated.some((path)=>pathname.startsWith(path));\n}\nfunction isPlanRestrictedRoute(pathname) {\n    return Object.keys(ROUTE_PERMISSIONS.planRestricted).some((path)=>pathname.startsWith(path));\n}\nasync function checkIfLegitimateUser(user, supabase) {\n    try {\n        const userMetadata = user.user_metadata || {};\n        const appMetadata = user.app_metadata || {};\n        if (userMetadata.created_via === 'free_registration' || userMetadata.free_account) {\n            return {\n                isLegitimate: true,\n                accountType: 'free',\n                reason: 'Free account registration'\n            };\n        }\n        if (userMetadata.plan) {\n            const accountType = userMetadata.plan === 'free' ? 'free' : 'paid';\n            return {\n                isLegitimate: true,\n                accountType,\n                reason: 'User in setup process'\n            };\n        }\n        if (userMetadata.stripe_session_id || userMetadata.stripe_customer_id) {\n            return {\n                isLegitimate: true,\n                accountType: 'paid',\n                reason: 'Has Stripe metadata'\n            };\n        }\n        const { data: transactions } = await supabase.from('stripe_transactions').select('id').eq('user_id', user.id).limit(1);\n        if (transactions && transactions.length > 0) {\n            return {\n                isLegitimate: true,\n                accountType: 'paid',\n                reason: 'Has payment transactions'\n            };\n        }\n        if (appMetadata.created_by_admin || userMetadata.invited_by_admin) {\n            return {\n                isLegitimate: true,\n                accountType: 'free',\n                reason: 'Admin created account'\n            };\n        }\n        return {\n            isLegitimate: false,\n            accountType: 'unknown',\n            reason: 'No legitimate registration method found'\n        };\n    } catch (error) {\n        console.error('❌ [MIDDLEWARE] Error checking user legitimacy:', error);\n        return {\n            isLegitimate: false,\n            accountType: 'unknown',\n            reason: 'Verification error'\n        };\n    }\n}\nasync function createRecoveryProfile(userId, accountType, supabase) {\n    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n    let profileData;\n    if (accountType === 'free') {\n        const expirationDate = new Date();\n        expirationDate.setDate(expirationDate.getDate() + 5);\n        profileData = {\n            user_id: userId,\n            subscription_plan: 'free',\n            monthly_token_limit: 50000,\n            current_month_tokens: 0,\n            current_month: currentMonth,\n            payment_verified: true,\n            plan_expires_at: expirationDate.toISOString(),\n            security_flags: {\n                recovered_profile: true,\n                recovery_date: new Date().toISOString(),\n                original_account_type: 'free'\n            }\n        };\n    } else {\n        profileData = {\n            user_id: userId,\n            subscription_plan: 'free',\n            monthly_token_limit: 50000,\n            current_month_tokens: 0,\n            current_month: currentMonth,\n            payment_verified: false,\n            security_flags: {\n                recovered_profile: true,\n                recovery_date: new Date().toISOString(),\n                original_account_type: 'paid',\n                requires_manual_verification: true\n            }\n        };\n    }\n    const { error } = await supabase.from('user_profiles').insert([\n        profileData\n    ]);\n    if (error) throw error;\n}\nasync function validateUserProfile(userId, supabase) {\n    try {\n        const { data: profile, error } = await supabase.from('user_profiles').select('subscription_plan, payment_verified, plan_expires_at').eq('user_id', userId).single();\n        if (error || !profile) return {\n            valid: false,\n            reason: 'Profile not found'\n        };\n        if (profile.plan_expires_at && new Date() > new Date(profile.plan_expires_at)) return {\n            valid: false,\n            reason: 'Account expired'\n        };\n        if (SECURITY_CONFIG.requirePaymentVerification && profile.subscription_plan !== 'free' && !profile.payment_verified) return {\n            valid: false,\n            reason: 'Payment not verified'\n        };\n        return {\n            valid: true\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            reason: 'Validation error'\n        };\n    }\n}\nasync function validatePlanAccess(userId, pathname, supabase) {\n    try {\n        const requiredPlans = Object.entries(ROUTE_PERMISSIONS.planRestricted).find(([path])=>pathname.startsWith(path))?.[1];\n        if (!requiredPlans) return {\n            valid: true\n        };\n        const { data: profile } = await supabase.from('user_profiles').select('subscription_plan').eq('user_id', userId).single();\n        if (!profile || !requiredPlans.includes(profile.subscription_plan)) {\n            return {\n                valid: false,\n                reason: `Plan ${profile?.subscription_plan || 'inválido'} not sufficient`,\n                requiredPlans\n            };\n        }\n        return {\n            valid: true\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            reason: 'Plan validation error',\n            requiredPlans: []\n        };\n    }\n}\nfunction redirectToLogin(request) {\n    const url = request.nextUrl.clone();\n    url.pathname = '/login';\n    url.searchParams.set('redirect', request.nextUrl.pathname);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n}\nfunction redirectToPayment(request, reason) {\n    const url = request.nextUrl.clone();\n    url.pathname = '/payment';\n    if (reason) url.searchParams.set('reason', reason);\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n}\nfunction redirectToUnauthorized(request, validation) {\n    const url = request.nextUrl.clone();\n    url.pathname = '/auth/unauthorized';\n    url.searchParams.set('reason', validation.reason || 'Access denied');\n    url.searchParams.set('feature', request.nextUrl.pathname);\n    if (validation.requiredPlans) url.searchParams.set('required_plan', validation.requiredPlans.join(','));\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n}\nfunction addSecurityHeaders(response) {\n    response.headers.set('X-Frame-Options', 'DENY');\n    response.headers.set('X-Content-Type-Options', 'nosniff');\n    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n    response.headers.set('X-XSS-Protection', '1; mode=block');\n    response.headers.set('Content-Security-Policy', \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://d3js.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.stripe.com;\");\n    return response;\n}\nconst config = {\n    matcher: [\n        '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\\\..*).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFFa0Q7QUFDVTtBQUU1RDtBQUNBLE1BQU1FLGlCQUFpQixHQUFHO0lBQ3hCO0lBQ0FDLE1BQU0sRUFBRTtRQUNOLEdBQUc7UUFDSCxRQUFRO1FBQ1IsVUFBVTtRQUNWLFlBQVk7UUFDWixVQUFVO1FBQ1YsVUFBVTtRQUNWLFFBQVE7UUFDUixnQkFBZ0I7UUFDaEIsb0JBQW9CO1FBQ3BCLHNCQUFzQjtRQUN0QixxQkFBcUI7UUFDckIseUJBQXlCO1FBQ3pCLHFCQUFxQjtRQUNyQixxQ0FBcUM7UUFDckMsbUNBQW1DO1FBQ25DLG9CQUFvQjtRQUNwQixrQkFBa0I7UUFDbEIsYUFBYTtLQUNkO0lBRUQ7SUFDQUMsYUFBYSxFQUFFO1FBQ2IsTUFBTTtRQUNOLFlBQVk7UUFDWixVQUFVO1FBQ1YsVUFBVTtRQUNWLGVBQWU7S0FDaEI7SUFFRDtJQUNBQyxjQUFjLEVBQUU7UUFDZCxnQkFBZ0IsRUFBRTtZQUFDLEtBQUs7U0FBQztRQUN6QixlQUFlLEVBQUU7WUFBQyxTQUFTO1lBQUUsS0FBSztTQUFDO1FBQ25DLGdCQUFnQixFQUFFO1lBQUMsS0FBSztTQUFDO1FBQ3pCLHdCQUF3QixFQUFFO1lBQUMsS0FBSztTQUFBO0lBQ2xDO0FBQ0YsQ0FBQztBQUVEO0FBQ0EsTUFBTUMsZUFBZSxHQUFHO0lBQ3RCQyxzQkFBc0IsRUFBRUMsT0FBTyxDQUFDQyxHQUFHLENBQUNDLHNCQUFzQixLQUFLLE1BQU07SUFDckVDLDBCQUEwQixFQUFFSCxPQUFPLENBQUNDLEdBQUcsQ0FBQ0csNEJBQTRCLEtBQUssTUFBTTtJQUMvRUMsbUJBQW1CLEVBQUVMLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDSyxxQkFBcUIsS0FBSyxNQUFNO0lBQ2pFQyxjQUFjLEVBQUUsQ0FBQyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUU7QUFDakMsQ0FBQztBQUVNLGVBQWVDLFVBQVVBLENBQUNDLE9BQW9CLEVBQUU7SUFDckQsTUFBTUMsU0FBUyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO0lBQzVCLE1BQU0sRUFBRUMsUUFBQUEsRUFBVSxHQUFHSixPQUFPLENBQUNLLE9BQU87SUFFcENDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFFLGlDQUFnQ0gsUUFBUyxFQUFDLENBQUM7SUFFeEQ7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBLE1BQU1JLGdCQUFnQixHQUFHO1FBQUMsZ0JBQWdCO1FBQUUsc0JBQXNCO1FBQUUscUJBQXFCO0tBQUM7SUFDMUYsSUFBSUEsZ0JBQWdCLENBQUNDLElBQUksRUFBQ0MsS0FBSyxHQUFJTixRQUFRLENBQUNPLFVBQVUsQ0FBQ0QsS0FBSyxDQUFDLENBQUMsRUFBRTtRQUM5REosT0FBTyxDQUFDQyxHQUFHLENBQUUsa0NBQWlDSCxRQUFTLDhDQUE2QyxDQUFDO1FBQ3JHLE9BQU9wQixxREFBWSxDQUFDNEIsSUFBSSxDQUFDLENBQUM7SUFDNUI7SUFFQSxJQUFJO1FBQ0YsSUFBSUMsZ0JBQWdCLEdBQUc3QixxREFBWSxDQUFDNEIsSUFBSSxDQUFDO1lBQUVaO1FBQVEsQ0FBQyxDQUFDO1FBRXJELE1BQU1jLFFBQVEsR0FBRy9CLGlFQUFrQixDQUNqQ1EsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7WUFDRTBCLElBQUksRUFBRTtnQkFDSkMsY0FBYyxFQUFFLEtBQUs7Z0JBQ3JCQyxnQkFBZ0IsRUFBRSxLQUFLO2dCQUN2QkMsa0JBQWtCLEVBQUU7WUFDdEIsQ0FBQztZQUNEQyxPQUFPLEVBQUU7Z0JBQ1BDLE1BQU1BLENBQUEsRUFBRztvQkFDUCxPQUFPdEIsT0FBTyxDQUFDcUIsT0FBTyxDQUFDQyxNQUFNLENBQUMsQ0FBQztnQkFDakMsQ0FBQztnQkFDREMsTUFBTUEsRUFBQ0MsWUFBWSxFQUFFO29CQUNuQkEsWUFBWSxDQUFDQyxPQUFPLENBQUMsQ0FBQyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsT0FBQUEsRUFBUyxHQUFLNUIsT0FBTyxDQUFDcUIsT0FBTyxDQUFDUSxHQUFHLENBQUNILElBQUksRUFBRUMsS0FBSyxDQUFDLENBQUM7b0JBQ3BGZCxnQkFBZ0IsR0FBRzdCLHFEQUFZLENBQUM0QixJQUFJLENBQUM7d0JBQUVaO29CQUFRLENBQUMsQ0FBQztvQkFDakR3QixZQUFZLENBQUNDLE9BQU8sQ0FBQyxDQUFDLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxPQUFBQSxFQUFTLEdBQUtmLGdCQUFnQixDQUFDUSxPQUFPLENBQUNRLEdBQUcsQ0FBQ0gsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLE9BQU8sQ0FBQyxDQUFDO2dCQUN4RztZQUNGO1FBQ0YsQ0FDRixDQUFDO1FBRUQsTUFBTSxFQUFFRSxJQUFJLEVBQUUsRUFBRUMsSUFBQUEsRUFBSyxFQUFHLEdBQUcsTUFBTWpCLFFBQVEsQ0FBQ0csSUFBSSxDQUFDZSxPQUFPLENBQUMsQ0FBQztRQUV4RDtRQUNBLElBQUlELElBQUksS0FBSzNCLFFBQVEsS0FBSyxHQUFHLElBQUlBLFFBQVEsS0FBSyxTQUFRLENBQUMsQ0FBRTtZQUN2REUsT0FBTyxDQUFDQyxHQUFHLENBQUUsd0NBQXVDSCxRQUFTLHdCQUF1QixDQUFDO1lBQ3JGLE9BQU9wQixxREFBWSxDQUFDaUQsUUFBUSxDQUFDLElBQUlDLEdBQUcsQ0FBQyxNQUFNLEVBQUVsQyxPQUFPLENBQUNtQyxHQUFHLENBQUMsQ0FBQztRQUM1RDtRQUVBO1FBQ0EsSUFBSUMsYUFBYSxDQUFDaEMsUUFBUSxDQUFDLEVBQUU7WUFDM0JFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFFLDJCQUEwQkgsUUFBUyx1QkFBc0IsQ0FBQztZQUN2RSxPQUFPaUMsa0JBQWtCLENBQUN4QixnQkFBZ0IsQ0FBQztRQUM3QztRQUVBO1FBQ0EsSUFBSSxDQUFDa0IsSUFBSSxFQUFFO1lBQ1R6QixPQUFPLENBQUNDLEdBQUcsQ0FBRSw0QkFBMkJILFFBQVMsc0NBQXFDLENBQUM7WUFDdkYsT0FBT2tDLGVBQWUsQ0FBQ3RDLE9BQU8sQ0FBQztRQUNqQztRQUVBO1FBQ0EsTUFBTXVDLGlCQUFpQixHQUFHLE1BQU1DLG1CQUFtQixDQUFDVCxJQUFJLENBQUNVLEVBQUUsRUFBRTNCLFFBQVEsQ0FBQztRQUN0RSxJQUFJLENBQUN5QixpQkFBaUIsQ0FBQ0csS0FBSyxFQUFFO1lBQzVCcEMsT0FBTyxDQUFDQyxHQUFHLENBQUUsZ0RBQStDZ0MsaUJBQWlCLENBQUNJLE1BQU8sR0FBRSxDQUFDO1lBRXhGO1lBQ0EsSUFBSUosaUJBQWlCLENBQUNJLE1BQU0sS0FBSyxtQkFBbUIsRUFBRTtnQkFDcEQsTUFBTUMsZ0JBQWdCLEdBQUcsTUFBTUMscUJBQXFCLENBQUNkLElBQUksRUFBRWpCLFFBQVEsQ0FBQztnQkFDcEUsSUFBSThCLGdCQUFnQixDQUFDRSxZQUFZLEVBQUU7b0JBQ2pDLElBQUk7d0JBQ0YsTUFBTUMscUJBQXFCLENBQUNoQixJQUFJLENBQUNVLEVBQUUsRUFBRUcsZ0JBQWdCLENBQUNJLFdBQVcsRUFBRWxDLFFBQVEsQ0FBQzt3QkFDNUVSLE9BQU8sQ0FBQ0MsR0FBRyxDQUFFLHFEQUFvRHdCLElBQUksQ0FBQ1UsRUFBRyxHQUFFLENBQUM7b0JBQzVFO29CQUNGLENBQUMsQ0FBQyxPQUFPUSxLQUFLLEVBQUU7d0JBQ2QzQyxPQUFPLENBQUMyQyxLQUFLLENBQUMsc0RBQXNELEVBQUVBLEtBQUssQ0FBQzt3QkFDNUUsT0FBT0MsaUJBQWlCLENBQUNsRCxPQUFPLEVBQUUseUJBQXlCLENBQUM7b0JBQzlEO2dCQUNGLENBQUMsTUFBTTtvQkFDTE0sT0FBTyxDQUFDQyxHQUFHLENBQUUsa0RBQWlEd0IsSUFBSSxDQUFDVSxFQUFHLEVBQUMsQ0FBQztvQkFDeEUsT0FBT1MsaUJBQWlCLENBQUNsRCxPQUFPLEVBQUUsZ0RBQWdELENBQUM7Z0JBQ3JGO1lBQ0YsQ0FBQyxNQUFNO2dCQUNMLE9BQU9rRCxpQkFBaUIsQ0FBQ2xELE9BQU8sRUFBRXVDLGlCQUFpQixDQUFDSSxNQUFNLENBQUM7WUFDN0Q7UUFDRjtRQUVBLE1BQU1RLGNBQWMsR0FBRyxNQUFNQyxrQkFBa0IsQ0FBQ3JCLElBQUksQ0FBQ1UsRUFBRSxFQUFFckMsUUFBUSxFQUFFVSxRQUFRLENBQUM7UUFDNUUsSUFBSSxDQUFDcUMsY0FBYyxDQUFDVCxLQUFLLEVBQUU7WUFDekJwQyxPQUFPLENBQUNDLEdBQUcsQ0FBRSw4Q0FBNkM0QyxjQUFjLENBQUNSLE1BQU8sR0FBRSxDQUFDO1lBQ25GLE9BQU9VLHNCQUFzQixDQUFDckQsT0FBTyxFQUFFbUQsY0FBYyxDQUFDO1FBQ3hEO1FBRUE3QyxPQUFPLENBQUNDLEdBQUcsQ0FBRSxtREFBa0RILFFBQVMsb0JBQW1CMkIsSUFBSSxDQUFDVSxFQUFHLEdBQUUsQ0FBQztRQUN0RyxPQUFPSixrQkFBa0IsQ0FBQ3hCLGdCQUFnQixDQUFDO0lBRTdDLENBQUMsQ0FBQyxPQUFPb0MsS0FBSyxFQUFFO1FBQ2QzQyxPQUFPLENBQUMyQyxLQUFLLENBQUMsZ0NBQWdDLEVBQUVBLEtBQUssQ0FBQztRQUN0RCxPQUFPWCxlQUFlLENBQUN0QyxPQUFPLENBQUM7SUFDakM7QUFDRjtBQUVBO0FBQ0E7QUFDQTtBQUVBLFNBQVNvQyxhQUFhQSxDQUFDaEMsUUFBZ0IsRUFBVztJQUNoRCxPQUFPbkIsaUJBQWlCLENBQUNDLE1BQU0sQ0FBQ3VCLElBQUksRUFBQzZDLElBQUksR0FBSWxELFFBQVEsQ0FBQ08sVUFBVSxDQUFDMkMsSUFBSSxDQUFDLENBQUM7QUFDekU7QUFFQSxTQUFTQyxvQkFBb0JBLENBQUNuRCxRQUFnQixFQUFXO0lBQ3ZELE9BQU9uQixpQkFBaUIsQ0FBQ0UsYUFBYSxDQUFDc0IsSUFBSSxFQUFDNkMsSUFBSSxHQUFJbEQsUUFBUSxDQUFDTyxVQUFVLENBQUMyQyxJQUFJLENBQUMsQ0FBQztBQUNoRjtBQUVBLFNBQVNFLHFCQUFxQkEsQ0FBQ3BELFFBQWdCLEVBQVc7SUFDeEQsT0FBT3FELE1BQU0sQ0FBQ0MsSUFBSSxDQUFDekUsaUJBQWlCLENBQUNHLGNBQWMsQ0FBQyxDQUFDcUIsSUFBSSxFQUFDNkMsSUFBSSxHQUFJbEQsUUFBUSxDQUFDTyxVQUFVLENBQUMyQyxJQUFJLENBQUMsQ0FBQztBQUM5RjtBQUVBLGVBQWVULHFCQUFxQkEsQ0FBQ2QsSUFBUyxFQUFFakIsUUFBYSxFQUFrRztJQUM3SixJQUFJO1FBQ0YsTUFBTTZDLFlBQVksR0FBRzVCLElBQUksQ0FBQzZCLGFBQWEsSUFBSSxDQUFDLENBQUM7UUFDN0MsTUFBTUMsV0FBVyxHQUFHOUIsSUFBSSxDQUFDK0IsWUFBWSxJQUFJLENBQUMsQ0FBQztRQUUzQyxJQUFJSCxZQUFZLENBQUNJLFdBQVcsS0FBSyxtQkFBbUIsSUFBSUosWUFBWSxDQUFDSyxZQUFZLEVBQUU7WUFDakYsT0FBTztnQkFBRWxCLFlBQVksRUFBRSxJQUFJO2dCQUFFRSxXQUFXLEVBQUUsTUFBTTtnQkFBRUwsTUFBTSxFQUFFO1lBQTRCLENBQUM7UUFDekY7UUFDQSxJQUFJZ0IsWUFBWSxDQUFDTSxJQUFJLEVBQUU7WUFDckIsTUFBTWpCLFdBQVcsR0FBR1csWUFBWSxDQUFDTSxJQUFJLEtBQUssTUFBTSxHQUFHLE1BQU0sR0FBRyxNQUFNO1lBQ2xFLE9BQU87Z0JBQUVuQixZQUFZLEVBQUUsSUFBSTtnQkFBRUUsV0FBVztnQkFBRUwsTUFBTSxFQUFFO1lBQXdCLENBQUM7UUFDN0U7UUFDQSxJQUFJZ0IsWUFBWSxDQUFDTyxpQkFBaUIsSUFBSVAsWUFBWSxDQUFDUSxrQkFBa0IsRUFBRTtZQUNyRSxPQUFPO2dCQUFFckIsWUFBWSxFQUFFLElBQUk7Z0JBQUVFLFdBQVcsRUFBRSxNQUFNO2dCQUFFTCxNQUFNLEVBQUU7WUFBc0IsQ0FBQztRQUNuRjtRQUNBLE1BQU0sRUFBRWIsSUFBSSxFQUFFc0MsWUFBQUEsRUFBYyxHQUFHLE1BQU10RCxRQUFRLENBQUN1RCxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDQyxFQUFFLENBQUMsU0FBUyxFQUFFeEMsSUFBSSxDQUFDVSxFQUFFLENBQUMsQ0FBQytCLEtBQUssQ0FBQyxDQUFDLENBQUM7UUFDdEgsSUFBSUosWUFBWSxJQUFJQSxZQUFZLENBQUNLLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDM0MsT0FBTztnQkFBRTNCLFlBQVksRUFBRSxJQUFJO2dCQUFFRSxXQUFXLEVBQUUsTUFBTTtnQkFBRUwsTUFBTSxFQUFFO1lBQTJCLENBQUM7UUFDeEY7UUFDQSxJQUFJa0IsV0FBVyxDQUFDYSxnQkFBZ0IsSUFBSWYsWUFBWSxDQUFDZ0IsZ0JBQWdCLEVBQUU7WUFDakUsT0FBTztnQkFBRTdCLFlBQVksRUFBRSxJQUFJO2dCQUFFRSxXQUFXLEVBQUUsTUFBTTtnQkFBRUwsTUFBTSxFQUFFO1lBQXdCLENBQUM7UUFDckY7UUFDQSxPQUFPO1lBQUVHLFlBQVksRUFBRSxLQUFLO1lBQUVFLFdBQVcsRUFBRSxTQUFTO1lBQUVMLE1BQU0sRUFBRTtRQUEwQyxDQUFDO0lBQzNHLENBQUMsQ0FBQyxPQUFPTSxLQUFLLEVBQUU7UUFDZDNDLE9BQU8sQ0FBQzJDLEtBQUssQ0FBQyxnREFBZ0QsRUFBRUEsS0FBSyxDQUFDO1FBQ3RFLE9BQU87WUFBRUgsWUFBWSxFQUFFLEtBQUs7WUFBRUUsV0FBVyxFQUFFLFNBQVM7WUFBRUwsTUFBTSxFQUFFO1FBQXFCLENBQUM7SUFDdEY7QUFDRjtBQUVBLGVBQWVJLHFCQUFxQkEsQ0FBQzZCLE1BQWMsRUFBRTVCLFdBQXdDLEVBQUVsQyxRQUFhLEVBQWlCO0lBQzNILE1BQU0rRCxZQUFZLEdBQUcsSUFBSTNFLElBQUksQ0FBQyxDQUFDLENBQUM0RSxXQUFXLENBQUMsQ0FBQyxDQUFDQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEtBQUs7SUFDakUsSUFBSUMsV0FBVztJQUNmLElBQUloQyxXQUFXLEtBQUssTUFBTSxFQUFFO1FBQzFCLE1BQU1pQyxjQUFjLEdBQUcsSUFBSS9FLElBQUksQ0FBQyxDQUFDO1FBQ2pDK0UsY0FBYyxDQUFDQyxPQUFPLENBQUNELGNBQWMsQ0FBQ0UsT0FBTyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDcERILFdBQVcsR0FBRztZQUNaSSxPQUFPLEVBQUVSLE1BQU07WUFDZlMsaUJBQWlCLEVBQUUsTUFBTTtZQUN6QkMsbUJBQW1CLEVBQUUsS0FBSztZQUMxQkMsb0JBQW9CLEVBQUUsQ0FBQztZQUN2QkMsYUFBYSxFQUFFWCxZQUFZO1lBQzNCWSxnQkFBZ0IsRUFBRSxJQUFJO1lBQ3RCQyxlQUFlLEVBQUVULGNBQWMsQ0FBQ0gsV0FBVyxDQUFDLENBQUM7WUFDN0NhLGNBQWMsRUFBRTtnQkFBRUMsaUJBQWlCLEVBQUUsSUFBSTtnQkFBRUMsYUFBYSxFQUFFLElBQUkzRixJQUFJLENBQUMsQ0FBQyxDQUFDNEUsV0FBVyxDQUFDLENBQUM7Z0JBQUVnQixxQkFBcUIsRUFBRTtZQUFPO1FBQ3BILENBQUM7SUFDSCxDQUFDLE1BQU07UUFDTGQsV0FBVyxHQUFHO1lBQ1pJLE9BQU8sRUFBRVIsTUFBTTtZQUNmUyxpQkFBaUIsRUFBRSxNQUFNO1lBQ3pCQyxtQkFBbUIsRUFBRSxLQUFLO1lBQzFCQyxvQkFBb0IsRUFBRSxDQUFDO1lBQ3ZCQyxhQUFhLEVBQUVYLFlBQVk7WUFDM0JZLGdCQUFnQixFQUFFLEtBQUs7WUFDdkJFLGNBQWMsRUFBRTtnQkFBRUMsaUJBQWlCLEVBQUUsSUFBSTtnQkFBRUMsYUFBYSxFQUFFLElBQUkzRixJQUFJLENBQUMsQ0FBQyxDQUFDNEUsV0FBVyxDQUFDLENBQUM7Z0JBQUVnQixxQkFBcUIsRUFBRSxNQUFNO2dCQUFFQyw0QkFBNEIsRUFBRTtZQUFLO1FBQ3hKLENBQUM7SUFDSDtJQUNBLE1BQU0sRUFBRTlDLEtBQUFBLEVBQU8sR0FBRyxNQUFNbkMsUUFBUSxDQUFDdUQsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDMkIsTUFBTSxDQUFDO1FBQUNoQixXQUFXO0tBQUMsQ0FBQztJQUM1RSxJQUFJL0IsS0FBSyxFQUFFLE1BQU1BLEtBQUs7QUFDeEI7QUFFQSxlQUFlVCxtQkFBbUJBLENBQUNvQyxNQUFjLEVBQUU5RCxRQUFhLEVBQWlEO0lBQy9HLElBQUk7UUFDRixNQUFNLEVBQUVnQixJQUFJLEVBQUVtRSxPQUFPLEVBQUVoRCxLQUFBQSxFQUFPLEdBQUcsTUFBTW5DLFFBQVEsQ0FBQ3VELElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLHNEQUFzRCxDQUFDLENBQUNDLEVBQUUsQ0FBQyxTQUFTLEVBQUVLLE1BQU0sQ0FBQyxDQUFDc0IsTUFBTSxDQUFDLENBQUM7UUFDbkssSUFBSWpELEtBQUssSUFBSSxDQUFDZ0QsT0FBTyxFQUFFLE9BQU87WUFBRXZELEtBQUssRUFBRSxLQUFLO1lBQUVDLE1BQU0sRUFBRTtRQUFvQixDQUFDO1FBQzNFLElBQUlzRCxPQUFPLENBQUNQLGVBQWUsSUFBSSxJQUFJeEYsSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJQSxJQUFJLENBQUMrRixPQUFPLENBQUNQLGVBQWUsQ0FBQyxFQUFFLE9BQU87WUFBRWhELEtBQUssRUFBRSxLQUFLO1lBQUVDLE1BQU0sRUFBRTtRQUFrQixDQUFDO1FBQ2pJLElBQUl0RCxlQUFlLENBQUNLLDBCQUEwQixJQUFJdUcsT0FBTyxDQUFDWixpQkFBaUIsS0FBSyxNQUFNLElBQUksQ0FBQ1ksT0FBTyxDQUFDUixnQkFBZ0IsRUFBRSxPQUFPO1lBQUUvQyxLQUFLLEVBQUUsS0FBSztZQUFFQyxNQUFNLEVBQUU7UUFBdUIsQ0FBQztRQUM1SyxPQUFPO1lBQUVELEtBQUssRUFBRTtRQUFLLENBQUM7SUFDeEIsQ0FBQyxDQUFDLE9BQU9PLEtBQUssRUFBRTtRQUNkLE9BQU87WUFBRVAsS0FBSyxFQUFFLEtBQUs7WUFBRUMsTUFBTSxFQUFFO1FBQW1CLENBQUM7SUFDckQ7QUFDRjtBQUVBLGVBQWVTLGtCQUFrQkEsQ0FBQ3dCLE1BQWMsRUFBRXhFLFFBQWdCLEVBQUVVLFFBQWEsRUFBMkU7SUFDMUosSUFBSTtRQUNGLE1BQU1xRixhQUFhLEdBQUcxQyxNQUFNLENBQUMyQyxPQUFPLENBQUNuSCxpQkFBaUIsQ0FBQ0csY0FBYyxDQUFDLENBQUNpSCxJQUFJLENBQUMsQ0FBQyxDQUFDL0MsSUFBSSxDQUFDLEdBQUtsRCxRQUFRLENBQUNPLFVBQVUsQ0FBQzJDLElBQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ3ZILElBQUksQ0FBQzZDLGFBQWEsRUFBRSxPQUFPO1lBQUV6RCxLQUFLLEVBQUU7UUFBSyxDQUFDO1FBQzFDLE1BQU0sRUFBRVosSUFBSSxFQUFFbUUsT0FBQUEsRUFBUyxHQUFHLE1BQU1uRixRQUFRLENBQUN1RCxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUNDLE1BQU0sQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDQyxFQUFFLENBQUMsU0FBUyxFQUFFSyxNQUFNLENBQUMsQ0FBQ3NCLE1BQU0sQ0FBQyxDQUFDO1FBQ3pILElBQUksQ0FBQ0QsT0FBTyxJQUFJLENBQUNFLGFBQWEsQ0FBQ0csUUFBUSxDQUFDTCxPQUFPLENBQUNaLGlCQUFpQixDQUFDLEVBQUU7WUFDbEUsT0FBTztnQkFBRTNDLEtBQUssRUFBRSxLQUFLO2dCQUFFQyxNQUFNLEVBQUcsUUFBT3NELE9BQU8sRUFBRVosaUJBQWlCLElBQUksVUFBVyxpQkFBZ0I7Z0JBQUVjO1lBQWMsQ0FBQztRQUNuSDtRQUNBLE9BQU87WUFBRXpELEtBQUssRUFBRTtRQUFLLENBQUM7SUFDeEIsQ0FBQyxDQUFDLE9BQU9PLEtBQUssRUFBRTtRQUNkLE9BQU87WUFBRVAsS0FBSyxFQUFFLEtBQUs7WUFBRUMsTUFBTSxFQUFFLHVCQUF1QjtZQUFFd0QsYUFBYSxFQUFFO1FBQUcsQ0FBQztJQUM3RTtBQUNGO0FBRUEsU0FBUzdELGVBQWVBLENBQUN0QyxPQUFvQixFQUFnQjtJQUMzRCxNQUFNbUMsR0FBRyxHQUFHbkMsT0FBTyxDQUFDSyxPQUFPLENBQUNrRyxLQUFLLENBQUMsQ0FBQztJQUNuQ3BFLEdBQUcsQ0FBQy9CLFFBQVEsR0FBRyxRQUFRO0lBQ3ZCK0IsR0FBRyxDQUFDcUUsWUFBWSxDQUFDM0UsR0FBRyxDQUFDLFVBQVUsRUFBRTdCLE9BQU8sQ0FBQ0ssT0FBTyxDQUFDRCxRQUFRLENBQUM7SUFDMUQsT0FBT3BCLHFEQUFZLENBQUNpRCxRQUFRLENBQUNFLEdBQUcsQ0FBQztBQUNuQztBQUVBLFNBQVNlLGlCQUFpQkEsQ0FBQ2xELE9BQW9CLEVBQUUyQyxNQUFlLEVBQWdCO0lBQzlFLE1BQU1SLEdBQUcsR0FBR25DLE9BQU8sQ0FBQ0ssT0FBTyxDQUFDa0csS0FBSyxDQUFDLENBQUM7SUFDbkNwRSxHQUFHLENBQUMvQixRQUFRLEdBQUcsVUFBVTtJQUN6QixJQUFJdUMsTUFBTSxFQUFFUixHQUFHLENBQUNxRSxZQUFZLENBQUMzRSxHQUFHLENBQUMsUUFBUSxFQUFFYyxNQUFNLENBQUM7SUFDbEQsT0FBTzNELHFEQUFZLENBQUNpRCxRQUFRLENBQUNFLEdBQUcsQ0FBQztBQUNuQztBQUVBLFNBQVNrQixzQkFBc0JBLENBQUNyRCxPQUFvQixFQUFFeUcsVUFBZSxFQUFnQjtJQUNuRixNQUFNdEUsR0FBRyxHQUFHbkMsT0FBTyxDQUFDSyxPQUFPLENBQUNrRyxLQUFLLENBQUMsQ0FBQztJQUNuQ3BFLEdBQUcsQ0FBQy9CLFFBQVEsR0FBRyxvQkFBb0I7SUFDbkMrQixHQUFHLENBQUNxRSxZQUFZLENBQUMzRSxHQUFHLENBQUMsUUFBUSxFQUFFNEUsVUFBVSxDQUFDOUQsTUFBTSxJQUFJLGVBQWUsQ0FBQztJQUNwRVIsR0FBRyxDQUFDcUUsWUFBWSxDQUFDM0UsR0FBRyxDQUFDLFNBQVMsRUFBRTdCLE9BQU8sQ0FBQ0ssT0FBTyxDQUFDRCxRQUFRLENBQUM7SUFDekQsSUFBSXFHLFVBQVUsQ0FBQ04sYUFBYSxFQUFFaEUsR0FBRyxDQUFDcUUsWUFBWSxDQUFDM0UsR0FBRyxDQUFDLGVBQWUsRUFBRTRFLFVBQVUsQ0FBQ04sYUFBYSxDQUFDTyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7SUFDdkcsT0FBTzFILHFEQUFZLENBQUNpRCxRQUFRLENBQUNFLEdBQUcsQ0FBQztBQUNuQztBQUVBLFNBQVNFLGtCQUFrQkEsQ0FBQ3NFLFFBQXNCLEVBQWdCO0lBQ2hFQSxRQUFRLENBQUNDLE9BQU8sQ0FBQy9FLEdBQUcsQ0FBQyxpQkFBaUIsRUFBRSxNQUFNLENBQUM7SUFDL0M4RSxRQUFRLENBQUNDLE9BQU8sQ0FBQy9FLEdBQUcsQ0FBQyx3QkFBd0IsRUFBRSxTQUFTLENBQUM7SUFDekQ4RSxRQUFRLENBQUNDLE9BQU8sQ0FBQy9FLEdBQUcsQ0FBQyxpQkFBaUIsRUFBRSxpQ0FBaUMsQ0FBQztJQUMxRThFLFFBQVEsQ0FBQ0MsT0FBTyxDQUFDL0UsR0FBRyxDQUFDLGtCQUFrQixFQUFFLGVBQWUsQ0FBQztJQUN6RDhFLFFBQVEsQ0FBQ0MsT0FBTyxDQUFDL0UsR0FBRyxDQUNsQix5QkFBeUIsRUFDekIsNk9BQ0YsQ0FBQztJQUNELE9BQU84RSxRQUFRO0FBQ2pCO0FBRU8sTUFBTUUsTUFBTSxHQUFHO0lBQ3BCQyxPQUFPLEVBQUU7UUFDUCxnRkFBZ0Y7S0FBQTtBQUVwRixDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTNcXHNyY1xcbWlkZGxld2FyZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvbWlkZGxld2FyZS50c1xuLy8gTWlkZGxld2FyZSByb2J1c3RvIGRlIHNlZ3VyaWRhZCBwYXJhIE9wb3NpQUkgKFZFUlNJw5NOIEZJTkFMIENPTVBMRVRBIFkgQ09SUkVHSURBKVxuXG5pbXBvcnQgeyBjcmVhdGVTZXJ2ZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcbmltcG9ydCB7IE5leHRSZXNwb25zZSwgdHlwZSBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcblxuLy8gQ29uZmlndXJhY2nDs24gZGUgcnV0YXMgeSBwZXJtaXNvc1xuY29uc3QgUk9VVEVfUEVSTUlTU0lPTlMgPSB7XG4gIC8vIFJ1dGFzIHDDumJsaWNhcyAobm8gcmVxdWllcmVuIGF1dGVudGljYWNpw7NuKVxuICBwdWJsaWM6IFtcbiAgICAnLycsXG4gICAgJy9sb2dpbicsXG4gICAgJy9wYXltZW50JyxcbiAgICAnL3RoYW5rLXlvdScsXG4gICAgJy9jb250YWN0JyxcbiAgICAnL3ByaXZhY3knLFxuICAgICcvdGVybXMnLFxuICAgICcvYXV0aC9jYWxsYmFjaycsXG4gICAgJy9hdXRoL3VuYXV0aG9yaXplZCcsXG4gICAgJy9hdXRoL3Jlc2V0LXBhc3N3b3JkJyxcbiAgICAnL2F1dGgvY29uZmlybS1yZXNldCcsXG4gICAgJy9hcGkvYXV0aC9yZWdpc3Rlci1mcmVlJyxcbiAgICAnL2FwaS9zdHJpcGUvd2ViaG9vaycsXG4gICAgJy9hcGkvc3RyaXBlL2NyZWF0ZS1jaGVja291dC1zZXNzaW9uJyxcbiAgICAnL2FwaS9zdHJpcGUvY3JlYXRlLXRva2VuLWNoZWNrb3V0JyxcbiAgICAnL2FwaS9ub3RpZnktc2lnbnVwJyxcbiAgICAnL2FwaS91c2VyL3N0YXR1cycsXG4gICAgJy9hcGkvaGVhbHRoJ1xuICBdLFxuXG4gIC8vIFJ1dGFzIHF1ZSByZXF1aWVyZW4gYXV0ZW50aWNhY2nDs24gYsOhc2ljYVxuICBhdXRoZW50aWNhdGVkOiBbXG4gICAgJy9hcHAnLFxuICAgICcvZGFzaGJvYXJkJyxcbiAgICAnL3Byb2ZpbGUnLFxuICAgICcvd2VsY29tZScsXG4gICAgJy91cGdyYWRlLXBsYW4nXG4gIF0sXG5cbiAgLy8gUnV0YXMgcXVlIHJlcXVpZXJlbiBwbGFuZXMgZXNwZWPDrWZpY29zXG4gIHBsYW5SZXN0cmljdGVkOiB7XG4gICAgJy9wbGFuLWVzdHVkaW9zJzogWydwcm8nXSxcbiAgICAnL2FwcC9haS10dXRvcic6IFsndXN1YXJpbycsICdwcm8nXSxcbiAgICAnL2FwcC9zdW1tYXJpZXMnOiBbJ3BybyddLFxuICAgICcvYXBwL2FkdmFuY2VkLWZlYXR1cmVzJzogWydwcm8nXVxuICB9XG59O1xuXG4vLyBDb25maWd1cmFjacOzbiBkZSBzZWd1cmlkYWRcbmNvbnN0IFNFQ1VSSVRZX0NPTkZJRyA9IHtcbiAgZW5hYmxlU3RyaWN0VmFsaWRhdGlvbjogcHJvY2Vzcy5lbnYuU1RSSUNUX1BMQU5fVkFMSURBVElPTiA9PT0gJ3RydWUnLFxuICByZXF1aXJlUGF5bWVudFZlcmlmaWNhdGlvbjogcHJvY2Vzcy5lbnYuUkVRVUlSRV9QQVlNRU5UX1ZFUklGSUNBVElPTiA9PT0gJ3RydWUnLFxuICBlbmFibGVBY2Nlc3NMb2dnaW5nOiBwcm9jZXNzLmVudi5FTkFCTEVfQUNDRVNTX0xPR0dJTkcgPT09ICd0cnVlJyxcbiAgc2Vzc2lvblRpbWVvdXQ6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRvcyBlbiBtaWxsaXNlZ3VuZG9zXG59O1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbWlkZGxld2FyZShyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICBjb25zdCB7IHBhdGhuYW1lIH0gPSByZXF1ZXN0Lm5leHRVcmw7XG5cbiAgY29uc29sZS5sb2coYFxcbvCfmoAgW01JRERMRVdBUkUgU1RBUlRdIFBhdGg6ICR7cGF0aG5hbWV9YCk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgLy8gIFBBU08gMTogQllQQVNTIEVYUEzDjUNJVE8gUEFSQSBSVVRBUyBERSBBVVRFTlRJQ0FDScOTTlxuICAvLyAgRXN0byBwZXJtaXRlIHF1ZSBsYXMgcMOhZ2luYXMgZGUgY2FsbGJhY2sgeSByZXNldGVvIGRlIGNvbnRyYXNlw7FhXG4gIC8vICBtYW5lamVuIGxhIGzDs2dpY2EgZGUgc2VzacOzbiBlbiBlbCBsYWRvIGRlbCBjbGllbnRlIHNpbiBpbnRlcmZlcmVuY2lhLlxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICBjb25zdCBhdXRoQnlwYXNzUm91dGVzID0gWycvYXV0aC9jYWxsYmFjaycsICcvYXV0aC9yZXNldC1wYXNzd29yZCcsICcvYXV0aC9jb25maXJtLXJlc2V0J107XG4gIGlmIChhdXRoQnlwYXNzUm91dGVzLnNvbWUocm91dGUgPT4gcGF0aG5hbWUuc3RhcnRzV2l0aChyb3V0ZSkpKSB7XG4gICAgY29uc29sZS5sb2coYFtNV19CWVBBU1NdIFBlcm1pdGllbmRvIHBhc28gYSAke3BhdGhuYW1lfSBwYXJhIG1hbmVqbyBkZSBhdXRlbnRpY2FjacOzbiBlbiBlbCBjbGllbnRlLmApO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UubmV4dCgpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBsZXQgc3VwYWJhc2VSZXNwb25zZSA9IE5leHRSZXNwb25zZS5uZXh0KHsgcmVxdWVzdCB9KTtcblxuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmVyQ2xpZW50KFxuICAgICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICAgIHtcbiAgICAgICAgYXV0aDoge1xuICAgICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZSxcbiAgICAgICAgICBhdXRvUmVmcmVzaFRva2VuOiBmYWxzZSxcbiAgICAgICAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWVcbiAgICAgICAgfSxcbiAgICAgICAgY29va2llczoge1xuICAgICAgICAgIGdldEFsbCgpIHtcbiAgICAgICAgICAgIHJldHVybiByZXF1ZXN0LmNvb2tpZXMuZ2V0QWxsKCk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgICBjb29raWVzVG9TZXQuZm9yRWFjaCgoeyBuYW1lLCB2YWx1ZSwgb3B0aW9ucyB9KSA9PiByZXF1ZXN0LmNvb2tpZXMuc2V0KG5hbWUsIHZhbHVlKSk7XG4gICAgICAgICAgICBzdXBhYmFzZVJlc3BvbnNlID0gTmV4dFJlc3BvbnNlLm5leHQoeyByZXF1ZXN0IH0pO1xuICAgICAgICAgICAgY29va2llc1RvU2V0LmZvckVhY2goKHsgbmFtZSwgdmFsdWUsIG9wdGlvbnMgfSkgPT4gc3VwYWJhc2VSZXNwb25zZS5jb29raWVzLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucykpO1xuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICB9XG4gICAgKTtcblxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIFxuICAgIC8vIFNpIGVsIHVzdWFyaW8gZXN0w6EgYXV0ZW50aWNhZG8geSB2aXNpdGEgdW5hIHJ1dGEgcMO6YmxpY2EgcHJpbmNpcGFsLCByZWRpcmlnaXIgYSBsYSBhcHBcbiAgICBpZiAodXNlciAmJiAocGF0aG5hbWUgPT09ICcvJyB8fCBwYXRobmFtZSA9PT0gJy9sb2dpbicpKSB7XG4gICAgICBjb25zb2xlLmxvZyhgW01XX1JFRElSRUNUXSBVc3VhcmlvIGF1dGVudGljYWRvIGVuICR7cGF0aG5hbWV9LiBSZWRpcmlnaWVuZG8gYSAvYXBwLmApO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKCcvYXBwJywgcmVxdWVzdC51cmwpKTtcbiAgICB9XG4gICAgXG4gICAgLy8gU2kgbGEgcnV0YSBlcyBww7pibGljYSAoeSBubyBlcyB1bm8gZGUgbG9zIGNhc29zIGRlIGFycmliYSksIHBlcm1pdGlyIGVsIGFjY2Vzb1xuICAgIGlmIChpc1B1YmxpY1JvdXRlKHBhdGhuYW1lKSkge1xuICAgICAgY29uc29sZS5sb2coYFtNV19BTExPV10gUnV0YSBww7pibGljYSAke3BhdGhuYW1lfS4gUGVybWl0aWVuZG8gYWNjZXNvLmApO1xuICAgICAgcmV0dXJuIGFkZFNlY3VyaXR5SGVhZGVycyhzdXBhYmFzZVJlc3BvbnNlKTtcbiAgICB9XG4gICAgXG4gICAgLy8gU2kgbGEgcnV0YSBubyBlcyBww7pibGljYSB5IG5vIGhheSB1c3VhcmlvLCByZWRpcmlnaXIgYSBsb2dpblxuICAgIGlmICghdXNlcikge1xuICAgICAgY29uc29sZS5sb2coYFtNV19ERU5ZXSBSdXRhIHByb3RlZ2lkYSAke3BhdGhuYW1lfSBzaW4gdXN1YXJpby4gUmVkaXJpZ2llbmRvIGEgL2xvZ2luLmApO1xuICAgICAgcmV0dXJuIHJlZGlyZWN0VG9Mb2dpbihyZXF1ZXN0KTtcbiAgICB9XG5cbiAgICAvLyBBIHBhcnRpciBkZSBhcXXDrSwgZWwgdXN1YXJpbyBlc3TDoSBhdXRlbnRpY2FkbyB5IGVuIHVuYSBydXRhIHByb3RlZ2lkYVxuICAgIGNvbnN0IHByb2ZpbGVWYWxpZGF0aW9uID0gYXdhaXQgdmFsaWRhdGVVc2VyUHJvZmlsZSh1c2VyLmlkLCBzdXBhYmFzZSk7XG4gICAgaWYgKCFwcm9maWxlVmFsaWRhdGlvbi52YWxpZCkge1xuICAgICAgY29uc29sZS5sb2coYFtNV19ERU5ZXSBQZXJmaWwgZGUgdXN1YXJpbyBpbnbDoWxpZG8uIFJhesOzbjogJHtwcm9maWxlVmFsaWRhdGlvbi5yZWFzb259LmApO1xuICAgICAgXG4gICAgICAvLyBMw7NnaWNhIGRlIHJlY3VwZXJhY2nDs24gZGUgcGVyZmlsXG4gICAgICBpZiAocHJvZmlsZVZhbGlkYXRpb24ucmVhc29uID09PSAnUHJvZmlsZSBub3QgZm91bmQnKSB7XG4gICAgICAgIGNvbnN0IGlzTGVnaXRpbWF0ZVVzZXIgPSBhd2FpdCBjaGVja0lmTGVnaXRpbWF0ZVVzZXIodXNlciwgc3VwYWJhc2UpO1xuICAgICAgICBpZiAoaXNMZWdpdGltYXRlVXNlci5pc0xlZ2l0aW1hdGUpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXdhaXQgY3JlYXRlUmVjb3ZlcnlQcm9maWxlKHVzZXIuaWQsIGlzTGVnaXRpbWF0ZVVzZXIuYWNjb3VudFR5cGUsIHN1cGFiYXNlKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgW01JRERMRVdBUkVdIFBlcmZpbCBkZSByZWN1cGVyYWNpw7NuIGNyZWFkbyBwYXJhICR7dXNlci5pZH0uYCk7XG4gICAgICAgICAgICAvLyBDb250aW51YXIgdHJhcyBjcmVhciBlbCBwZXJmaWxcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFtNSURETEVXQVJFXSBFcnJvciBjcmVhbmRvIHBlcmZpbCBkZSByZWN1cGVyYWNpw7NuOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiByZWRpcmVjdFRvUGF5bWVudChyZXF1ZXN0LCAnUHJvZmlsZSByZWNvdmVyeSBmYWlsZWQnKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCfmqggW01JRERMRVdBUkVdIFVzdWFyaW8gc29zcGVjaG9zbyBzaW4gcGVyZmlsOiAke3VzZXIuaWR9YCk7XG4gICAgICAgICAgcmV0dXJuIHJlZGlyZWN0VG9QYXltZW50KHJlcXVlc3QsICdJbnZhbGlkIGFjY291bnQgLSBwbGVhc2UgY29tcGxldGUgcmVnaXN0cmF0aW9uJyk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiByZWRpcmVjdFRvUGF5bWVudChyZXF1ZXN0LCBwcm9maWxlVmFsaWRhdGlvbi5yZWFzb24pO1xuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IHBsYW5WYWxpZGF0aW9uID0gYXdhaXQgdmFsaWRhdGVQbGFuQWNjZXNzKHVzZXIuaWQsIHBhdGhuYW1lLCBzdXBhYmFzZSk7XG4gICAgaWYgKCFwbGFuVmFsaWRhdGlvbi52YWxpZCkge1xuICAgICAgY29uc29sZS5sb2coYFtNV19ERU5ZXSBBY2Nlc28gZGVuZWdhZG8gcG9yIHBsYW4uIFJhesOzbjogJHtwbGFuVmFsaWRhdGlvbi5yZWFzb259LmApO1xuICAgICAgcmV0dXJuIHJlZGlyZWN0VG9VbmF1dGhvcml6ZWQocmVxdWVzdCwgcGxhblZhbGlkYXRpb24pO1xuICAgIH1cbiAgICBcbiAgICBjb25zb2xlLmxvZyhgW01XX0FMTE9XXSBBY2Nlc28gcGVybWl0aWRvIGEgbGEgcnV0YSBwcm90ZWdpZGEgJHtwYXRobmFtZX0gcGFyYSBlbCB1c3VhcmlvICR7dXNlci5pZH0uYCk7XG4gICAgcmV0dXJuIGFkZFNlY3VyaXR5SGVhZGVycyhzdXBhYmFzZVJlc3BvbnNlKTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTUlERExFV0FSRV0gQ3JpdGljYWwgZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiByZWRpcmVjdFRvTG9naW4ocmVxdWVzdCk7XG4gIH1cbn1cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vICBGVU5DSU9ORVMgQVVYSUxJQVJFU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuZnVuY3Rpb24gaXNQdWJsaWNSb3V0ZShwYXRobmFtZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBST1VURV9QRVJNSVNTSU9OUy5wdWJsaWMuc29tZShwYXRoID0+IHBhdGhuYW1lLnN0YXJ0c1dpdGgocGF0aCkpO1xufVxuXG5mdW5jdGlvbiBpc0F1dGhlbnRpY2F0ZWRSb3V0ZShwYXRobmFtZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiBST1VURV9QRVJNSVNTSU9OUy5hdXRoZW50aWNhdGVkLnNvbWUocGF0aCA9PiBwYXRobmFtZS5zdGFydHNXaXRoKHBhdGgpKTtcbn1cblxuZnVuY3Rpb24gaXNQbGFuUmVzdHJpY3RlZFJvdXRlKHBhdGhuYW1lOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgcmV0dXJuIE9iamVjdC5rZXlzKFJPVVRFX1BFUk1JU1NJT05TLnBsYW5SZXN0cmljdGVkKS5zb21lKHBhdGggPT4gcGF0aG5hbWUuc3RhcnRzV2l0aChwYXRoKSk7XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGNoZWNrSWZMZWdpdGltYXRlVXNlcih1c2VyOiBhbnksIHN1cGFiYXNlOiBhbnkpOiBQcm9taXNlPHsgaXNMZWdpdGltYXRlOiBib29sZWFuOyBhY2NvdW50VHlwZTogJ2ZyZWUnIHwgJ3BhaWQnIHwgJ3Vua25vd24nOyByZWFzb24/OiBzdHJpbmc7IH0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB1c2VyTWV0YWRhdGEgPSB1c2VyLnVzZXJfbWV0YWRhdGEgfHwge307XG4gICAgY29uc3QgYXBwTWV0YWRhdGEgPSB1c2VyLmFwcF9tZXRhZGF0YSB8fCB7fTtcblxuICAgIGlmICh1c2VyTWV0YWRhdGEuY3JlYXRlZF92aWEgPT09ICdmcmVlX3JlZ2lzdHJhdGlvbicgfHwgdXNlck1ldGFkYXRhLmZyZWVfYWNjb3VudCkge1xuICAgICAgcmV0dXJuIHsgaXNMZWdpdGltYXRlOiB0cnVlLCBhY2NvdW50VHlwZTogJ2ZyZWUnLCByZWFzb246ICdGcmVlIGFjY291bnQgcmVnaXN0cmF0aW9uJyB9O1xuICAgIH1cbiAgICBpZiAodXNlck1ldGFkYXRhLnBsYW4pIHtcbiAgICAgIGNvbnN0IGFjY291bnRUeXBlID0gdXNlck1ldGFkYXRhLnBsYW4gPT09ICdmcmVlJyA/ICdmcmVlJyA6ICdwYWlkJztcbiAgICAgIHJldHVybiB7IGlzTGVnaXRpbWF0ZTogdHJ1ZSwgYWNjb3VudFR5cGUsIHJlYXNvbjogJ1VzZXIgaW4gc2V0dXAgcHJvY2VzcycgfTtcbiAgICB9XG4gICAgaWYgKHVzZXJNZXRhZGF0YS5zdHJpcGVfc2Vzc2lvbl9pZCB8fCB1c2VyTWV0YWRhdGEuc3RyaXBlX2N1c3RvbWVyX2lkKSB7XG4gICAgICByZXR1cm4geyBpc0xlZ2l0aW1hdGU6IHRydWUsIGFjY291bnRUeXBlOiAncGFpZCcsIHJlYXNvbjogJ0hhcyBTdHJpcGUgbWV0YWRhdGEnIH07XG4gICAgfVxuICAgIGNvbnN0IHsgZGF0YTogdHJhbnNhY3Rpb25zIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKCdzdHJpcGVfdHJhbnNhY3Rpb25zJykuc2VsZWN0KCdpZCcpLmVxKCd1c2VyX2lkJywgdXNlci5pZCkubGltaXQoMSk7XG4gICAgaWYgKHRyYW5zYWN0aW9ucyAmJiB0cmFuc2FjdGlvbnMubGVuZ3RoID4gMCkge1xuICAgICAgcmV0dXJuIHsgaXNMZWdpdGltYXRlOiB0cnVlLCBhY2NvdW50VHlwZTogJ3BhaWQnLCByZWFzb246ICdIYXMgcGF5bWVudCB0cmFuc2FjdGlvbnMnIH07XG4gICAgfVxuICAgIGlmIChhcHBNZXRhZGF0YS5jcmVhdGVkX2J5X2FkbWluIHx8IHVzZXJNZXRhZGF0YS5pbnZpdGVkX2J5X2FkbWluKSB7XG4gICAgICByZXR1cm4geyBpc0xlZ2l0aW1hdGU6IHRydWUsIGFjY291bnRUeXBlOiAnZnJlZScsIHJlYXNvbjogJ0FkbWluIGNyZWF0ZWQgYWNjb3VudCcgfTtcbiAgICB9XG4gICAgcmV0dXJuIHsgaXNMZWdpdGltYXRlOiBmYWxzZSwgYWNjb3VudFR5cGU6ICd1bmtub3duJywgcmVhc29uOiAnTm8gbGVnaXRpbWF0ZSByZWdpc3RyYXRpb24gbWV0aG9kIGZvdW5kJyB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbTUlERExFV0FSRV0gRXJyb3IgY2hlY2tpbmcgdXNlciBsZWdpdGltYWN5OicsIGVycm9yKTtcbiAgICByZXR1cm4geyBpc0xlZ2l0aW1hdGU6IGZhbHNlLCBhY2NvdW50VHlwZTogJ3Vua25vd24nLCByZWFzb246ICdWZXJpZmljYXRpb24gZXJyb3InIH07XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gY3JlYXRlUmVjb3ZlcnlQcm9maWxlKHVzZXJJZDogc3RyaW5nLCBhY2NvdW50VHlwZTogJ2ZyZWUnIHwgJ3BhaWQnIHwgJ3Vua25vd24nLCBzdXBhYmFzZTogYW55KTogUHJvbWlzZTx2b2lkPiB7XG4gIGNvbnN0IGN1cnJlbnRNb250aCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCA3KSArICctMDEnO1xuICBsZXQgcHJvZmlsZURhdGE7XG4gIGlmIChhY2NvdW50VHlwZSA9PT0gJ2ZyZWUnKSB7XG4gICAgY29uc3QgZXhwaXJhdGlvbkRhdGUgPSBuZXcgRGF0ZSgpO1xuICAgIGV4cGlyYXRpb25EYXRlLnNldERhdGUoZXhwaXJhdGlvbkRhdGUuZ2V0RGF0ZSgpICsgNSk7XG4gICAgcHJvZmlsZURhdGEgPSB7XG4gICAgICB1c2VyX2lkOiB1c2VySWQsXG4gICAgICBzdWJzY3JpcHRpb25fcGxhbjogJ2ZyZWUnLFxuICAgICAgbW9udGhseV90b2tlbl9saW1pdDogNTAwMDAsXG4gICAgICBjdXJyZW50X21vbnRoX3Rva2VuczogMCxcbiAgICAgIGN1cnJlbnRfbW9udGg6IGN1cnJlbnRNb250aCxcbiAgICAgIHBheW1lbnRfdmVyaWZpZWQ6IHRydWUsXG4gICAgICBwbGFuX2V4cGlyZXNfYXQ6IGV4cGlyYXRpb25EYXRlLnRvSVNPU3RyaW5nKCksXG4gICAgICBzZWN1cml0eV9mbGFnczogeyByZWNvdmVyZWRfcHJvZmlsZTogdHJ1ZSwgcmVjb3ZlcnlfZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLCBvcmlnaW5hbF9hY2NvdW50X3R5cGU6ICdmcmVlJyB9XG4gICAgfTtcbiAgfSBlbHNlIHtcbiAgICBwcm9maWxlRGF0YSA9IHtcbiAgICAgIHVzZXJfaWQ6IHVzZXJJZCxcbiAgICAgIHN1YnNjcmlwdGlvbl9wbGFuOiAnZnJlZScsXG4gICAgICBtb250aGx5X3Rva2VuX2xpbWl0OiA1MDAwMCxcbiAgICAgIGN1cnJlbnRfbW9udGhfdG9rZW5zOiAwLFxuICAgICAgY3VycmVudF9tb250aDogY3VycmVudE1vbnRoLFxuICAgICAgcGF5bWVudF92ZXJpZmllZDogZmFsc2UsXG4gICAgICBzZWN1cml0eV9mbGFnczogeyByZWNvdmVyZWRfcHJvZmlsZTogdHJ1ZSwgcmVjb3ZlcnlfZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLCBvcmlnaW5hbF9hY2NvdW50X3R5cGU6ICdwYWlkJywgcmVxdWlyZXNfbWFudWFsX3ZlcmlmaWNhdGlvbjogdHJ1ZSB9XG4gICAgfTtcbiAgfVxuICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKCd1c2VyX3Byb2ZpbGVzJykuaW5zZXJ0KFtwcm9maWxlRGF0YV0pO1xuICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xufVxuXG5hc3luYyBmdW5jdGlvbiB2YWxpZGF0ZVVzZXJQcm9maWxlKHVzZXJJZDogc3RyaW5nLCBzdXBhYmFzZTogYW55KTogUHJvbWlzZTx7IHZhbGlkOiBib29sZWFuOyByZWFzb24/OiBzdHJpbmc7IH0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGRhdGE6IHByb2ZpbGUsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKCd1c2VyX3Byb2ZpbGVzJykuc2VsZWN0KCdzdWJzY3JpcHRpb25fcGxhbiwgcGF5bWVudF92ZXJpZmllZCwgcGxhbl9leHBpcmVzX2F0JykuZXEoJ3VzZXJfaWQnLCB1c2VySWQpLnNpbmdsZSgpO1xuICAgIGlmIChlcnJvciB8fCAhcHJvZmlsZSkgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCByZWFzb246ICdQcm9maWxlIG5vdCBmb3VuZCcgfTtcbiAgICBpZiAocHJvZmlsZS5wbGFuX2V4cGlyZXNfYXQgJiYgbmV3IERhdGUoKSA+IG5ldyBEYXRlKHByb2ZpbGUucGxhbl9leHBpcmVzX2F0KSkgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCByZWFzb246ICdBY2NvdW50IGV4cGlyZWQnIH07XG4gICAgaWYgKFNFQ1VSSVRZX0NPTkZJRy5yZXF1aXJlUGF5bWVudFZlcmlmaWNhdGlvbiAmJiBwcm9maWxlLnN1YnNjcmlwdGlvbl9wbGFuICE9PSAnZnJlZScgJiYgIXByb2ZpbGUucGF5bWVudF92ZXJpZmllZCkgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCByZWFzb246ICdQYXltZW50IG5vdCB2ZXJpZmllZCcgfTtcbiAgICByZXR1cm4geyB2YWxpZDogdHJ1ZSB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgcmVhc29uOiAnVmFsaWRhdGlvbiBlcnJvcicgfTtcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiB2YWxpZGF0ZVBsYW5BY2Nlc3ModXNlcklkOiBzdHJpbmcsIHBhdGhuYW1lOiBzdHJpbmcsIHN1cGFiYXNlOiBhbnkpOiBQcm9taXNlPHsgdmFsaWQ6IGJvb2xlYW47IHJlYXNvbj86IHN0cmluZzsgcmVxdWlyZWRQbGFucz86IHN0cmluZ1tdOyB9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVxdWlyZWRQbGFucyA9IE9iamVjdC5lbnRyaWVzKFJPVVRFX1BFUk1JU1NJT05TLnBsYW5SZXN0cmljdGVkKS5maW5kKChbcGF0aF0pID0+IHBhdGhuYW1lLnN0YXJ0c1dpdGgocGF0aCkpPy5bMV07XG4gICAgaWYgKCFyZXF1aXJlZFBsYW5zKSByZXR1cm4geyB2YWxpZDogdHJ1ZSB9O1xuICAgIGNvbnN0IHsgZGF0YTogcHJvZmlsZSB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbSgndXNlcl9wcm9maWxlcycpLnNlbGVjdCgnc3Vic2NyaXB0aW9uX3BsYW4nKS5lcSgndXNlcl9pZCcsIHVzZXJJZCkuc2luZ2xlKCk7XG4gICAgaWYgKCFwcm9maWxlIHx8ICFyZXF1aXJlZFBsYW5zLmluY2x1ZGVzKHByb2ZpbGUuc3Vic2NyaXB0aW9uX3BsYW4pKSB7XG4gICAgICByZXR1cm4geyB2YWxpZDogZmFsc2UsIHJlYXNvbjogYFBsYW4gJHtwcm9maWxlPy5zdWJzY3JpcHRpb25fcGxhbiB8fCAnaW52w6FsaWRvJ30gbm90IHN1ZmZpY2llbnRgLCByZXF1aXJlZFBsYW5zIH07XG4gICAgfVxuICAgIHJldHVybiB7IHZhbGlkOiB0cnVlIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCByZWFzb246ICdQbGFuIHZhbGlkYXRpb24gZXJyb3InLCByZXF1aXJlZFBsYW5zOiBbXSB9O1xuICB9XG59XG5cbmZ1bmN0aW9uIHJlZGlyZWN0VG9Mb2dpbihyZXF1ZXN0OiBOZXh0UmVxdWVzdCk6IE5leHRSZXNwb25zZSB7XG4gIGNvbnN0IHVybCA9IHJlcXVlc3QubmV4dFVybC5jbG9uZSgpO1xuICB1cmwucGF0aG5hbWUgPSAnL2xvZ2luJztcbiAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoJ3JlZGlyZWN0JywgcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lKTtcbiAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdCh1cmwpO1xufVxuXG5mdW5jdGlvbiByZWRpcmVjdFRvUGF5bWVudChyZXF1ZXN0OiBOZXh0UmVxdWVzdCwgcmVhc29uPzogc3RyaW5nKTogTmV4dFJlc3BvbnNlIHtcbiAgY29uc3QgdXJsID0gcmVxdWVzdC5uZXh0VXJsLmNsb25lKCk7XG4gIHVybC5wYXRobmFtZSA9ICcvcGF5bWVudCc7XG4gIGlmIChyZWFzb24pIHVybC5zZWFyY2hQYXJhbXMuc2V0KCdyZWFzb24nLCByZWFzb24pO1xuICByZXR1cm4gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHVybCk7XG59XG5cbmZ1bmN0aW9uIHJlZGlyZWN0VG9VbmF1dGhvcml6ZWQocmVxdWVzdDogTmV4dFJlcXVlc3QsIHZhbGlkYXRpb246IGFueSk6IE5leHRSZXNwb25zZSB7XG4gIGNvbnN0IHVybCA9IHJlcXVlc3QubmV4dFVybC5jbG9uZSgpO1xuICB1cmwucGF0aG5hbWUgPSAnL2F1dGgvdW5hdXRob3JpemVkJztcbiAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoJ3JlYXNvbicsIHZhbGlkYXRpb24ucmVhc29uIHx8ICdBY2Nlc3MgZGVuaWVkJyk7XG4gIHVybC5zZWFyY2hQYXJhbXMuc2V0KCdmZWF0dXJlJywgcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lKTtcbiAgaWYgKHZhbGlkYXRpb24ucmVxdWlyZWRQbGFucykgdXJsLnNlYXJjaFBhcmFtcy5zZXQoJ3JlcXVpcmVkX3BsYW4nLCB2YWxpZGF0aW9uLnJlcXVpcmVkUGxhbnMuam9pbignLCcpKTtcbiAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdCh1cmwpO1xufVxuXG5mdW5jdGlvbiBhZGRTZWN1cml0eUhlYWRlcnMocmVzcG9uc2U6IE5leHRSZXNwb25zZSk6IE5leHRSZXNwb25zZSB7XG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdYLUZyYW1lLU9wdGlvbnMnLCAnREVOWScpO1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1Db250ZW50LVR5cGUtT3B0aW9ucycsICdub3NuaWZmJyk7XG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdSZWZlcnJlci1Qb2xpY3knLCAnc3RyaWN0LW9yaWdpbi13aGVuLWNyb3NzLW9yaWdpbicpO1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1YU1MtUHJvdGVjdGlvbicsICcxOyBtb2RlPWJsb2NrJyk7XG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KFxuICAgICdDb250ZW50LVNlY3VyaXR5LVBvbGljeScsXG4gICAgXCJkZWZhdWx0LXNyYyAnc2VsZic7IHNjcmlwdC1zcmMgJ3NlbGYnICd1bnNhZmUtaW5saW5lJyAndW5zYWZlLWV2YWwnIGh0dHBzOi8vanMuc3RyaXBlLmNvbSBodHRwczovL2QzanMub3JnOyBzdHlsZS1zcmMgJ3NlbGYnICd1bnNhZmUtaW5saW5lJzsgaW1nLXNyYyAnc2VsZicgZGF0YTogaHR0cHM6OyBjb25uZWN0LXNyYyAnc2VsZicgaHR0cHM6Ly8qLnN1cGFiYXNlLmNvIGh0dHBzOi8vYXBpLnN0cmlwZS5jb207XCJcbiAgKTtcbiAgcmV0dXJuIHJlc3BvbnNlO1xufVxuXG5leHBvcnQgY29uc3QgY29uZmlnID0ge1xuICBtYXRjaGVyOiBbXG4gICAgJy8oKD8hX25leHQvc3RhdGljfF9uZXh0L2ltYWdlfGZhdmljb24uaWNvfG1hbmlmZXN0Lmpzb258cm9ib3RzLnR4dHwuKlxcXFwuLiopLiopJyxcbiAgXSxcbn07Il0sIm5hbWVzIjpbImNyZWF0ZVNlcnZlckNsaWVudCIsIk5leHRSZXNwb25zZSIsIlJPVVRFX1BFUk1JU1NJT05TIiwicHVibGljIiwiYXV0aGVudGljYXRlZCIsInBsYW5SZXN0cmljdGVkIiwiU0VDVVJJVFlfQ09ORklHIiwiZW5hYmxlU3RyaWN0VmFsaWRhdGlvbiIsInByb2Nlc3MiLCJlbnYiLCJTVFJJQ1RfUExBTl9WQUxJREFUSU9OIiwicmVxdWlyZVBheW1lbnRWZXJpZmljYXRpb24iLCJSRVFVSVJFX1BBWU1FTlRfVkVSSUZJQ0FUSU9OIiwiZW5hYmxlQWNjZXNzTG9nZ2luZyIsIkVOQUJMRV9BQ0NFU1NfTE9HR0lORyIsInNlc3Npb25UaW1lb3V0IiwibWlkZGxld2FyZSIsInJlcXVlc3QiLCJzdGFydFRpbWUiLCJEYXRlIiwibm93IiwicGF0aG5hbWUiLCJuZXh0VXJsIiwiY29uc29sZSIsImxvZyIsImF1dGhCeXBhc3NSb3V0ZXMiLCJzb21lIiwicm91dGUiLCJzdGFydHNXaXRoIiwibmV4dCIsInN1cGFiYXNlUmVzcG9uc2UiLCJzdXBhYmFzZSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiYXV0aCIsInBlcnNpc3RTZXNzaW9uIiwiYXV0b1JlZnJlc2hUb2tlbiIsImRldGVjdFNlc3Npb25JblVybCIsImNvb2tpZXMiLCJnZXRBbGwiLCJzZXRBbGwiLCJjb29raWVzVG9TZXQiLCJmb3JFYWNoIiwibmFtZSIsInZhbHVlIiwib3B0aW9ucyIsInNldCIsImRhdGEiLCJ1c2VyIiwiZ2V0VXNlciIsInJlZGlyZWN0IiwiVVJMIiwidXJsIiwiaXNQdWJsaWNSb3V0ZSIsImFkZFNlY3VyaXR5SGVhZGVycyIsInJlZGlyZWN0VG9Mb2dpbiIsInByb2ZpbGVWYWxpZGF0aW9uIiwidmFsaWRhdGVVc2VyUHJvZmlsZSIsImlkIiwidmFsaWQiLCJyZWFzb24iLCJpc0xlZ2l0aW1hdGVVc2VyIiwiY2hlY2tJZkxlZ2l0aW1hdGVVc2VyIiwiaXNMZWdpdGltYXRlIiwiY3JlYXRlUmVjb3ZlcnlQcm9maWxlIiwiYWNjb3VudFR5cGUiLCJlcnJvciIsInJlZGlyZWN0VG9QYXltZW50IiwicGxhblZhbGlkYXRpb24iLCJ2YWxpZGF0ZVBsYW5BY2Nlc3MiLCJyZWRpcmVjdFRvVW5hdXRob3JpemVkIiwicGF0aCIsImlzQXV0aGVudGljYXRlZFJvdXRlIiwiaXNQbGFuUmVzdHJpY3RlZFJvdXRlIiwiT2JqZWN0Iiwia2V5cyIsInVzZXJNZXRhZGF0YSIsInVzZXJfbWV0YWRhdGEiLCJhcHBNZXRhZGF0YSIsImFwcF9tZXRhZGF0YSIsImNyZWF0ZWRfdmlhIiwiZnJlZV9hY2NvdW50IiwicGxhbiIsInN0cmlwZV9zZXNzaW9uX2lkIiwic3RyaXBlX2N1c3RvbWVyX2lkIiwidHJhbnNhY3Rpb25zIiwiZnJvbSIsInNlbGVjdCIsImVxIiwibGltaXQiLCJsZW5ndGgiLCJjcmVhdGVkX2J5X2FkbWluIiwiaW52aXRlZF9ieV9hZG1pbiIsInVzZXJJZCIsImN1cnJlbnRNb250aCIsInRvSVNPU3RyaW5nIiwic2xpY2UiLCJwcm9maWxlRGF0YSIsImV4cGlyYXRpb25EYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJ1c2VyX2lkIiwic3Vic2NyaXB0aW9uX3BsYW4iLCJtb250aGx5X3Rva2VuX2xpbWl0IiwiY3VycmVudF9tb250aF90b2tlbnMiLCJjdXJyZW50X21vbnRoIiwicGF5bWVudF92ZXJpZmllZCIsInBsYW5fZXhwaXJlc19hdCIsInNlY3VyaXR5X2ZsYWdzIiwicmVjb3ZlcmVkX3Byb2ZpbGUiLCJyZWNvdmVyeV9kYXRlIiwib3JpZ2luYWxfYWNjb3VudF90eXBlIiwicmVxdWlyZXNfbWFudWFsX3ZlcmlmaWNhdGlvbiIsImluc2VydCIsInByb2ZpbGUiLCJzaW5nbGUiLCJyZXF1aXJlZFBsYW5zIiwiZW50cmllcyIsImZpbmQiLCJpbmNsdWRlcyIsImNsb25lIiwic2VhcmNoUGFyYW1zIiwidmFsaWRhdGlvbiIsImpvaW4iLCJyZXNwb25zZSIsImhlYWRlcnMiLCJjb25maWciLCJtYXRjaGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});