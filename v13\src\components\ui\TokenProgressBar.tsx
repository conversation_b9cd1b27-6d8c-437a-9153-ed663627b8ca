'use client';

import React from 'react';

interface TokenProgressBarProps {
  used: number;
  limit: number;
  percentage: number;
  remaining: number;
}

export default function TokenProgressBar({ used, limit, percentage, remaining }: TokenProgressBarProps) {
  // Determinar color según el porcentaje de uso
  const getProgressColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-500';
    if (percentage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getProgressBgColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-100';
    if (percentage < 80) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const formatTokens = (tokens: number) => {
    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`;
    }
    if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(1)}K`;
    }
    return tokens.toLocaleString();
  };

  return (
    <div className="space-y-3">
      {/* Título y porcentaje */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-700">Uso de Tokens</h3>
        <span className={`text-sm font-semibold ${
          percentage < 50 ? 'text-green-600' : 
          percentage < 80 ? 'text-yellow-600' : 
          'text-red-600'
        }`}>
          {percentage}%
        </span>
      </div>

      {/* Barra de progreso */}
      <div className={`w-full ${getProgressBgColor(percentage)} rounded-full h-3`}>
        <div
          className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(percentage)}`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>

      {/* Información detallada */}
      <div className="flex items-center justify-between text-xs text-gray-600">
        <span>
          <strong>{formatTokens(used)}</strong> usados
        </span>
        <span>
          <strong>{formatTokens(remaining)}</strong> restantes
        </span>
      </div>

      {/* Límite total */}
      <div className="text-center">
        <span className="text-xs text-gray-500">
          Límite mensual: <strong>{formatTokens(limit)}</strong> tokens
        </span>
      </div>

      {/* Advertencia si está cerca del límite */}
      {percentage >= 80 && (
        <div className={`p-2 rounded-lg text-xs ${
          percentage >= 95 ? 'bg-red-50 text-red-700 border border-red-200' :
          'bg-yellow-50 text-yellow-700 border border-yellow-200'
        }`}>
          {percentage >= 95 ? (
            <span>⚠️ Límite casi alcanzado. Considera comprar más tokens.</span>
          ) : (
            <span>⚠️ Te estás acercando al límite mensual de tokens.</span>
          )}
        </div>
      )}
    </div>
  );
}
