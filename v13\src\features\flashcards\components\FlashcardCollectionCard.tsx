import React from 'react';
import { FiBookOpen } from 'react-icons/fi';
// Tipo para ColeccionFlashcards
interface ColeccionFlashcards {
  id: string;
  nombre: string;
  titulo: string; // Alias para nombre
  descripcion?: string;
  user_id: string;
  creado_en: string;
  actualizado_en: string;
}
import { EstadisticasColeccion } from './types';

interface FlashcardCollectionCardProps {
  coleccion: ColeccionFlashcards;
  isSelected: boolean;
  onClick: () => void;
  estadisticas?: EstadisticasColeccion | null;
}

const FlashcardCollectionCard: React.FC<FlashcardCollectionCardProps> = ({ 
  coleccion, 
  isSelected, 
  onClick, 
  estadisticas 
}) => (
  <div
    onClick={onClick}
    className={`p-4 border rounded-xl cursor-pointer transition-all duration-200 ${
      isSelected
        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50/50'
    }`}
  >
    <div className="flex justify-between items-start">
      <h3 className="font-semibold text-gray-900">{coleccion.titulo}</h3>
      {estadisticas && (
        <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
          {estadisticas.paraHoy} para hoy
        </span>
      )}
    </div>

    {coleccion.descripcion && (
      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
        {coleccion.descripcion}
      </p>
    )}

    {estadisticas && (
      <div className="mt-3 pt-2 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
        <span className="flex items-center">
          <FiBookOpen className="mr-1" size={12} />
          {estadisticas.total} tarjetas
        </span>
        <div className="flex space-x-1">
          <span className="text-green-600">{estadisticas.aprendidas}✓</span>
          <span className="text-yellow-500">{estadisticas.aprendiendo}⏳</span>
          <span className="text-gray-400">{estadisticas.nuevas}+</span>
        </div>
      </div>
    )}
  </div>
);

export default FlashcardCollectionCard;
