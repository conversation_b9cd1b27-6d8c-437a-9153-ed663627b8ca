'use client';

import React, { useState, useEffect } from 'react';
import { FiX, FiBarChart, FiActivity, FiLock, FiArrowUp } from 'react-icons/fi';
import { getUserTokenStats, TokenUsageStats } from '@/lib/supabase/tokenUsageService';
import { usePlanLimits } from '@/hooks/usePlanLimits';
import TokenProgressBar from './TokenProgressBar';
import TokenPurchaseButton from './TokenPurchaseButton';
import Link from 'next/link';

interface TokenStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  shouldRefreshOnOpen?: boolean;
}

export default function TokenStatsModal({ isOpen, onClose, shouldRefreshOnOpen = false }: TokenStatsModalProps) {
  const [stats, setStats] = useState<TokenUsageStats | null>(null);
  const [loading, setLoading] = useState(false);
  const planLimits = usePlanLimits();

  // Refrescar datos cuando se abre el modal si es necesario
  useEffect(() => {
    if (isOpen && shouldRefreshOnOpen) {
      planLimits.refresh();
    }
  }, [isOpen, shouldRefreshOnOpen, planLimits]);

  useEffect(() => {
    if (isOpen && planLimits.userPlan && planLimits.userPlan !== 'free') {
      loadStats();
    } else if (isOpen && planLimits.userPlan === 'free') {
      setStats(null);
      setLoading(false);
    }
  }, [isOpen, planLimits.userPlan]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const currentStats = await getUserTokenStats();
      setStats(currentStats);
    } catch (error) {
      console.error('Error al cargar estadísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const formatTokens = (tokens: number) => tokens.toLocaleString();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FiBarChart className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Estadísticas de Uso de IA</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FiX className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {planLimits.loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Verificando plan...</span>
            </div>
          ) : planLimits.userPlan === 'free' ? (
            <div className="text-center py-12">
              <FiLock className="mx-auto h-16 w-16 text-gray-400 mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Estadísticas de Tokens no disponibles
              </h3>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto mb-6">
                <p className="text-sm text-yellow-800">
                  <strong>Plan Gratuito:</strong> Las estadísticas avanzadas y la compra de tokens están disponibles solo para usuarios con planes de pago.
                </p>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600">
                  Actualiza tu plan para acceder a:
                </p>
                <ul className="text-sm text-gray-700 space-y-2 max-w-sm mx-auto">
                  <li className="flex items-center">
                    <FiBarChart className="w-4 h-4 text-blue-500 mr-2" />
                    Estadísticas detalladas de uso
                  </li>
                  <li className="flex items-center">
                    <FiActivity className="w-4 h-4 text-green-500 mr-2" />
                    Análisis por actividad y modelo
                  </li>
                  <li className="flex items-center">
                    <FiArrowUp className="w-4 h-4 text-purple-500 mr-2" />
                    Seguimiento de progreso
                  </li>
                </ul>
                <div className="pt-4">
                  <Link
                    href="/"
                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <FiArrowUp className="w-4 h-4 mr-2" />
                    Ver Planes Disponibles
                  </Link>
                </div>
              </div>
            </div>
          ) : loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Cargando estadísticas...</span>
            </div>
          ) : stats ? (
            <>
              {/* 1. Barra de Progreso de Tokens */}
              {planLimits.tokenUsage && (
                <div className="mb-6">
                  <TokenProgressBar
                    used={planLimits.tokenUsage.current}
                    limit={planLimits.tokenUsage.limit}
                    percentage={planLimits.tokenUsage.percentage}
                    remaining={planLimits.tokenUsage.remaining}
                  />
                </div>
              )}

              {/* 2. Botón de Compra de Tokens (si aplica) */}
              {planLimits.tokenUsage && planLimits.tokenUsage.percentage >= 80 && planLimits.userPlan && planLimits.userPlan !== 'free' && (
                <div className="mb-6">
                  <TokenPurchaseButton
                    userPlan={planLimits.userPlan as 'usuario' | 'pro'}
                    currentTokens={planLimits.tokenUsage.current}
                    tokenLimit={planLimits.tokenUsage.limit}
                  />
                </div>
              )}

              {/* 3. Estadísticas Detalladas */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <FiActivity className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Total Sesiones</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-900 mt-1">
                    {stats.totalSessions}
                  </p>
                </div>

                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <FiBarChart className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Tokens Consumidos (Histórico)</span>
                  </div>
                  <p className="text-2xl font-bold text-green-900 mt-1">
                    {formatTokens(stats.totalTokens)}
                  </p>
                </div>
              </div>

          {/* Por Actividad */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Uso por Actividad</h3>
            <div className="bg-gray-50 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Actividad</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">Sesiones</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">Tokens</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {Object.entries(stats.byActivity).map(([activity, data]) => {
                    const activityData = data as { tokens: number; cost: number; count: number };
                    return (
                      <tr key={activity} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900">{activity}</td>
                        <td className="px-4 py-3 text-sm text-gray-600 text-right">{activityData.count}</td>
                        <td className="px-4 py-3 text-sm text-gray-600 text-right">{formatTokens(activityData.tokens)}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Por Modelo */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Uso por Modelo</h3>
            <div className="bg-gray-50 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Modelo</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">Sesiones</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-700">Tokens</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {Object.entries(stats.byModel).map(([model, data]) => {
                    const modelData = data as { tokens: number; cost: number; count: number };
                    return (
                      <tr key={model} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900 font-mono">{model}</td>
                        <td className="px-4 py-3 text-sm text-gray-600 text-right">{modelData.count}</td>
                        <td className="px-4 py-3 text-sm text-gray-600 text-right">{formatTokens(modelData.tokens)}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

              {/* Nota */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-800">
                  <strong>Nota:</strong> Los datos de uso de tokens se almacenan en Supabase.
                  Los tokens de entrada y salida se registran automáticamente para cada actividad de IA.
                </p>
              </div>
            </>
          ) : (
            <div className="flex justify-center items-center py-8">
              <p className="text-gray-600">No hay datos de uso disponibles.</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
}
