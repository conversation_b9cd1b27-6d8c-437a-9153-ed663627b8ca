-- Versión 2: Acepta transaction_id nulo para cuentas gratuitas
-- Actualización de la función para manejar cuentas gratuitas sin transacción
CREATE OR REPLACE FUNCTION public.create_user_profile_and_history(
    p_user_id uuid,
    p_transaction_id uuid, -- <PERSON><PERSON> puede ser NULL
    p_profile_data jsonb
)
RETURNS TABLE (
    created_profile_id uuid,
    created_history_id uuid
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_profile_id uuid;
    v_history_id uuid;
    v_new_plan text;
    v_old_plan text;
    v_existing_profile record;
BEGIN
    -- Extraer el plan del JSON
    v_new_plan := p_profile_data->>'subscription_plan';

    -- Verificar si ya existe un perfil para este usuario
    SELECT id, subscription_plan INTO v_existing_profile
    FROM public.user_profiles
    WHERE user_id = p_user_id;

    -- Si ya existe un perfil, actualízalo (upsert).
    -- Si no, inserta uno nuevo.
    INSERT INTO public.user_profiles (
        user_id,
        subscription_plan,
        monthly_token_limit,
        current_month_tokens,
        current_month,
        payment_verified,
        stripe_customer_id,
        stripe_subscription_id,
        last_payment_date,
        auto_renew,
        plan_expires_at,
        plan_features,
        security_flags
    )
    VALUES (
        p_user_id,
        p_profile_data->>'subscription_plan',
        (p_profile_data->>'monthly_token_limit')::int,
        (p_profile_data->>'current_month_tokens')::int,
        (p_profile_data->>'current_month')::date,
        (p_profile_data->>'payment_verified')::boolean,
        p_profile_data->>'stripe_customer_id',
        p_profile_data->>'stripe_subscription_id',
        (p_profile_data->>'last_payment_date')::timestamptz,
        (p_profile_data->>'auto_renew')::boolean,
        (p_profile_data->>'plan_expires_at')::timestamptz,
        p_profile_data->'plan_features',
        p_profile_data->'security_flags'
    )
    ON CONFLICT (user_id) DO UPDATE SET
        subscription_plan = EXCLUDED.subscription_plan,
        monthly_token_limit = EXCLUDED.monthly_token_limit,
        current_month_tokens = EXCLUDED.current_month_tokens,
        current_month = EXCLUDED.current_month,
        payment_verified = EXCLUDED.payment_verified,
        stripe_customer_id = COALESCE(EXCLUDED.stripe_customer_id, user_profiles.stripe_customer_id),
        stripe_subscription_id = COALESCE(EXCLUDED.stripe_subscription_id, user_profiles.stripe_subscription_id),
        last_payment_date = EXCLUDED.last_payment_date,
        auto_renew = EXCLUDED.auto_renew,
        plan_expires_at = EXCLUDED.plan_expires_at,
        plan_features = EXCLUDED.plan_features,
        security_flags = user_profiles.security_flags || EXCLUDED.security_flags,
        updated_at = now()
    RETURNING id INTO v_profile_id;

    -- Si no se encontró un perfil existente, v_old_plan es nulo.
    v_old_plan := v_existing_profile.subscription_plan;

    -- Registrar el cambio en el historial de planes
    -- Ahora transaction_id puede ser NULL para cuentas gratuitas
    INSERT INTO public.user_plan_history (
        user_id,
        old_plan,
        new_plan,
        changed_by,
        reason,
        transaction_id -- Esta columna ahora acepta NULL
    )
    VALUES (
        p_user_id,
        v_old_plan,
        v_new_plan,
        'system',
        -- Modificamos la razón para ser más clara según si hay transacción o no
        CASE
            WHEN p_transaction_id IS NOT NULL THEN
                CASE
                    WHEN v_old_plan IS NULL THEN 'Initial plan assignment via payment'
                    ELSE 'Plan update via payment for existing user'
                END
            ELSE 'Free account registration'
        END,
        p_transaction_id -- Puede ser NULL
    )
    RETURNING id INTO v_history_id;

    -- Devolver los IDs de los registros creados
    RETURN QUERY SELECT v_profile_id, v_history_id;

END;
$$;