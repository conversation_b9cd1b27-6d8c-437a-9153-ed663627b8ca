# 🚀 OposicionesIA - Instalación Rápida

## 📦 Contenido del ZIP

Este archivo ZIP contiene la aplicación **OposicionesIA** completamente reorganizada por funcionalidades, lista para ejecutar en cualquier PC.

### 📁 Estructura Incluida:
```
├── src/                     # Código fuente completo
│   ├── features/            # Nueva estructura por funcionalidades
│   │   ├── auth/           # Autenticación
│   │   ├── documents/      # Gestión de documentos
│   │   ├── conversations/  # Q&A y conversaciones
│   │   ├── flashcards/     # Flashcards con repetición espaciada
│   │   ├── tests/          # Tests y exámenes
│   │   ├── mindmaps/       # Mapas mentales
│   │   ├── dashboard/      # Dashboard y estadísticas
│   │   └── shared/         # Componentes compartidos
│   ├── app/                # App Router de Next.js
│   ├── lib/                # Librerías y servicios
│   ├── contexts/           # Contextos de React
│   └── utils/              # Utilidades
├── package.json            # Dependencias del proyecto
├── package-lock.json       # Versiones exactas de dependencias
├── next.config.js          # Configuración de Next.js
├── tailwind.config.js      # Configuración de Tailwind CSS (ACTUALIZADA)
├── tsconfig.json           # Configuración de TypeScript
└── INSTALACION.md          # Guía completa de instalación
```

## ⚡ Instalación Rápida (5 minutos)

### 1. **Requisitos Previos**
- **Node.js 18+** - [Descargar aquí](https://nodejs.org/)
- **Cuenta de Supabase** - [Crear gratis](https://supabase.com/)
- **API Key de Google Gemini** - [Obtener aquí](https://makersuite.google.com/app/apikey)

### 2. **Extraer y Configurar**
```bash
# 1. Extraer el ZIP en la ubicación deseada
# 2. Abrir terminal en la carpeta extraída
cd ruta/a/la/carpeta/extraida

# 3. Instalar dependencias
npm install
```

### 3. **Configurar Variables de Entorno**
Crear archivo `.env.local` en la raíz del proyecto:

```env
# Configuración de Supabase
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url_aqui
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key_aqui

# API Key de Google Gemini
GEMINI_API_KEY=tu_gemini_api_key_aqui
```

### 4. **Configurar Base de Datos**
Ejecutar el script SQL completo que está en `INSTALACION.md` en tu proyecto de Supabase.

### 5. **Ejecutar la Aplicación**
```bash
# Modo desarrollo
npm run dev

# La aplicación estará disponible en: http://localhost:3000
```

## ✨ Características de la Nueva Estructura

### 🎯 **Organización por Funcionalidades**
- Cada feature tiene sus componentes, servicios y hooks agrupados
- Fácil localización y mantenimiento del código
- Estructura escalable para nuevas funcionalidades

### 🔧 **Mejoras Implementadas**
- ✅ **Tailwind CSS actualizado** para la nueva estructura
- ✅ **Todas las importaciones corregidas**
- ✅ **Sin errores de compilación**
- ✅ **Funcionalidad completa preservada**

### 📊 **Funcionalidades Incluidas**
- 🔐 **Autenticación** con auto-logout por inactividad
- 📄 **Gestión de documentos** (PDF/TXT)
- 💬 **Q&A con IA** (OpenAI)
- 🃏 **Flashcards** con repetición espaciada
- 🧠 **Mapas mentales** interactivos
- 📝 **Tests** personalizados
- 📊 **Estadísticas** detalladas
- 🔄 **Tareas en segundo plano**

## 🛠️ Solución de Problemas

### Error de Dependencias
```bash
# Limpiar cache y reinstalar
rm -rf node_modules package-lock.json
npm install
```

### Error de Compilación
```bash
# Limpiar cache de Next.js
rm -rf .next
npm run dev
```

### Error de Estilos
- Verificar que `tailwind.config.js` incluya `'./src/features/**/*.{js,ts,jsx,tsx,mdx}'`

## 📞 Soporte

Para problemas técnicos:
1. Revisar `INSTALACION.md` para guía completa
2. Verificar configuración de variables de entorno
3. Comprobar que Supabase y Gemini estén configurados correctamente

---

**¡La aplicación está lista para usar con la nueva estructura organizacional!** 🎉
